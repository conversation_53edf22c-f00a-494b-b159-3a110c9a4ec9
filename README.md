# Syp and Save - React Native App

A comprehensive deals mobile application built with React Native, featuring user authentication, location-based deals, business management, and social login integration.

## Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Environment Variables](#environment-variables)
- [Dependencies](#dependencies)
- [Platform Setup](#platform-setup)
- [Running the App](#running-the-app)
- [Development Commands](#development-commands)
- [Architecture](#architecture)
- [Troubleshooting](#troubleshooting)

## Overview

**App Name:** SYPnSAVE  
**Version:** 1.1 (Build 13)  
**React Native Version:** 0.70.5  
**Supported Platforms:** iOS 14.0+, Android API 24+

### Key Features
- 🔐 Firebase Authentication with social login (Google, Facebook, Apple)
- 📍 Location-based deals and geofencing
- 🏪 Business and customer user roles
- 💳 Deal creation and template management
- 🗺️ Google Maps integration
- 📱 Push notifications
- 🔗 Deep linking support
- 📊 Analytics and crash reporting

## Prerequisites

### System Requirements
- **Node.js:** 16.x (specified in `.node-version`)
- **Ruby:** 2.7.6 (for iOS development)
- **Yarn:** Latest version
- **Git:** Latest version

### Development Environment
- **macOS:** Required for iOS development
- **Xcode:** 14.0+ (for iOS)
- **Android Studio:** Latest version (for Android)
- **JDK:** 11 or higher

### Tools Installation
```bash
# Install Node.js version manager
brew install nvm
nvm install 16
nvm use 16

# Install Yarn
npm install -g yarn

# Install Ruby version manager
brew install rbenv
rbenv install 2.7.6
rbenv global 2.7.6

# Install CocoaPods (for iOS)
sudo gem install cocoapods

# Install React Native CLI
npm install -g react-native-cli
```

## Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd syp-and-save-react-native
```

### 2. Install Dependencies
```bash
# Install npm dependencies
yarn install

# This will automatically run:
# - react-native setup-ios-permissions
# - pod-install (for iOS)
# - patch-package (for any necessary patches)
# - husky install (for git hooks)
```

### 3. Environment Setup
```bash
# Create environment file
cp .env.example .env
# Edit .env file with your configuration (see Environment Variables section)
```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
# App Configuration
APP_NAME=SYPnSAVE
APP_VERSION=1.1

# Contact Information
APP_CONTACT_MAIL=<EMAIL>
APP_CONTACT_PHONE=+1234567890
APP_CONTACT_COORDINATES=40.7128,-74.0060
APP_CONTACT_ADDRESS=Your Business Address
APP_AGE_LIMIT=21

# API Configuration
APP_API_URL=https://api.sypnsave.com
APP_GOOGLE_CLOUD_STORAGE=your-gcs-bucket-url
APP_GOOGLE_MAP_API_KEY=your-google-maps-api-key

# Legal Pages
APP_TERMS_AND_CONDITIONS_URL=https://sypnsave.com/terms
APP_PRIVACY_POLICY_URL=https://sypnsave.com/privacy

# Deep Linking
DEAL_DETAILS_DEEP_LINK=/deal-details
DEEP_LINK_BASE_URL=https://sypnsave.com
```

## Dependencies

### Core Dependencies

| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `react` | 18.1.0 | React framework | [React Docs](https://reactjs.org) |
| `react-native` | 0.70.5 | React Native framework | [RN Docs](https://reactnative.dev) |
| `hermes-engine` | ^0.11.0 | JavaScript engine | [Hermes Docs](https://hermesengine.dev) |

### State Management & Data
| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `@reduxjs/toolkit` | ^1.9.0 | State management | [Redux Toolkit](https://redux-toolkit.js.org) |
| `react-redux` | ^8.0.5 | React Redux bindings | [React Redux](https://react-redux.js.org) |
| `redux-persist` | ^6.0.0 | State persistence | [Redux Persist](https://github.com/rt2zz/redux-persist) |
| `@react-native-async-storage/async-storage` | ^1.17.11 | Local storage | [AsyncStorage](https://github.com/react-native-async-storage/async-storage) |

### Navigation
| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `@react-navigation/native` | ^6.0.13 | Navigation core | [React Navigation](https://reactnavigation.org) |
| `@react-navigation/stack` | ^6.3.4 | Stack navigator | [Stack Navigator](https://reactnavigation.org/docs/stack-navigator) |
| `@react-navigation/bottom-tabs` | ^6.4.0 | Tab navigator | [Bottom Tabs](https://reactnavigation.org/docs/bottom-tab-navigator) |
| `react-native-screens` | ^3.18.2 | Native screens | [RN Screens](https://github.com/software-mansion/react-native-screens) |
| `react-native-safe-area-context` | ^4.4.1 | Safe area handling | [Safe Area](https://github.com/th3rdwave/react-native-safe-area-context) |

### UI Components & Styling
| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `native-base` | ^3.4.25 | UI component library | [NativeBase](https://nativebase.io) |
| `react-native-vector-icons` | ^9.2.0 | Icon components | [Vector Icons](https://github.com/oblador/react-native-vector-icons) |
| `react-native-svg` | ^13.6.0 | SVG support | [RN SVG](https://github.com/react-native-svg/react-native-svg) |
| `react-native-reanimated` | ^2.13.0 | Animations | [Reanimated](https://docs.swmansion.com/react-native-reanimated) |
| `react-native-gesture-handler` | ^2.8.0 | Gesture handling | [Gesture Handler](https://docs.swmansion.com/react-native-gesture-handler) |

### Authentication & Firebase
| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `@react-native-firebase/app` | ^16.5.0 | Firebase core | [RN Firebase](https://rnfirebase.io) |
| `@react-native-firebase/auth` | ^16.5.0 | Firebase authentication | [Firebase Auth](https://rnfirebase.io/auth/usage) |
| `@react-native-google-signin/google-signin` | ^8.2.1 | Google Sign-In | [Google Sign-In](https://github.com/react-native-google-signin/google-signin) |
| `react-native-fbsdk-next` | ^11.1.0 | Facebook SDK | [Facebook SDK](https://github.com/thebergamo/react-native-fbsdk-next) |
| `@invertase/react-native-apple-authentication` | ^2.4.0 | Apple Sign-In | [Apple Auth](https://github.com/invertase/react-native-apple-authentication) |

### Location & Maps
| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `react-native-maps` | ^1.3.2 | Google Maps | [RN Maps](https://github.com/react-native-maps/react-native-maps) |
| `@react-native-community/geolocation` | ^3.0.4 | Location services | [Geolocation](https://github.com/react-native-geolocation/react-native-geolocation) |
| `react-native-permissions` | ^3.8.0 | Permission handling | [RN Permissions](https://github.com/zoontek/react-native-permissions) |

### Form Handling & Input
| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `react-hook-form` | ^7.39.5 | Form management | [React Hook Form](https://react-hook-form.com) |
| `@react-native-community/checkbox` | ^0.5.17 | Checkbox component | [RN Checkbox](https://github.com/react-native-checkbox/react-native-checkbox) |
| `@react-native-picker/picker` | ^2.6.1 | Picker component | [RN Picker](https://github.com/react-native-picker/picker) |
| `@react-native-community/datetimepicker` | ^7.6.1 | Date/time picker | [DateTime Picker](https://github.com/react-native-datetimepicker/datetimepicker) |
| `react-native-text-input-mask` | ^3.2.0 | Input masking | [Text Input Mask](https://github.com/react-native-text-input-mask/react-native-text-input-mask) |

### Media & File Handling
| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `react-native-image-picker` | ^4.10.1 | Image selection | [Image Picker](https://github.com/react-native-image-picker/react-native-image-picker) |
| `react-native-splash-screen` | ^3.3.0 | Splash screen | [Splash Screen](https://github.com/crazycodeboy/react-native-splash-screen) |

### Utilities & Data
| Library | Version | Purpose | Documentation |
|---------|---------|---------|---------------|
| `axios` | ^1.2.1 | HTTP client | [Axios](https://axios-http.com) |
| `dayjs` | ^1.11.7 | Date manipulation | [Day.js](https://day.js.org) |
| `lodash` | ^4.17.21 | Utility functions | [Lodash](https://lodash.com) |
| `uuid` | ^9.0.0 | UUID generation | [UUID](https://github.com/uuidjs/uuid) |
| `react-native-device-info` | ^10.3.0 | Device information | [Device Info](https://github.com/react-native-device-info/react-native-device-info) |

## Platform Setup

### iOS Setup

#### 1. iOS Requirements
- Xcode 14.0+ installed
- iOS 14.0+ deployment target
- Apple Developer Account (for device testing/distribution)

#### 2. Configure iOS Permissions
The app requires the following iOS permissions (already configured in `Info.plist`):
- App Tracking Transparency
- Camera access
- Location services (when in use/always)
- Media library access
- Push notifications
- Photo library access
- StoreKit (for in-app purchases)

#### 3. Firebase Configuration
```bash
# Add GoogleService-Info.plist to ios/ folder
# Get this file from Firebase Console > Project Settings > iOS apps
```

#### 4. Install Pods
```bash
cd ios && pod install && cd ..
```

### Android Setup

#### 1. Android Requirements
- Android Studio with Android SDK
- Android API 24+ (minimum)
- Google Play Services

#### 2. Configure Android Permissions
The app requests the following Android permissions (configured in `AndroidManifest.xml`):
- Internet access
- Camera
- Location (fine and coarse)
- External storage read/write
- System alert window

#### 3. Firebase Configuration
```bash
# Add google-services.json to android/app/ folder
# Get this file from Firebase Console > Project Settings > Android apps
```

#### 4. API Keys Configuration
Update `android/app/src/main/res/values/strings.xml`:
```xml
<string name="google_map_api_key">YOUR_GOOGLE_MAPS_API_KEY</string>
<string name="facebook_app_id">YOUR_FACEBOOK_APP_ID</string>
<string name="facebook_client_token">YOUR_FACEBOOK_CLIENT_TOKEN</string>
```

## Running the App

### Start Metro Bundler
```bash
yarn start
```

### Run on iOS
```bash
# Default simulator (iPhone 16 Pro)
yarn ios

# Specific simulator
npx react-native run-ios --simulator "iPhone 15 Pro"

# Physical device
npx react-native run-ios --device "Your iPhone"
```

### Run on Android
```bash
# Default emulator/device
yarn android

# Specific device
adb devices  # List available devices
npx react-native run-android --deviceId=DEVICE_ID
```

## Development Commands

### Code Quality
```bash
# Lint code
yarn lint

# Format code
yarn format

# Run tests
yarn test

# Run specific test
jest __tests__/App-test.js
```

### Build & Clean
```bash
# Clean Metro cache
yarn start --reset-cache

# Clean iOS build
cd ios && xcodebuild clean && cd ..

# Clean Android build
cd android && ./gradlew clean && cd ..

# Reinstall dependencies
rm -rf node_modules && yarn install
```

### iOS Specific
```bash
# Update/install pods
cd ios && pod install && cd ..

# Update pod repos
cd ios && pod repo update && cd ..
```

## Architecture

### Project Structure
```
src/
├── components/          # Reusable UI components
├── navigation/          # Navigation configuration
├── screens/            # Screen components with Redux slices
├── services/           # API client, auth, permissions
├── utils/             # Constants and utilities
├── assets/            # Images, fonts, static resources
├── store.js           # Redux store configuration
└── theme.js           # NativeBase theme configuration
```

### Key Architecture Patterns
- **State Management:** Redux Toolkit with Redux Persist
- **Navigation:** React Navigation v6 with deep linking
- **API Layer:** Centralized Axios client with Firebase auth
- **Component Pattern:** Screen-specific components with dedicated Redux slices
- **Form Handling:** React Hook Form for all form interactions
- **Styling:** NativeBase components with custom theme

### Navigation Flow
```
Root Navigation
├── Age Verification (if not verified)
├── Auth Navigation (if not authenticated)
├── Location Permission (if needed for customers)
└── Main App (Bottom Tab Navigation)
    ├── Deals
    ├── Favorites
    ├── My Deals (Business users)
    ├── Templates (Business users)
    └── Profile
```

## Troubleshooting

### Common Issues

#### iOS Build Issues
```bash
# Pod installation fails
cd ios && rm -rf Pods Podfile.lock && pod install

# Xcode build errors
cd ios && xcodebuild clean && cd ..
yarn ios

# Metro bundler issues
yarn start --reset-cache
```

#### Android Build Issues
```bash
# Gradle build fails
cd android && ./gradlew clean && cd ..
yarn android

# ADB connection issues
adb kill-server && adb start-server
adb devices
```

#### Common Development Issues

**Metro bundler port in use:**
```bash
npx react-native start --port 8082
```

**Node modules cache issues:**
```bash
rm -rf node_modules
yarn install
yarn start --reset-cache
```

**iOS simulator not found:**
```bash
xcrun simctl list devices
npx react-native run-ios --simulator "iPhone 15 Pro"
```

### Environment Setup Issues

**Node version mismatch:**
```bash
nvm use 16
```

**Ruby version issues (iOS):**
```bash
rbenv install 2.7.6
rbenv local 2.7.6
gem install cocoapods
```

**Android SDK issues:**
```bash
# Add to ~/.zshrc or ~/.bash_profile
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

### Firebase Configuration

**iOS GoogleService-Info.plist missing:**
1. Download from Firebase Console
2. Add to `ios/` folder (not inside Xcode groups)
3. Clean and rebuild

**Android google-services.json missing:**
1. Download from Firebase Console
2. Add to `android/app/` folder
3. Clean and rebuild

## Support & Resources

- **React Native:** [Documentation](https://reactnative.dev/docs/getting-started)
- **Firebase:** [React Native Firebase](https://rnfirebase.io/)
- **NativeBase:** [Component Library](https://nativebase.io/)
- **React Navigation:** [Navigation Library](https://reactnavigation.org/)
- **Redux Toolkit:** [State Management](https://redux-toolkit.js.org/)

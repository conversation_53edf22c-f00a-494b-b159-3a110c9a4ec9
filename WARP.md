# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Development Commands

### Environment Setup
```bash
# Install dependencies
yarn install

# Setup iOS permissions and install pods (automatically run after install)
yarn postinstall

# Create environment file from example
cp .env.example .env
```

### Running the App
```bash
# Start Metro bundler
yarn start

# Run iOS (default simulator: iPhone 16 Pro)
yarn ios

# Run Android
yarn android
```

### Development Tools
```bash
# Lint code
yarn lint

# Format code with Prettier
yarn format

# Run tests
yarn test

# Run specific test file
jest __tests__/App-test.js
```

### iOS Development
```bash
# Install/update pods
cd ios && pod install

# Clean build
cd ios && xcodebuild clean

# Run on specific simulator
npx react-native run-ios --simulator "iPhone 15 Pro"
```

### Android Development
```bash
# Clean build
cd android && ./gradlew clean

# List available devices
adb devices

# Run on specific device
npx react-native run-android --deviceId=DEVICE_ID
```

## Architecture Overview

### App Structure
- **Entry Point**: `App.js` → `src/index.js` (Root component)
- **State Management**: Redux Toolkit with Redux Persist for offline storage
- **Navigation**: React Navigation with deep linking support
- **UI Framework**: NativeBase for consistent UI components
- **Authentication**: Firebase Auth with social login support

### Key Architecture Patterns

#### Navigation Flow
```
Navigation (src/navigation/index.js)
├── AgeVerificationNavigation (if not verified)
├── AuthNavigation (if not authenticated)
├── LocationNavigation (if location permission needed)
└── BottomTabNavigation (main app)
    ├── DealsNavigation
    ├── FavoriteNavigation
    ├── MyDealsNavigation
    ├── TemplatesNavigation
    └── ProfileNavigation
```

#### Redux Store Structure
State is organized by feature screens with dedicated slices:
- Authentication: `userSignInSlice`, `userSignUpSlice`, `forgotPasswordSlice`
- Core Features: `dealsSlice`, `dealDetailSlice`, `categoryDealSlice`, `favoriteDealSlice`
- User Management: `editProfileSlice`, `userProfileSlice`
- Business Features: `createDealSlice`, `myDealsSlice`, `myTemplatesSlice`

#### API Client Pattern
- Centralized API client (`src/services/apiClient.js`) with automatic authentication
- Firebase token-based auth with automatic refresh
- Global error handling with user-friendly toast messages
- Request correlation IDs for debugging

### Key Configuration Files

#### Environment Variables (.env)
Required variables for different environments:
- API endpoints and configuration
- Firebase/social login credentials  
- Google Maps API key
- Deep linking URLs
- App metadata and contact info

#### Code Quality Tools
- **ESLint**: Custom rules for React Native with Prettier integration
- **Prettier**: Tabs, single quotes, trailing commas, 80 char width
- **Husky**: Git hooks for pre-commit linting
- **Metro**: Standard React Native bundler configuration

### Deep Linking
The app supports deep linking for product details:
- Base URL configured via `DEEP_LINK_BASE_URL` env var
- Routes to `DealsNavigation > DealDetail` screen
- Handled through React Navigation linking configuration

### Permissions & Platform Features
- Location services for customer role users
- Camera and photo library access
- Push notifications
- iOS-specific: App tracking transparency, StoreKit
- Comprehensive permission handling via `src/services/RNPermission.js`

### Component Organization
- **Reusable Components**: `src/components/` (DealCard, ProductCard, sliders, etc.)
- **Screen-Specific**: Each screen has its own directory with component + slice
- **Services**: API, authentication, permissions, maps integration
- **Assets**: Images and other static resources

### Data Persistence
- Redux Persist whitelist includes: user auth, favorites, age verification, addresses
- AsyncStorage for React Native
- Automatic state cleanup on user sign out (except age verification)

### Testing Setup
- Jest with React Native preset
- Basic smoke test for main App component
- Test files in `__tests__/` directory

## Development Notes

### Required Dependencies
This app uses several native modules requiring manual setup:
- Firebase (iOS: GoogleService-Info.plist, Android: google-services.json)
- Google Sign-In configuration
- Maps integration requires API key setup
- Social login (Facebook, Apple) requires additional platform configuration

### Build Requirements
- Node.js version specified in `.node-version`
- Ruby version for iOS toolchain in `.ruby-version`  
- Xcode for iOS development
- Android Studio/SDK for Android development

### Common Development Patterns
- All screens follow Redux slice pattern with async thunks
- Form handling with react-hook-form
- Consistent error handling through toast notifications
- Image handling with react-native-image-picker
- Date/time inputs with platform-specific pickers

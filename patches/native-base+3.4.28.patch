diff --git a/node_modules/native-base/src/core/NativeBaseProvider.tsx b/node_modules/native-base/src/core/NativeBaseProvider.tsx
index 43b4bd1..dd1329c 100644
--- a/node_modules/native-base/src/core/NativeBaseProvider.tsx
+++ b/node_modules/native-base/src/core/NativeBaseProvider.tsx
@@ -4,7 +4,6 @@ import {
   Metrics,
   initialWindowMetrics as defaultInitialWindowMetrics,
 } from 'react-native-safe-area-context';
-import { SSRProvider } from '@react-native-aria/utils';
 import { theme as defaultTheme, ITheme } from './../theme';
 import type { IColorModeProviderProps } from './color-mode';
 import HybridProvider from './hybrid-overlay/HybridProvider';
@@ -94,7 +93,7 @@ const NativeBaseProvider = (props: NativeBaseProviderProps) => {
             <OverlayProvider isSSR>
               <ToastProvider>
                 <InitializeToastRef />
-                <SSRProvider>{children}</SSRProvider>
+                {children}
               </ToastProvider>
             </OverlayProvider>
           </HybridProvider>

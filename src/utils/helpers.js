import {APP_GOOGLE_CLOUD_STORAGE} from './constants';

export const toInitials = str =>
	str
		// strip off capital letters (example: "First Last" ==> "FL")
		.replace(/[^A-Z]/g, '')
		// append the second character of the first word to the end of this new string (example: "FL" ==> "FLI")
		.concat(str.charAt(1).toUpperCase())
		// limit this new string to 2 characters (example: "FLI" ==> "FL")
		.substring(0, 2);

export const toUri = path => (path ? APP_GOOGLE_CLOUD_STORAGE + path : null);

export const toCamelCase = str => {
	return str
		.replace(/(?:^\w|[A-Z]|\b\w)/g, function (word, index) {
			return index === 0 ? word.toLowerCase() : word.toUpperCase();
		})
		.replace(/\s+/g, '');
};

export function capitalizeName(name) {
	return name.replace(/\b\w/g, (char) => char.toUpperCase());
}
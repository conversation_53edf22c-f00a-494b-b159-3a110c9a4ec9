const constants = {
	API_ROUTES: {
		USER_DELETE: '/user/delete',
		CHECK_USER: '/user/check',
		SAVE_TEMPLATE: '/template',
		UPDATE_TEMPLATE: '/template',
		DELETE_TEMPLATE: '/template',
		GET_TEMPLATE: '/template',
		EDIT_TEMPLATE: '/template',
		UPDATE_DEAL: '/business/deal',
		MY_DEAL_DETAIL: '/business/deal',
		CREATE_DEAL: '/business/deal',
		GET_CATEGORIES: '/category',
		GET_BANNERS: '/banner',
		VERIFY_EMAIL: '/user/verify-email',
		GET_TOP_DEAL_BY_TYPE: '/deals/top/type',
		GET_DEAL_BY_TYPE: '/deals/type',
		GET_DEAL_BY_CATEGORY: '/deals/category',
		SEARCH_DEALS: '/deals',
		GET_DEAL: '/deals',
		GET_USER: '/user',
		ADD_USER: '/user',
		UPDATE_USER: '/user',
		GET_FAVORITE_DEAL_IDS: '/favorite/deal-ids',
		CHANGE_PASSWORD: '/user/change-password',
		UPLOAD_PROFILE: '/user/upload-profile',
		GET_DEAL_RELATED_STORES: '/product',
		GET_STORE_RELATED_DEALS: '/user',
		GET_STORE: '/user/store',
		GET_DEAL_OFFERS: '/deals/offers',
		GET_PRICE_RANGE: '/deals/price-range',
		GET_PRODUCT_SIZES: '/deals/product-sizes',
		GET_BRANDS: '/brand',
		GET_DISCOUNTS: '/deals/discount-percents',
		ADD_FAVORITE: '/favorite',
		REMOVE_FAVORITE: '/favorite',
		GET_FAVORITE: '/favorite',
		GET_NOTIFICATIONS: '/notifications',
		ADD_UPDATE_DEVICE: '/user/add-update-device',
		GET_BUSINESS: '/business',
		MY_DEALS: '/business',
		DELETE_DEAL: '/deals',
		MY_TEMPLATES: '/template',
		UPDATE_BUSINESS: '/business',
		GET_SUBCATEGORIES: '/subcategory',
		REQUEST_ADD_PRODUCT: '/product/add-request',
		GET_PRODUCTS: '/product',
		DEEP_LINK_PRODUCT_DETAILS: '/deal-detail/:id'
	},
	COLLECTIONS: {
		USER: 'user',
		CATEGORY: 'category',
		BANNER: 'banner',
		PRODUCTS: 'products',
		DEALS: 'deals',
		FAVORITE: 'favorite',
		BRAND: 'brand',
		SIZE: 'size',
		DEAL_TYPE: 'deal_type',
		NOTIFICATIONS: 'notifications',
	},
	COLORS: {
		ORANGE: '#F05023',
		LIGHT_ORANGE: '#FFE5CD',
		GREEN: '#10C600',
		WHITE: '#FFFFFF',
		RED: '#FF0707',
		BLACK: '#0E0E0E',
		DARK1: '#171717',
		GREY1: '#A0A0A5',
		GREY2: '#C7C7CC',
		GREY3: '#E5E5EA',
		GREY4: '#AEAEB2',
		GREY5: '#7A7A7A',
		GREY6: '#D9D9D9',
	},
	VALIDATION_MESSAGES: {
		NAME_MIN_LENGTH: 'Name must be at least 2 characters.',
		ZIP_CODE_MAX_LENGTH: 'Zip code max length should be six digits.',
		VALID_BUSINESS_NAME: 'Please enter valid business name',
		ZIP_CODE_MINLENGTH: 'Zip code min length should be five digits',
		VALID_DEAL_ENDS_ON: 'Deal ends on must be greater than current datetime.',
		PRODUCT_REQUIRED: 'Product is required!',
		MATCH_DISCOUNT_PRICE:
			'Discount price must be less or equal to Product price',
		VALID_PRODUCT_PRICE: 'Product price must be number!',
		PRODUCT_PRICE: 'Product price is required!',
		ABV_MIN: 'ABV must be greater or equal to 10%',
		ABV_MAX: 'ABV must be less or equal to 100%',
		VALID_ABV: 'Please enter valid ABV!',
		DISCOUNT_PERCENT_MIN: 'Discount percent must be greater or equal to 10%',
		DISCOUNT_PERCENT_MAX: 'Discount percent must be less or equal to 100%',
		VALID_DISCOUNT_PERCENT: 'Discount percent must be number!',
		VALID_DISCOUNT_PRICE: 'Discount price must be number!',
		DISCOUNT_PRICE_REQUIRED: 'Discount price is required!',
		QUANTITY_REQUIRED: 'Quantity is required!',
		DISCOUNT_PERCENT_REQUIRED: 'Discount percent is required!',
		DISCOUNT_TYPE_REQUIRED: 'Discount type is required!',
		DEAL_TYPE_REQUIRED: 'Deal type is required!',
		ZIP_CODE_REQUIRED: 'Zip code is required!',
		CITY_REQUIRED: 'City is required!',
		STATE_REQUIRED: 'State is required!',
		SERVICE_OPTIONS_REQUIRED: 'Service Options is required!',
		BUSINESS_PHONE_REQUIRED: 'Business phone is required!',
		STREET_ADDRESS_REQUIRED: 'Street address is required!',
		VALID_STREET_ADDRESS: 'Please enter valid street address',
		STORE_DESCRIPTION_REQUIRED: 'Store description is required!',
		STORE_TIMING_REQUIRED: 'Store timing is required!',
		BUSINESS_ADDRESS_REQUIRED: 'Business address is required!',
		BUSINESS_EMAIL_REQUIRED: 'Business email ID is required!',
		BUSINESS_PORTAL_URL_REQUIRED: 'Business portal URL is required!',
		VALID_BUSINESS_PORTAL_URL:
			'Invalid business portal URL please enter the valid URL!',
		BUSINESS_NAME_REQUIRED: 'Business name is required!',
		PRODUCT_DESCRIPTION_REQUIRED: 'Product description is required!',
		EMAIL_REQUIRED: 'Email ID is required!',
		PRODUCT_NAME_REQUIRED: 'Product name is required!',
		VALID_EMAIL: 'Invalid Email ID!',
		PASSWORD_REQUIRED: 'Password is required!',
		NEW_PASSWORD_REQUIRED: 'New Password is required!',
		CURRENT_PASSWORD_REQUIRED: 'Current Password is required!',
		PASSWORD_LENGTH: 'Password must be greater than or equal to 6 characters!',
		CONFIRM_PASSWORD_REQUIRED: 'Confirm Password is required!',
		CONFIRM_PASSWORD_NOT_MATCH: 'Your passwords do not match!',
		NAME_REQUIRED: 'Name is required!',
		PHONE_REQUIRED: 'Phone is required!',
		ADDRESS_REQUIRED: 'Address is required!',
		VALID_PHONE: 'Invalid Phone!',
		BRAND_REQUIRED: 'Brand required!',
		CATEGORY_REQUIRED: 'Category required!',
		SUBCATEGORY_REQUIRED: 'Subcategory required!',
		SIZE_REQUIRED: 'Product size required!',
		MINIMUM_SIZE: 'Product size must be greater than or equal to 100ml',
		INVALID_SIZE: 'Invalid product size',
		PRODUCER_REQUIRED: 'Producer required!',
		SUPPLIER_REQUIRED: 'Supplier required!',
		UPC_REQUIRED: 'UPC required!',
		VALID_UPC: 'UPC must be number!',
		TASTE_REQUIRED: 'Taste required!',
		ABV_REQUIRED: 'ABV required!',
		REGION_REQUIRED: 'Region required!',
		COUNTRY_REQUIRED: 'Country required!',
		PHOTO_REQUIRED: 'Photo required!',
		WEAK_PASSWORD:
			'Password must contain one number, one letter and one special character!',
		CAMERA_PERMISSION: 'App Camera Permission',
		CAMERA_PERMISSION_MESSAGE: 'App needs access to your camera',
		ASK_ME_LATER: 'Ask Me Later',
		CANCEL: 'Cancel',
		OK: 'OK',
		ALL_BUSINESS_DAYS_TIME_REQUIRED: 'All business days and time is required!',
		BUSINESS_LICENSE_REQUIRED: 'Business License is required',
	},
	LINK: {
		FORGOT_PASSWORD: 'Forgot password?',
		CREATE_AN_ACCOUNT: 'Create an Account',
		SIGN_IN: 'Sign In',
		RESEND: 'Resend',
		READ_MORE: 'Read More',
		READ_LESS: 'Read Less',
		SAVE_AS_TEMPLATE: 'Save as Template',
		TERMS_AND_CONDITIONS: 'Terms and Conditions',
		PRIVACY_POLICY: 'Privacy Policy',
		SIGN_IN_AS_GUEST: 'Sign In as Guest',
	},
	BUTTON: {
		SIGN_IN_AS_GUEST: 'Sign In As Guest',
		SIGN_IN: 'Sign In',
		SIGN_UP: 'Create an Account',
		VIEW_MORE: 'View More',
		SAVE: 'Save',
		SAVE_ADDRESS: 'Save Address',
		CONFIRM_LOCATION: 'Confirm Location',
		VERIFY: 'Verify',
		APPLY_FILTERS: 'Apply Filters',
		SEND: 'Send',
		SAVE_DEAL: 'Save Deal',
		EDIT_TEMPLATE: 'Edit Template',
		DELETE_TEMPLATE: 'Delete Template',
		EDIT_DEAL: 'Edit Deal',
		COPY_DEAL: 'Copy Deal',
		SAVE_TEMPLATE: 'Save Template',
		LOAD_MORE: 'Load More',
		USE_TEMPLATE: 'Use Template',
		ENTER_LOCATION_MANUALLY: 'Enter Location Manually',
		ENABLE_DEVICE_LOCATION: 'Enable Device Location',
		CANCEL: 'Cancel',
		DELETE: 'Delete',
		LOGOUT: 'Logout',
		REQUEST_A_PRODUCT: 'Request add Product',
	},
	LABEL: {
		DISCOUNTED_PRICE: 'Discounted Price',
		PACK_OF: 'Pack Of',
		QUANTITY: 'Quantity',
		DOB: 'Date Of Birth',
		LOW_STOCK: 'Low Stock',
		PRODUCT: 'Product',
		PRODUCT_PRICE: 'Product Price',
		DEAL_TYPE: 'Deal Type',
		DISCOUNT_PRICE: 'Discount Price',
		PRODUCT_QUANTITY: 'Product Quantity',
		DISCOUNT_PERCENT: 'Discount Percent',
		DISCOUNT_TYPE: 'Discount Type',
		STATE: 'State',
		ZIP_CODE: 'Zip Code',
		CITY: 'City',
		PRODUCT_DESCRIPTION: 'Product Description',
		PRODUCT_PHOTO: 'Product Photo',
		YOUR_EMAIL_ID: 'Your Email ID',
		PRODUCT_NAME: 'Product Name',
		PASSWORD: 'Password',
		PHONE: 'Phone',
		CONFIRM_PASSWORD: 'Confirm Password',
		CURRENT_PASSWORD: 'Current Password',
		NEW_PASSWORD: 'New Password',
		CONFIRM_NEW_PASSWORD: 'Confirm New Password',
		NAME: 'Name',
		ADDRESS: 'Address',
		UNITED_STATES: 'United States',
		PRICE: 'Price',
		VOLUME: 'Volume',
		SELECT_PRICE_RANGE: 'Selected price range',
		ENTER_EMAIL_ADDRESS: 'Enter Email Address',
		WHAT_CAN_WE_HELP_YOU_FIND: 'What can we help you find?',
		TYPE: 'Type',
		VALUE: 'Value',
		VALID_QUANTITY: 'Valid Quantity',
		DISCOUNT: 'Discount',
		DEAL_ENDS_ON: 'Deal Ends On',
		SEARCH_FOR_DEALS: 'Search for Deals',
		SEARCH_FOR_PRODUCTS: 'Search for Products',
		SIZE: 'Size',
		CATEGORY: 'Category',
		SUBCATEGORY: 'Subcategory',
		PRODUCT_SIZE: 'Product Size',
		COUNTRY: 'Country',
		REGION: 'Region',
		ABV: 'ABV',
		TEST: 'Test',
		DESCRIPTION: 'Description',
		UPC: 'UPC',
		SUPPLIER: 'Supplier',
		PRODUCER: 'Producer',
		BUSINESS_NAME: 'Business Name',
		BUSINESS_EMAIL_ID: 'Business Email ID',
		BUSINESS_PORTAL_URL: 'Business Portal URL to Buy',
		STREET_ADDRESS: 'Street Address',
		BUSINESS_PHONE_NUMBER: 'Business Phone Number',
		STORE_TIMING: 'Store Timing',
		STORE_DESCRIPTION: 'Store Description',
		SERVICE_OPTIONS: 'Service Options',
		UPLOAD_BUSINESS_PICTURE: 'Upload Business Picture',
		EMAIL_ID: 'Email ID',
		SELECT_PHOTO: 'Select Photo',
		TAKE_PHOTO: 'Take Photo',
		BRAND: 'Brand',
		TASTE: 'Taste',
		NO_MORE_DEALS: 'No more deals',
		NO_MORE_TEMPLATES: 'No more templates',
		NO_DEALS_FOUND: 'No deals found',
		NO_MORE_FAVORITES: 'No more favorites',
		NO_FAVORITES_FOUND: 'No favorites found',
		NO_MORE_NOTIFICATIONS: 'No more notifications',
		NO_NOTIFICATIONS_FOUND: 'No notifications found',
		SELECT_LOCATION: 'Select your location',
		LICENSE_NUMBER: 'License Number',
		NO_PRODUCT_FOUND: 'No product found',
	},
	TITLE: {
		DELETE_ACCOUNT: 'Delete Account',
		SIGN_IN: 'Sign In',
		CREATE_AN_ACCOUNT: 'Create an Account',
		PROFILE: 'Profile',
		CATEGORIES: 'Categories',
		FLASH_DEALS: 'Flash Deals',
		TRENDING: 'Trending',
		NEW: 'New',
		COLLECTION: 'Collections',
		VERIFICATION: 'Verification',
		DEAL_DETAIL: 'Deal Detail',
		FILTERS: 'Filters',
		FORGOT_PASSWORD: 'Forgot Password',
		IMPRESSION: 'Impression',
		PRODUCT_DETAILS: 'Product Details',
		CONFIRM_YOUR_AGE: 'Please confirm your age',
		SIGN_OUT_CONFIRMATION: 'Sign Out Confirmation',
	},
	SUBTITLE: {
		VERIFICATION_CODE_SENT:
			'Enter the verification code we just sent you on your email address',
		ENTER_THE_EMAIL_ADDRESS: 'Enter the email address',
	},
	TEXT: {
		LOW_STOCK: 'Low Stock',
		SELECT_LOCATION: 'Select a Location',
		OR_SIGN_IN_WITH: 'Or Sign in with',
		OR: 'OR',
		OR_SIGN_UP_WITH: 'Or Sign up with',
		NOT_HAVE_AN_ACCOUNT: 'Not have an Account? ',
		ALREADY_HAVE_AN_ACCOUNT: 'Already have an Account? ',
		DID_NOT_RECEIVE_CODE: "If you didn't receive a code! ",
		RESENT_CODE_IN: 'Resend Code in ',
		BACK_TO: 'Back to ',
		EMAIL_YOU_LINK_TO_RESET_PASSWORD:
			'We will email you a link to reset your password',
		WE_DONT_HAVE_LOCATION: "We don't hav your location yet!",
		SET_LOCATION_TO_EXPLORE_DEALS:
			'Set your location to start exploring deals near you',
		DELETE_ACCOUNT:
			'Are you sure? Your account information will be deleted from our app.',
		CONFIRM_YOUR_AGE:
			'You must be 21 or above to enter this application. Please verify your age.',
		CONFIRM_SIGN_OUT: 'Are you sure you want to sign out? You will need to log in again to access your account.',
	},
	ERROR_MESSAGE: {
		ALREADY_HAVE_ACCOUNT: 'Already have an Account',
		ACCOUNT_NOT_FIND: "Couldn't find your SYPnSAVE Account",
		INVALID_CREDENTIALS: 'Invalid Credentials!',
		TOO_MANY_ATTEMPT: 'Too many attempts! Please try again later',
		CONNECTION_TIMEOUT: 'Connection timeout!',
		SERVER_ERROR: 'Oops! Server error',
		INVALID_EMAIL: 'The email ID is invalid!',
		ALREADY_EXISTS: 'The email ID is in use!',
		USER_NOT_FOUND: 'User not found!',
		WRONG_PASSWORD: 'Wrong password!',
		INTERNAL_SERVER_ERROR: 'Internal server error!',
		SERVER_DOWN: 'Server down!',
		SESSION_EXPIRED: 'Session expired!',
		EMAIL_NOT_VERIFIED: 'Email not verified',
		SIGN_IN_CANCELED: 'Sign In canceled',
		BUSINESS_REQUIRED: 'Business ID required',
		CAMERA_NOT_AVAILABLE: 'Camera not available',
		SOMETHING_WRONG: 'Oops! Something went wrong',
		INVALID_PHONE: 'Phone number must be 10 digits',
		DOB_REQUIRED: 'Date Of Birth is required!',
		REQUEST_RECENT_LOGIN: 'Please sign in again to update your password',
		NETWORK_ERROR:
			'Network error. Please check your internet connection and try again.',
		WEAK_PASSWORD:
			'Password must contain one number, one letter and one special character!',
		BUSINESS_PROFILE_REQUIRED:
			'Please create Business profile first to create a Deal.',
		ZIP_CODE_VALIDATION: 'Please enter a valid ZIP code.',
		LOCATION_NOT_SERVICEABLE: 'Location Not Serviceable',
		SERVICE_IS_ONLY_IN_USA: 'We currently only provide service within the United States. Please select a US location to view deals.'
	},
	SUCCESS_MESSAGE: {
		VERIFICATION_EMAIL_SENT: "We've sent a verification email to your email",
		RESET_PASSWORD_LINK_SENT: "We've sent a password reset link to your email.",
		PASSWORD_CHANGED: 'Password updated!',
		PROFILE_UPDATE: 'Profile updated!',
		BUSINESS_PROFILE_UPDATE: 'Business profile updated!',
		REQUEST_ADD_PRODUCT: 'Add product request sent!',
		DEAL_CREATED: 'Deal created!',
		DEAL_UPDATED: 'Deal updated!',
		DEAL_DELETED: 'Deal deleted!',
		TEMPLATE_UPDATED: 'Template updated!',
		TEMPLATE_DELETED: 'Template deleted',
		TEMPLATE_SAVED: 'Template saved!',
		ADDRESS_SAVED: 'Address saved!',
		ACCOUNT_DELETE: 'Account deleted!',
		PASSWORD_RESET_LINK_SENT: 'Password reset link sent',
		PASSWORD_CHANGED_SUCCESSFULLY: 'Password changed successfully',
		LOGGED_IN_WITH_APPLE_ACCOUNT: 'Logged in with Apple account'
	},
	ROUTE: {
		ENABLE_LOCATION: 'EnableLocation',
		SELECT_LOCATION: 'SelectLocation',
		CONFIRM_LOCATION: 'ConfirmLocation',
		EDIT_TEMPLATE: 'EditTemplate',
		EDIT_DEAL: 'EditDeal',
		MY_DEAL_DETAIL: 'MyDealDetail',
		CREATE_DEAL: 'CreateDeal',
		REQUEST_ADD_PRODUCT: 'RequestAddProduct',
		MY_DEALS_NAVIGATION: 'MyDealsNavigation',
		MY_DEALS: 'MyDeals',
		TEMPLATES: 'Templates',
		TEMPLATES_NAVIGATION: 'TemplatesNavigation',
		DEALS: 'Deals',
		FAVORITE: 'Favorite',
		FAVORITE_NAVIGATION: 'FavoriteNavigation',
		PROFILE: 'Profile',
		CHANGE_PASSWORD: 'ChangePassword',
		NOTIFICATIONS: 'Notifications',
		CONTACT_US: 'ContactUs',
		PRIVACY_POLICY: 'PrivacyPolicy',
		TERMS_CONDITIONS: 'TermsAndConditions',
		HELP_SUPPORT: 'HelpAndSupport',
		AGE_VERIFICATION: 'AgeVerification',
		SIGN_IN: 'SignIn',
		SIGN_UP: 'SignUp',
		SIGN_OUT: 'SignOut',
		FORGOT_PASSWORD: 'ForgotPassword',
		VERIFICATION: 'Verification',
		EDIT_PROFILE: 'EditProfile',
		EDIT_BUSINESS_PROFILE: 'EditBusinessProfile',
		CATEGORY_DEAL: 'CategoryDeal',
		DEALS_NAVIGATION: 'DealsNavigation',
		PROFILE_NAVIGATION: 'ProfileNavigation',
		BOTTOM_NAVIGATION: 'BottomNavigation',
		FLASH_DEALS: 'FlashDeals',
		NEW_DEALS: 'NewDeals',
		TRENDING_DEALS: 'TrendingDeals',
		COLLECTIONS_DEALS: 'CollectionsDeals',
		DEAL_DETAIL: 'DealDetail',
		SHOP_DETAILS: 'ShopDetails',
		PRODUCT_NAVIGATION: 'DealNavigation',
		DEAL_FILTERS: 'DealFilters',
		ADD_ADDRESS: 'AddAddress',
		SEARCH_DEALS: 'SearchDeals',
	},
	SCREENS: {
		ENABLE_LOCATION: 'Enable or Select Location',
		SELECT_LOCATION: 'Select Location',
		CONFIRM_LOCATION: 'Confirm Location',
		AGE_VERIFICATION: 'Age Verification',
		EDIT_TEMPLATE: 'Edit Template',
		EDIT_DEAL: 'Edit Deal',
		MY_DEAL_DETAIL: 'Deal Detail',
		REQUEST_ADD_PRODUCT: 'Request Add Product',
		CREATE_DEAL: 'Create Deal',
		MY_DEALS: 'My Deals',
		MY_TEMPLATES: 'My Templates',
		TEMPLATES: 'Templates',
		DEALS: 'Deals',
		FAVORITE: 'Favorite',
		PROFILE: 'Profile',
		CHANGE_PASSWORD: 'Change Password',
		NOTIFICATIONS: 'Notifications',
		CONTACT_US: 'Contact Us',
		PRIVACY_POLICY: 'Privacy Policy',
		TERMS_CONDITIONS: 'Terms and Conditions',
		HELP_SUPPORT: 'Help and Support',
		SIGN_IN: 'Sign In',
		SIGN_UP: 'Sign Up',
		SIGN_OUT: 'Sign Out',
		FORGOT_PASSWORD: 'Forgot Password',
		VERIFICATION: 'Verification',
		EDIT_PROFILE: 'Edit Profile',
		EDIT_BUSINESS_PROFILE: 'Edit Business Profile',
		FLASH_DEALS: 'Flash Deals',
		NEW_DEALS: 'New',
		TRENDING_DEALS: 'Trending',
		COLLECTIONS_DEALS: 'Collections',
		DEAL_DETAILS: 'Deal Detail',
		SHOP_DETAILS: 'Shop Details',
		ADD_ADDRESS: 'Add Address',
		FILTERS: 'Filters',
		DELETE_ACCOUNT: 'Delete Account',
	},
	ROLE: {
		CUSTOMER: 'Customer',
		RETAILER: 'Retailer',
	},
	REGEX: {
		URL: /^(http|https):\/\/([a-z]*\.)?[a-z]*\.[a-z]{2,}(\/)?$/,
		PHONE: /^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
		EMAIL: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
		ABV: /^[+-]?\d+(\.\d+)?$/,
		UPC: /^[0-9]+$/,
		PRODUCT_PRICE: /^[0-9]+$/,
		PASSWORD: /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9_!@#$%^&*]{6,}$/,
		PRODUCT_SIZE: /^(0|[1-9]\d*)(\.\d+)?$/,
		DISCOUNT_PERCENT: /^[0-9]+$/,
		DISCOUNT_PRICE: /^[0-9]+$/,
		PIN_CODE: /^(?!0*$)[0-9]*$/,
		BUSINESS_NAME: /^[A-Za-z0-9 _@&-]*[A-Za-z0-9][A-Za-z0-9 _@&-]*$/,
		STREET_ADDRESS: /^[A-Za-z0-9 _]*[A-Za-z0-9][A-Za-z0-9 _,@&-]*$/,
	},
	DEAL_TYPE: {
		PERCENT: 'percent',
		FIX: 'fix',
		QUANTITY: 'quantity',
	},
};

export const DEAL_DETAILS_DEEP_LINK = "https://api.sypnsave.com/app/deal-detail";
export const DEEP_LINK_BASE_URL = "https://api.sypnsave.com/app";

export const APP_NAME = 'SYP n’ SAVE';
export const APP_VERSION = 'v1.1';
export const APP_CONTACT_MAIL = '<EMAIL>';
export const APP_FACEBOOK_APP_ID = '754509512299378';
export const APP_CONTACT_PHONE = '(*************';
export const APP_CONTACT_COORDINATES = '23.182574354923858,72.6286068';
export const APP_CONTACT_ADDRESS =
	'7200 Madison St, Forest Park, IL 60130';
export const APP_AGE_LIMIT = 21;
export const APP_API_URL = 'https://api.sypnsave.com';
export const APP_GOOGLE_CLOUD_STORAGE =
	'https://storage.googleapis.com/sypnsave/';
export const APP_GOOGLE_MAP_API_KEY = 'AIzaSyArFGXRxyjvvmsfeMqg2cuJJb37ldrNvWg';
export const APP_GOOGLE_WEB_CLIENT_ID =
	'740702161397-hnhlreer7o5ohroitoaih66rn777vfr6.apps.googleusercontent.com';
export const APP_TERMS_AND_CONDITIONS_URL =
	'https://sypnsave.com/terms-and-conditions';
export const APP_PRIVACY_POLICY_URL = 'https://sypnsave.com/privacy-policy';

export const timeRange = [
	'12:00 AM', '12:15 AM', '12:30 AM', '12:45 AM',
	'1:00 AM', '1:15 AM', '1:30 AM', '1:45 AM',
	'2:00 AM', '2:15 AM', '2:30 AM', '2:45 AM',
	'3:00 AM', '3:15 AM', '3:30 AM', '3:45 AM',
	'4:00 AM', '4:15 AM', '4:30 AM', '4:45 AM',
	'5:00 AM', '5:15 AM', '5:30 AM', '5:45 AM',
	'6:00 AM', '6:15 AM', '6:30 AM', '6:45 AM',
	'7:00 AM', '7:15 AM', '7:30 AM', '7:45 AM',
	'8:00 AM', '8:15 AM', '8:30 AM', '8:45 AM',
	'9:00 AM', '9:15 AM', '9:30 AM', '9:45 AM',
	'10:00 AM', '10:15 AM', '10:30 AM', '10:45 AM',
	'11:00 AM', '11:15 AM', '11:30 AM', '11:45 AM',
	'12:00 PM', '12:15 PM', '12:30 PM', '12:45 PM',
	'1:00 PM', '1:15 PM', '1:30 PM', '1:45 PM',
	'2:00 PM', '2:15 PM', '2:30 PM', '2:45 PM',
	'3:00 PM', '3:15 PM', '3:30 PM', '3:45 PM',
	'4:00 PM', '4:15 PM', '4:30 PM', '4:45 PM',
	'5:00 PM', '5:15 PM', '5:30 PM', '5:45 PM',
	'6:00 PM', '6:15 PM', '6:30 PM', '6:45 PM',
	'7:00 PM', '7:15 PM', '7:30 PM', '7:45 PM',
	'8:00 PM', '8:15 PM', '8:30 PM', '8:45 PM',
	'9:00 PM', '9:15 PM', '9:30 PM', '9:45 PM',
	'10:00 PM', '10:15 PM', '10:30 PM', '10:45 PM',
	'11:00 PM', '11:15 PM', '11:30 PM', '11:45 PM'
];

export const dayNames = [
	'Sun',
	'Mon',
	'Tue',
	'Wed',
	'Thu',
	'Fri',
	'Sat',
];

export const DEAL_TYPE_OPTIONS = [
	{
		label: 'Flash',
		value: 'flash',
	},
	{
		label: 'New',
		value: 'new',
	},
	{
		label: 'Collections',
		value: 'collection',
	},
];

export const DISCOUNT_TYPE_OPTIONS = [
	{ label: 'Percentage', value: 'percent' },
	{
		label: 'Fix Amount',
		value: 'fix',
	},
	{
		label: 'Quantity Deal',
		value: 'quantity',
	},
];

export default constants;

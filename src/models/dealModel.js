const dealModel = {
	_id: '',
	brand: {
		_id: '',
		name: '',
	},
	business: {
		_id: '',
		createdAt: '',
		email: '',
		location: {
			coordinates: [],
			type: 'Point',
		},
		name: '',
		photos: [],
		serviceOptions: [],
		status: true,
		updatedAt: '',
		user: '',
		verified: true,
	},
	category: {
		_id: '',
		name: '',
	},
	createdAt: '',
	discountPrice: 0,
	discountType: '',
	discountValue: 0,
	location: {
		coordinates: [],
		type: 'Point',
	},
	product: {
		_id: '',
		brand: '',
		category: '',
		description: '',
		name: '',
		photo: '',
		size: 0,
		status: true,
		subcategory: '',
	},
	productPrice: 0,
	subcategory: {
		_id: '',
		name: '',
	},
	type: '',
	updatedAt: '',
	expiryDateTime: '',
	lowStock: false,
	quantity: 1,
};

export default dealModel;

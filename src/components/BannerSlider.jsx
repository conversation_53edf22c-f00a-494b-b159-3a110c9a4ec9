import * as React from 'react';
import {useEffect, useState} from 'react';
import {Dimensions} from 'react-native';
import {useDispatch} from 'react-redux';
import {AspectRatio, Center, Image, Link} from 'native-base';
import Carousel, {Pagination} from 'react-native-snap-carousel';

import constants from '../utils/constants';
import {toUri} from '../utils/helpers';
import {getAllBanner} from '../screens/Deals/dealsSlice';

const {width: viewportWidth} = Dimensions.get('window');
const ITEM_WIDTH = viewportWidth;
const SLIDER_WIDTH = viewportWidth;

const CarouselCardItem = ({item, index}) => {
	return (
		<Link href={item.url ? item.url : ''} isExternal>
			<AspectRatio key={index} width="100%" ratio={6 / 3}>
				<Image source={{uri: toUri(item.photo)}} alt="image" />
			</AspectRatio>
		</Link>
	);
};

const BannerSlider = props => {
	/**
	 * Start Initials
	 */
	const [banners, setBanners] = useState([]);
	const [index, setIndex] = React.useState(0);

	const isCarousel = React.useRef(null);

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		fetchBanners();
	}, []);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchBanners = () => {
		dispatch(getAllBanner())
			.unwrap()
			.then(res => {
				setBanners(res);
			});
	};
	/**
	 * End Methods
	 */

	return (
		<Center w="100%">
			<Carousel
				layout="default"
				layoutCardOffset={9}
				ref={isCarousel}
				data={banners}
				renderItem={CarouselCardItem}
				sliderWidth={SLIDER_WIDTH}
				itemWidth={ITEM_WIDTH}
				onSnapToItem={index => setIndex(index)}
				activeSlideAlignment={'start'}
				inactiveSlideScale={1}
				inactiveSlideOpacity={1}
				enableSnap={true}
				loop={true}
				autoplay={true}
				autoplayDelay={500}
				autoplayInterval={30000}
				disableIntervalMomentum
				pagingEnabled
				useScrollView={true}
				useExperimentalSnap={true}
			/>
			<Pagination
				dotsLength={banners.length}
				activeDotIndex={index}
				carouselRef={isCarousel}
				dotStyle={{
					width: 10,
					height: 10,
					borderRadius: 5,
					marginHorizontal: 0,
					borderWidth: 1,
					borderColor: constants.COLORS.ORANGE,
					backgroundColor: constants.COLORS.ORANGE,
				}}
				inactiveDotStyle={{
					backgroundColor: 'transparent',
				}}
				containerStyle={{marginTop: -15, marginBottom: -15}}
				inactiveDotOpacity={1}
				inactiveDotScale={1}
				tappableDots={true}
			/>
		</Center>
	);
};

export default BannerSlider;

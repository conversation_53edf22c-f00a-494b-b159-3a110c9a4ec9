import * as React from 'react';
import {useEffect, useState} from 'react';
import {Dimensions} from 'react-native';
import {useDispatch} from 'react-redux';

import Carousel from 'react-native-snap-carousel';
import CategoryAvatar from './CategoryAvatar';
import {getAllCategory} from '../screens/Deals/dealsSlice';

const {width: viewportWidth} = Dimensions.get('window');
const SLIDE_WIDTH = Math.round(viewportWidth / 5.5);
const ITEM_HORIZONTAL_MARGIN = 1;
const ITEM_WIDTH = Number(SLIDE_WIDTH + ITEM_HORIZONTAL_MARGIN * 6);
const SLIDER_WIDTH = viewportWidth;

const CategorySlider = props => {
	/**
	 * Start Initials
	 */
	const {navigation} = props;

	const [categoryList, setCategoryList] = useState([]);

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		fetchCategoryList();
	}, []);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchCategoryList = () => {
		dispatch(getAllCategory())
			.unwrap()
			.then(res => {
				setCategoryList(res);
			});
	};

	const CarouselCardItem = ({item, index}) => (
		<CategoryAvatar idex={index} item={item} navigation={navigation} />
	);
	/**
	 * End Methods
	 */

	return (
		<Carousel
			data={categoryList}
			renderItem={CarouselCardItem}
			sliderWidth={SLIDER_WIDTH}
			itemWidth={ITEM_WIDTH}
			activeSlideAlignment={'start'}
			inactiveSlideScale={1}
			inactiveSlideOpacity={1}
			removeClippedSubviews={false}
			contentContainerCustomStyle={{
				overflow: 'hidden',
				width: ITEM_WIDTH * categoryList.length,
				marginLeft: -0.5,
			}}
		/>
	);
};

export default CategorySlider;

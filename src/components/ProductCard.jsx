import React from 'react';
import {Box, VStack, HStack, Image, Stack, Text} from 'native-base';
import {StyleSheet, TouchableOpacity} from 'react-native';

import constants from '../utils/constants';
import {toUri} from '../utils/helpers';

const ProductCard = props => {
	/**
	 * Start Initials
	 */
	const {product, selectedProduct, onSelectProduct} = props;
	/**
	 * End Initials
	 */

	const onPressSelectProduct = () => {
		onSelectProduct(product);
	};

	return (
		<TouchableOpacity onPress={onPressSelectProduct}>
			<Box
				w="100%"
				style={
					selectedProduct?._id === product._id
						? styles.selectedCardStyle
						: styles.cardStyle
				}>
				<VStack>
					<HStack space={1}>
						<Box w="20%">
							<Image
								source={{uri: toUri(product.photo)}}
								alt="image"
								resizeMode="contain"
								size="md"
							/>
						</Box>

						<Stack w="80%" space={2}>
							<Stack>
								<Text fontSize={16}>{product.name}</Text>
								<Text fontSize={12}>{product.size} ml</Text>
							</Stack>
						</Stack>
					</HStack>
				</VStack>
			</Box>
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	cardStyle: {
		shadowColor: constants.COLORS.GREY1,
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.3,
		shadowRadius: 2.84,
		elevation: 3,
		borderRadius: 5,
		padding: 2,
		marginHorizontal: 5,
		backgroundColor: constants.COLORS.WHITE,
	},
	selectedCardStyle: {
		shadowColor: constants.COLORS.GREY1,
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.3,
		shadowRadius: 2.84,
		elevation: 3,
		borderRadius: 5,
		padding: 2,
		marginHorizontal: 5,
		backgroundColor: constants.COLORS.WHITE,
		borderColor: constants.COLORS.ORANGE,
		borderWidth: 1,
	},
});

export default ProductCard;

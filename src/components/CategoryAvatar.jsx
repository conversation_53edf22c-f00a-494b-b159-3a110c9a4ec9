import * as React from 'react';
import {TouchableOpacity} from 'react-native';

import {Avatar, Box, Text} from 'native-base';
import constants from '../utils/constants';
import {toUri} from '../utils/helpers';

const CategoryAvatar = props => {
	/**
	 * Start Initials
	 */
	const {index, item, navigation} = props;
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const openCategoryScreen = (navigation, item) => {
		navigation.navigate(constants.ROUTE.CATEGORY_DEAL, {
			categoryName: item.name,
			categoryId: item._id,
		});
	};
	/**
	 * End Methods
	 */

	return (
		<Box key={index} alignItems="center" background="white">
			<TouchableOpacity
				onPress={() => openCategoryScreen(navigation, item)}
				style={{alignItems: 'center'}}>
				<Avatar
					size="lg"
					source={{uri: toUri(item.photo)}}
					borderWidth={1}
					borderColor="gray.300"
					bg="white"
					style={{padding: 5}}>
					{item.name}
				</Avatar>
				<Text fontSize={12} ml={1}>
					{item.name}
				</Text>
			</TouchableOpacity>
		</Box>
	);
};

export default CategoryAvatar;

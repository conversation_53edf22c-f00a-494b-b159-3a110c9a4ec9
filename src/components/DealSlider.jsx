import * as React from 'react';
import {Dimensions} from 'react-native';

import Carousel from 'react-native-snap-carousel';
import {Box} from 'native-base';

import DealCard from './DealCard';

const {width: viewportWidth} = Dimensions.get('window');
const SLIDE_WIDTH = Math.round(viewportWidth / 3);
const ITEM_HORIZONTAL_MARGIN = 5;
const ITEM_WIDTH = Number(SLIDE_WIDTH + ITEM_HORIZONTAL_MARGIN * 5);
const SLIDER_WIDTH = viewportWidth;

const DealSlider = props => {
	/**
	 * Start Initials
	 */
	const {navigation, deals, user} = props;
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const CarouselCardItem = ({item, index}) => {
		return (
			<Box my={1}>
				<DealCard
					key={index}
					navigation={navigation}
					productId={item.product?._id}
					productName={item.product?.name}
					productPhoto={item.product?.photo}
					productPrice={item.productPrice}
					discountPrice={item.discountPrice}
					discountType={item.discountType}
					discountValue={item.discountValue}
					productSize={item.product?.size}
					type={item.type}
					dealId={item._id}
					user={user}
					lowStock={item.lowStock}
					quantity={item.quantity}
				/>
			</Box>
		);
	};
	/**
	 * End Methods
	 */

	return (
		<Carousel
			data={deals}
			renderItem={CarouselCardItem}
			sliderWidth={SLIDER_WIDTH}
			itemWidth={ITEM_WIDTH}
			activeSlideAlignment={'start'}
			inactiveSlideScale={1}
			inactiveSlideOpacity={1}
			removeClippedSubviews={false}
			contentContainerCustomStyle={{
				overflow: 'hidden',
				width: ITEM_WIDTH * deals.length + ITEM_WIDTH - 100,
			}}
		/>
	);
};

export default DealSlider;

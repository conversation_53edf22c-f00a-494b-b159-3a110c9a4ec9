import React, {useEffect, useState} from 'react';
import {Dimensions, StyleSheet, useWindowDimensions} from 'react-native';
import {TabView, SceneMap, TabBar} from 'react-native-tab-view';

import constants from '../utils/constants';
import DealList from '../screens/FlashDeals/DealList';
import {toCamelCase} from '../utils/helpers';

const {width: viewportWidth} = Dimensions.get('window');

const DealCategoryTab = props => {
	/**
	 * Start Initials
	 */
	const {navigation, categoryList, type} = props;
	const layout = useWindowDimensions();

	const [index, setIndex] = useState(0);
	const [categoryTabs, setCategoryTabs] = useState({});
	const [routes, setRoutes] = useState([]);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		let category = [];
		let maps = {};

		categoryList.map(item => {
			let key = toCamelCase(item.name);

			category.push({key: key, title: item.name});

			maps[key] = () => (
				<DealList navigation={navigation} categoryId={item._id} type={type} />
			);
		});
		setRoutes(category);
		setCategoryTabs(maps);
	}, []);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const renderScene = SceneMap(categoryTabs);
	/**
	 * End Methods
	 */

	const renderTabBar = props => (
		<TabBar
			{...props}
			scrollEnabled
			indicatorStyle={styles.indicator}
			style={styles.tabBar}
			tabStyle={styles.tab}
			labelStyle={styles.label}
		/>
	);

	return routes.length > 0 ? (
		<TabView
			lazy
			navigationState={{index, routes}}
			renderScene={renderScene}
			renderTabBar={renderTabBar}
			onIndexChange={setIndex}
			initialLayout={{width: layout.width}}
			style={{borderWidth: 0, borderColor: 'red'}}
		/>
	) : (
		''
	);
};

const styles = StyleSheet.create({
	tabBar: {
		backgroundColor: constants.COLORS.ORANGE,
		width: viewportWidth,
	},
	tab: {
		width: 'auto',
	},
	indicator: {
		backgroundColor: 'white',
	},
	label: {
		fontSize: 14,
		textTransform: 'capitalize',
	},
});

export default DealCategoryTab;

import {Select} from 'native-base';
import React from 'react';
import {
	View,
	Text,
	FlatList,
	Checkbox,
	FormControl,
	HStack,
	Stack,
} from 'react-native';

export const BusinessCalender = ({
	dayNames,
	businessHours,
	handleTimeChange,
	handleCheckboxChange,
	timeRange,
	businessHourError,
}) => {
	const renderItem = ({item: day, index}) => {
		return (
			<Stack key={index} mt={0.5} space={1}>
				<HStack justifyContent="space-between">
					<Text fontWeight="bold" mt={0.5}>
						{day}
					</Text>
				</HStack>
				<HStack space={5} mb={1}>
					<Stack>
						<Select
							minWidth="140"
							fontSize={15}
							accessibilityLabel="Opens at"
							isDisabled={businessHours[day]?.closed}
							selectedValue={businessHours[day]?.opensAt || ''}
							onValueChange={itemValue =>
								handleTimeChange(day, 'opensAt', itemValue)
							}
							placeholder="Opens at"
							mt={1}>
							{timeRange.map((time, key) => (
								<Select.Item key={key} label={time} value={time} />
							))}
						</Select>
					</Stack>
					<Stack>
						<Select
							fontSize={15}
							minWidth="140"
							accessibilityLabel="Closes at"
							isDisabled={businessHours[day]?.closed}
							selectedValue={businessHours[day]?.closesAt || ''}
							placeholder="Closes at"
							mt={1}
							onValueChange={itemValue =>
								handleTimeChange(day, 'closesAt', itemValue)
							}>
							{timeRange.map((time, key) => (
								<Select.Item key={key} label={time} value={time} />
							))}
						</Select>
					</Stack>
					<Stack mt={1}>
						<Checkbox
							defaultIsChecked={businessHours[day]?.closed || false}
							mt={1}
							aria-label={day}
							id={day}
							onChange={e => handleCheckboxChange(day, e)}
						/>
					</Stack>
				</HStack>
			</Stack>
		);
	};

	return (
		<FlatList
			data={dayNames}
			renderItem={renderItem}
			keyExtractor={(item, index) => index.toString()}
			ListHeaderComponent={() => (
				<View>
					<FormControl.Label>
						Store Timing
						<Text color="red">*</Text>
					</FormControl.Label>
					<HStack justifyContent="space-between">
						<Text fontWeight="bold" fontSize={17}>
							Days
						</Text>
						<Text fontWeight="bold" fontSize={17}>
							Closed
						</Text>
					</HStack>
				</View>
			)}
			ListFooterComponent={() => (
				<>
					{businessHourError && (
						<Text fontSize={12} color="red">
							All business days and time are required.
						</Text>
					)}
				</>
			)}
		/>
	);
};

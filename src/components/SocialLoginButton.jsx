import * as React from 'react';
import {HStack, IconButton, useToast} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch} from 'react-redux';

import {
	appleSignIn,
	facebookSignIn,
	googleSignIn,
} from '../screens/SignIn/userSignInSlice';
import {
	appleSignUp,
	facebookSignUp,
	googleSignUp,
} from '../screens/SignUp/userSignUpSlice';
import ToastAlert from './ToastAlert';
import {Platform} from 'react-native';

const SocialLoginButton = props => {
	/**
	 * Start Initials
	 */
	const {userRole = null, setLoading} = props;

	const dispatch = useDispatch();

	const toast = useToast();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const googleSignInUser = () => {
		setLoading(true);
		if (userRole) {
			dispatch(googleSignUp(userRole))
				.unwrap()
				.then(() => {
					setLoading(false);
				})
				.catch(err => {
					setLoading(false);
					toast.show({
						avoidKeyboard: true,
						render: ({id}) => {
							return (
								<ToastAlert
									id={id}
									toast={toast}
									type="error"
									message={err?.message}
								/>
							);
						},
					});
				});
		} else {
			dispatch(googleSignIn())
				.unwrap()
				.then(() => setLoading(false))
				.catch(err => {
					setLoading(false);
					toast.show({
						avoidKeyboard: true,
						render: ({id}) => {
							return (
								<ToastAlert
									id={id}
									toast={toast}
									type="error"
									message={err?.message}
								/>
							);
						},
					});
				});
		}
	};

	const facebookSignInUser = () => {
		setLoading(true);
		if (userRole) {
			dispatch(facebookSignUp(userRole))
				.unwrap()
				.then(() => setLoading(false))
				.catch(err => {
					setLoading(false);
					avoidKeyboard: true,
						toast.show({
							render: ({id}) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={err?.message}
									/>
								);
							},
						});
				});
		} else {
			dispatch(facebookSignIn())
				.unwrap()
				.then(() => setLoading(false))
				.catch(err => {
					setLoading(false);
					avoidKeyboard: true,
						toast.show({
							render: ({id}) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={err?.message}
									/>
								);
							},
						});
				});
		}
	};

	// Sign in with Apple account method start.
	const appleSignInUser = async () => {
		setLoading(true);
		if (userRole) {
			dispatch(appleSignUp(userRole))
				.unwrap()
				.then(() => setLoading(false))
				.catch(err => {
					setLoading(false);
					avoidKeyboard: true,
						toast.show({
							render: ({id}) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={err?.message}
									/>
								);
							},
						});
				});
		} else {
			dispatch(appleSignIn())
				.unwrap()
				.then(() => setLoading(false))
				.catch(err => {
					setLoading(false);
					avoidKeyboard: true,
						toast.show({
							render: ({id}) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={err?.message}
									/>
								);
							},
						});
				});
		}
	};
	/**
	 * End Methods
	 */

	return (
		<HStack space={10} justifyContent="center">
			{/* <IconButton
				onPress={googleSignInUser}
				backgroundColor={'custom.lightOrange'}
				variant="outline"
				borderRadius={10}
				_icon={{
					as: MaterialCommunityIcons,
					name: 'google',
					textAlign: 'center',
					size: 9,
				}}
			/> */}
			{Platform.OS === 'ios' && (
				<IconButton
					backgroundColor={'custom.lightOrange'}
					onPress={appleSignInUser}
					variant="outline"
					borderRadius={10}
					_icon={{
						as: MaterialCommunityIcons,
						name: 'apple',
						textAlign: 'center',
						size: 9,
					}}
				/>
			)}
			<IconButton
				onPress={facebookSignInUser}
				backgroundColor={'custom.lightOrange'}
				variant="outline"
				borderRadius={10}
				_icon={{
					as: MaterialCommunityIcons,
					name: 'facebook',
					textAlign: 'center',
					size: 9,
				}}
			/>
		</HStack>
	);
};

export default SocialLoginButton;

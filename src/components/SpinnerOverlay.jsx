import React from 'react';
import {Dimensions, StyleSheet} from 'react-native';
import {Center, Spinner} from 'native-base';
import {initialWindowMetrics} from 'react-native-safe-area-context';

const {height: viewportHeight} = Dimensions.get('window');

const SpinnerOverlay = props => {
	/**
	 * Start Initials
	 */
	const {loading} = props;
	/**
	 * End Initials
	 */

	if (loading)
		return (
			<Center style={styles.loading}>
				<Spinner color="custom.orange" size="lg" />
			</Center>
		);

	return <></>;
};

const styles = StyleSheet.create({
	loading: {
		position: 'absolute',
		left: 0,
		right: 0,
		top: 0,
		bottom: 0,
		opacity: 0.5,
		backgroundColor: 'black',
		justifyContent: 'center',
		alignItems: 'center',
		height: initialWindowMetrics.frame.height || viewportHeight,
	},
});

export default SpinnerOverlay;

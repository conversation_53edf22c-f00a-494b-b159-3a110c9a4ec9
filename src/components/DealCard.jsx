import React, {useEffect, useState} from 'react';
import {TouchableOpacity, StyleSheet, Dimensions} from 'react-native';

import {
	Badge,
	Box,
	Center,
	Flex,
	HStack,
	IconButton,
	Image,
	Stack,
	Text,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch, useSelector} from 'react-redux';

import constants from '../utils/constants';
import {
	markFavoriteDeal,
	unFavoriteDeal,
} from '../screens/Favorite/favoriteDealSlice';
import {toUri} from '../utils/helpers';

const {width: viewportWidth} = Dimensions.get('window');

const DealCard = props => {
	/**
	 * Start Initials
	 */
	const {
		quantity,
		navigation,
		productId,
		productName,
		productPhoto,
		productSize,
		productPrice,
		discountPrice,
		discountType,
		discountValue,
		dealId,
		user,
		lowStock,
		removeItemFromList,
	} = props;

	const [favorite, setFavorite] = useState(null);

	const dispatch = useDispatch();

	const fetchFavoriteDealsIDs = useSelector(
		state => state.favoriteDeal.favorites,
	);
	const isGuestUser = useSelector(state => state.userSignIn.isGuestUser);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		if (fetchFavoriteDealsIDs.length)
			(async () => {
				fetchFavoriteDealsIDs.some(o => o.deal === dealId && setFavorite(o));
			})();
	}, [fetchFavoriteDealsIDs]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const markDealUnFavorite = () => {
		dispatch(unFavoriteDeal(favorite._id))
			.unwrap()
			.then(() => {
				removeItemFromList && removeItemFromList(favorite._id);
			});
	};

	const markDealFavorite = () => {
		dispatch(markFavoriteDeal(dealId));
	};

	const gotToDealDetail = () => {
		navigation.navigate(constants.ROUTE.DEAL_DETAIL, {
			dealId: dealId,
			productId: productId,
		});
	};
	/**
	 * End Methods
	 */

	return (
		<TouchableOpacity onPress={gotToDealDetail} style={styles.cardStyle}>
			<Box width={viewportWidth / 2.8} px={1}>
				<Box>
					{quantity > 1 ? (
						<Text
							bg={'#F5F5F5'}
							position="absolute"
							zIndex={1}
							top={0}
							right={-5}
							borderRadius={8}
							px={1}
							fontSize={12}>
							Pack of {quantity}
						</Text>
					) : null}
					<HStack alignItems="center" justifyContent="space-between" zIndex={1}>
						{!isGuestUser && user?.role === constants.ROLE.CUSTOMER ? (
							fetchFavoriteDealsIDs.some(o => o.deal === dealId) ? (
								<IconButton
									variant="ghost"
									size="sm"
									_icon={{
										as: MaterialCommunityIcons,
										name: 'heart',
										color: 'custom.red',
									}}
									onPress={markDealUnFavorite}
								/>
							) : (
								<IconButton
									variant="ghost"
									size="sm"
									_icon={{
										as: MaterialCommunityIcons,
										name: 'heart-outline',
										color: 'custom.dark1',
									}}
									onPress={markDealFavorite}
								/>
							)
						) : null}
					</HStack>

					<Center>
						<Image
							source={{uri: toUri(productPhoto)}}
							alt="image"
							resizeMode="contain"
							size="lg"
						/>
						<Text color="custom.red" fontSize={10} bold>
							{lowStock && constants.TEXT.LOW_STOCK}
						</Text>
					</Center>
				</Box>
				<Stack p="1" space={1}>
					<Stack space={1} flex={1}>
						<Text fontSize={16} isTruncated>
							{productName}
						</Text>
						<Text fontSize={12}>{productSize} ml</Text>
					</Stack>
					<HStack space={3}>
						{[
							constants.DEAL_TYPE.FIX,
							constants.DEAL_TYPE.PERCENT,
							constants.DEAL_TYPE.QUANTITY,
						].includes(discountType) && (
							<Text fontSize={12} strikeThrough color="custom.red">
								${productPrice}
							</Text>
						)}
						<Text fontSize={14} bold color="custom.green">
							${discountPrice}
						</Text>
					</HStack>
					<Center bg="custom.green" borderRadius={2} zIndex={1}>
						{['fix', 'percent', 'quantity'].includes(discountType) ? (
							<>
								<Text color="white" fontSize={10} bold>
									{discountType === 'percent' || discountType === 'quantity'
										? `${discountValue}%`
										: `$${discountValue}`}{' '}
									OFF
								</Text>
							</>
						) : (
							<Text color="white" fontSize={10} bold>
								{discountType}
							</Text>
						)}
					</Center>
				</Stack>
			</Box>
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	cardStyle: {
		shadowColor: constants.COLORS.GREY1,
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.3,
		shadowRadius: 2.84,
		elevation: 3,
		borderRadius: 5,
		padding: 2,
		marginHorizontal: 5,
		backgroundColor: constants.COLORS.WHITE,
	},
});

export default DealCard;

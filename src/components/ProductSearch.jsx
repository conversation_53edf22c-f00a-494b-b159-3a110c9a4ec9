import React, {
	forwardRef,
	useCallback,
	useImperativeHandle,
	useState,
} from 'react';
import {
	Box,
	VStack,
	Center,
	useDisclose,
	Actionsheet,
	Input,
	Icon,
	Text,
	Button,
} from 'native-base';
import {ActivityIndicator, Dimensions, FlatList, Platform} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch} from 'react-redux';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import DeviceInfo from 'react-native-device-info';

import {addSearchKeyword, getProducts} from '../screens/CreateDeal/createDealSlice';
import ProductCard from './ProductCard';
import constants from '../utils/constants';

const {height: viewportHeight} = Dimensions.get('window');
const DEFAULT_PAGE = 1,
	DEFAULT_LIMIT = 10;

const ProductSearch = forwardRef((props, ref) => {
	/**
	 * Start Initials
	 */
	const {selectedProduct, setSelectedProduct} = props;

	const navigation = useNavigation();

	const [products, setProducts] = useState([]);
	const [search, setSearch] = useState('');
	const [totalPages, setTotalPages] = useState(1);
	const [initialLoading, setInitialLoading] = useState(false);
	const [loading, setLoading] = useState(false);
	const [hasData, setHasData] = useState(true);
	const [page, setPage] = useState(DEFAULT_PAGE);

	const dispatch = useDispatch();

	const {isOpen, onOpen, onClose} = useDisclose();

	const insets = useSafeAreaInsets();

	const bottomTabBarHeight = useBottomTabBarHeight();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchProducts();
		}, []),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const onChangeSearch = value => {
		setSearch(value);
	};

	const fetchProducts = () => {
		setPage(1);
		setInitialLoading(true);
		dispatch(getProducts({search: search, page: page, limit: DEFAULT_LIMIT}))
			.unwrap()
			.then(res => {
				setProducts(res.data);
				setTotalPages(res.totalPages);
				setInitialLoading(false);
				if (res.totalPages == 0 || res.totalPages == 1) {
					setHasData(false);
				}
			})
			.catch(() => setInitialLoading(false));
	};

	const fetchMoreProducts = () => {
		if (hasData) {
			setPage(page + 1);
			setLoading(true);
			dispatch(getProducts({search: search, page: page, limit: DEFAULT_LIMIT}))
				.unwrap()
				.then(res => {
					setProducts(prevProducts => [...prevProducts, ...res.data]);
					setTotalPages(res.totalPages);
					setLoading(false);

					if (page == totalPages) {
						setHasData(false);
					}
				})
				.catch(() => setLoading(false));
		}
	};

	useImperativeHandle(ref, () => ({
		openActionsheet() {
			onOpen();
			fetchProducts();
		},
	}));

	const handleKeyDown = () => {
		fetchProducts();
	};

	const onSelectProduct = item => {
		setSelectedProduct(item);
		onClose();
	};

	const redirectToRequestProduct = () => {
		navigation.navigate(constants.ROUTE.PROFILE_NAVIGATION, {
			screen: constants.ROUTE.REQUEST_ADD_PRODUCT
		});
	};
	/**
	 * End Methods
	 */

	const ItemView = ({item}) => {
		return (
			<Center w="100%" mt={2} mb={2}>
				<VStack space={3}>
					<ProductCard
						product={item}
						selectedProduct={selectedProduct}
						onSelectProduct={onSelectProduct}
					/>
				</VStack>
			</Center>
		);
	};

	return (
		<Box ref={ref}>
			<Actionsheet
				mt={Platform.OS === 'android' ? 5 : 0}
				isOpen={isOpen}
				onClose={onClose}
				size="full">
				<Actionsheet.Content
					h={
						viewportHeight -
						(bottomTabBarHeight +
							insets.top +
							(DeviceInfo.hasNotch() ? 10 : 81))
					}>
					<Box w="99%">
						<VStack space={2}>
							<Input
								size="xl"
								placeholder={constants.LABEL.SEARCH_FOR_PRODUCTS}
								py={3}
								fontSize={14}
								value={search}
								onChangeText={onChangeSearch}
								onSubmitEditing={handleKeyDown}
								returnKeyType="search"
								returnKeyLabel="search"
								InputLeftElement={
									<Icon
										as={MaterialCommunityIcons}
										name="magnify"
										size={6}
										ml={2}
										mr={2}
									/>
								}
							/>

							{initialLoading ? (
								<ActivityIndicator style={{paddingTop: 15}} />
							) : products?.length === 0 ? (
								<Center>
									<Text color="custom.grey" textAlign="center">
										{constants.LABEL.NO_PRODUCT_FOUND}
									</Text>
									<Button
										mt={2}
										onPress={redirectToRequestProduct}>
										{constants.BUTTON.REQUEST_A_PRODUCT}
									</Button>
								</Center>
							) : (
								<FlatList
									data={products}
									keyExtractor={(item, index) => index.toString()}
									enableEmptySections={true}
									renderItem={ItemView}
									refreshing={loading}
									onEndReached={fetchMoreProducts}
									style={{
										height:
											viewportHeight -
											(bottomTabBarHeight +
												insets.top +
												(DeviceInfo.hasNotch() ? 100 : 181)),
									}}
								/>
							)}
						</VStack>
					</Box>
				</Actionsheet.Content>
			</Actionsheet>
		</Box>
	);
});

export default ProductSearch;

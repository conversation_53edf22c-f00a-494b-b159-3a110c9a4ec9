import React from 'react';

import {<PERSON><PERSON>, CloseIcon, HStack, IconButton, Text, VStack} from 'native-base';

const ToastAlert = props => {
	/**
	 * Start Initials
	 */
	const {id, toast, type, message} = props;
	/**
	 * End Initials
	 */

	return (
		<Alert
			maxWidth="95%"
			alignSelf="center"
			flexDirection="row"
			status={type}
			variant="subtle"
			maxHeight={90}>
			<VStack space={1} flexShrink={1} w="100%">
				<HStack
					flexShrink={1}
					alignItems="center"
					justifyContent="space-between">
					<HStack space={2} flexShrink={1} alignItems="center">
						<Alert.Icon />
						<Text flexShrink={1} color={'darkText'}>
							{message}
						</Text>
					</HStack>
					<IconButton
						variant="ghost"
						icon={<CloseIcon size="4" />}
						_icon={{
							color: 'darkText',
						}}
						onPress={() => toast.close(id)}
					/>
				</HStack>
			</VStack>
		</Alert>
	);
};

export default ToastAlert;

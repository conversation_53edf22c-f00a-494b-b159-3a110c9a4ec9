import React, {useEffect} from 'react';

import {Icon, StatusBar} from 'native-base';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch, useSelector} from 'react-redux';
import {TabActions} from '@react-navigation/native';
import Geolocation from '@react-native-community/geolocation';

import constants from '../utils/constants';
import ProfileNavigation from './ProfileNavigation.js';
import DealsNavigation from './DealsNavigation';
import MyDealsNavigation from './MyDealsNavigation';
import TemplatesNavigation from './TemplatesNavigation';
import CreateDeal from '../screens/CreateDeal';
import FavoriteNavigation from './FavoriteNavigation';
import {
	setAddress,
	setCoordinates,
} from '../screens/AddAddress/addAddressSlice';
import {GooglePlaceName} from '../services/RNGoogleMaps';

const Tab = createBottomTabNavigator();

const BottomTabNavigation = () => {
	/**
	 * Start Initials
	 */
	const dispatch = useDispatch();

	const userData = useSelector(state => state.userSignIn.userData);
	const storedAddress = useSelector(state => state.addAddress.address);
	const isGuestUser = useSelector(state => state.userSignIn.isGuestUser);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		if (userData.role === constants.ROLE.CUSTOMER && !storedAddress) {
			(async () => {
				await getAddress();
			})();
		}
	}, [userData, storedAddress]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const getAddress = async () => {
		await Geolocation.getCurrentPosition(
			async ({coords}) => {
				const latitude = coords?.latitude ?? 0;
				const longitude = coords?.longitude ?? 0;

				dispatch(setCoordinates({latitude, longitude}));

				GooglePlaceName(latitude, longitude).then(r =>
					dispatch(setAddress(r.fullName)),
				);
			},
			() => {
				//
			},
			{
				enableHighAccuracy: false,
				timeout: 5000,
				maximumAge: 3600000,
			},
		);
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<StatusBar barStyle="light-content" />
			<Tab.Navigator
				initialRouteName={constants.ROUTE.DEALS_NAVIGATION}
				sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
				screenOptions={{
					tabBarActiveTintColor: constants.COLORS.ORANGE,
					tabBarInactiveTintColor: constants.COLORS.GREY5,
					tabBarLabelStyle: {
						fontSize: 12,
					},
					tabBarItemStyle: {
						paddingVertical: 7,
					},
					tabBarHideOnKeyboard: true,
					headerStyle: {
						backgroundColor: constants.COLORS.ORANGE,
					},
					headerTitleStyle: {
						color: constants.COLORS.WHITE,
						fontSize: 18,
						letterSpacing: 0.5,
					},
					headerTitleAlign: 'center',
					headerStatusBarHeight: 10,
				}}>
				<Tab.Screen
					name={constants.ROUTE.DEALS_NAVIGATION}
					component={DealsNavigation}
					options={{
						tabBarLabel: constants.SCREENS.DEALS,
						tabBarIcon: ({color}) => (
							<Icon
								as={MaterialCommunityIcons}
								name="flash-outline"
								size={5}
								color={color}
							/>
						),
						headerShown: false,
					}}
				/>
				{userData.role === constants.ROLE.RETAILER && (
					<>
						<Tab.Screen
							name={constants.ROUTE.MY_DEALS_NAVIGATION}
							component={MyDealsNavigation}
							options={{
								tabBarLabel: constants.SCREENS.MY_DEALS,
								tabBarIcon: ({color}) => (
									<Icon
										as={MaterialCommunityIcons}
										name="bullhorn-outline"
										size={5}
										color={color}
									/>
								),
								headerShown: false,
							}}
						/>
						<Tab.Screen
							name={constants.ROUTE.CREATE_DEAL}
							component={CreateDeal}
							listeners={({navigation}) => ({
								tabPress: event => {
									event.preventDefault();
									navigation.dispatch(
										TabActions.jumpTo(constants.ROUTE.CREATE_DEAL, {
											templateId: null,
										}),
									);
								},
							})}
							options={{
								unmountOnBlur: true,
								tabBarLabel: '',
								headerShown: true,
								headerStatusBarHeight: Platform.OS === 'ios' ? 60 : 10,
								tabBarShowLabel: true,
								headerTitle: constants.SCREENS.CREATE_DEAL,
								tabBarIcon: ({color}) => (
									<Icon
										as={MaterialCommunityIcons}
										name="plus-circle-outline"
										size={10}
										mt={3}
										color={color}
									/>
								),
							}}
						/>
						<Tab.Screen
							name={constants.ROUTE.TEMPLATES_NAVIGATION}
							component={TemplatesNavigation}
							options={{
								tabBarLabel: constants.SCREENS.TEMPLATES,
								tabBarIcon: ({color}) => (
									<Icon
										as={MaterialCommunityIcons}
										name="file-document-outline"
										size={5}
										color={color}
									/>
								),
								headerShown: false,
							}}
						/>
					</>
				)}
				{!isGuestUser && userData.role === constants.ROLE.CUSTOMER && (
					<Tab.Screen
						name={constants.ROUTE.FAVORITE_NAVIGATION}
						component={FavoriteNavigation}
						options={{
							tabBarLabel: constants.SCREENS.FAVORITE,
							tabBarIcon: ({color}) => (
								<Icon
									as={MaterialCommunityIcons}
									name="heart-outline"
									size={5}
									color={color}
								/>
							),
							headerShown: false,
						}}
					/>
				)}
				<Tab.Screen
					name={constants.ROUTE.PROFILE_NAVIGATION}
					component={ProfileNavigation}
					options={{
						tabBarLabel: constants.SCREENS.PROFILE,
						tabBarIcon: ({color}) => (
							<Icon
								as={MaterialCommunityIcons}
								name="account-outline"
								size={5}
								color={color}
							/>
						),
						headerShown: false,
					}}
				/>
			</Tab.Navigator>
		</>
	);
};

export default BottomTabNavigation;

import React, {useLayoutEffect} from 'react';

import {createStackNavigator} from '@react-navigation/stack';
import {getFocusedRouteNameFromRoute} from '@react-navigation/native';
import {IconButton} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import constants from '../utils/constants';
import MyTemplates from '../screens/MyTemplates';
import EditTemplate from '../screens/EditTemplate';

const Stack = createStackNavigator();

const TemplatesNavigation = ({navigation, route}) => {
	useLayoutEffect(() => {
		const tabHiddenRoutes = [constants.ROUTE.EDIT_PROFILE];

		if (tabHiddenRoutes.includes(getFocusedRouteNameFromRoute(route))) {
			navigation.setOptions({tabBarStyle: {display: 'none'}});
		} else {
			navigation.setOptions({tabBarStyle: {display: 'flex'}});
		}
	}, [navigation, route]);

	return (
		<Stack.Navigator
			initialRouteName={constants.ROUTE.TEMPLATES}
			sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
			screenOptions={{
				headerStyle: {
					backgroundColor: constants.COLORS.ORANGE,
				},
				headerTitleStyle: {
					color: constants.COLORS.WHITE,
					fontSize: 18,
					letterSpacing: 0.5,
				},
				headerTitleAlign: 'center',
				cardStyle: {backgroundColor: '#fff'},
			}}>
			<Stack.Screen
				name={constants.ROUTE.TEMPLATES}
				component={MyTemplates}
				options={{
					headerShown: false,
					headerTitle: constants.SCREENS.TEMPLATES,
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.EDIT_TEMPLATE}
				component={EditTemplate}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.EDIT_TEMPLATE,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.TEMPLATES)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
		</Stack.Navigator>
	);
};

export default TemplatesNavigation;

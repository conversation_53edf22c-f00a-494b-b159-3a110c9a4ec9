import React from 'react';

import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Bar} from 'native-base';
import {createStackNavigator} from '@react-navigation/stack';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {StackActions} from '@react-navigation/native';

import constants from '../utils/constants';
import Deals from '../screens/Deals';
import CategoryDeal from '../screens/CategoryDeal';
import FlashDeals from '../screens/FlashDeals';
import DealFilters from '../screens/DealFilters';
import AddAddress from '../screens/AddAddress';
import DealDetail from '../screens/DealDetail';
import ShopDetail from '../screens/ShopDetail';
import TrendingDeals from '../screens/TrendingDeals';
import NewDeals from '../screens/NewDeals';
import CollectionDeals from '../screens/CollectionDeals';
import SearchDeal from '../screens/SearchDeal';

const Stack = createStackNavigator();

const DealsNavigation = ({navigation}) => {
	return (
		<>
			<StatusBar
				backgroundColor={constants.COLORS.ORANGE}
				barStyle="light-content"
			/>
			<Stack.Navigator
				initialRouteName={constants.ROUTE.DEALS}
				sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
				screenOptions={{
					headerStyle: {
						backgroundColor: constants.COLORS.ORANGE,
					},
					headerTitleStyle: {
						color: constants.COLORS.WHITE,
						fontSize: 18,
						letterSpacing: 0.5,
					},
					headerTitleAlign: 'center',
					cardStyle: {backgroundColor: '#fff'},
				}}>
				<Stack.Screen
					name={constants.ROUTE.DEALS}
					component={Deals}
					options={{
						headerShown: false,
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.DEAL_DETAIL}
					component={DealDetail}
					options={{
						headerShown: true,
						headerTitle: 'Product Details',
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() =>
									navigation.dispatch(
										StackActions.replace(constants.ROUTE.DEALS), // Replace the pop to push, when It was redirected with the dynamic link there was showing an error no screen left due to that replaced with push.
									)
								}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.SHOP_DETAILS}
					component={ShopDetail}
					options={{
						headerShown: true,
						headerTitle: '',
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() => navigation.dispatch(StackActions.pop())}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.CATEGORY_DEAL}
					component={CategoryDeal}
					options={({route}) => ({
						headerShown: true,
						headerTitle: route.params?.categoryName,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() => navigation.navigate(constants.ROUTE.DEALS)}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
						headerRight: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() =>
									navigation.navigate(constants.ROUTE.DEAL_FILTERS)
								}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'filter-outline',
									color: 'white',
								}}
							/>
						),
					})}
				/>
				<Stack.Screen
					name={constants.ROUTE.FLASH_DEALS}
					component={FlashDeals}
					options={{
						headerShadowVisible: false,
						headerShown: true,
						headerTitle: constants.SCREENS.FLASH_DEALS,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() => navigation.navigate(constants.ROUTE.DEALS)}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
						headerRight: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() =>
									navigation.navigate(constants.ROUTE.DEAL_FILTERS)
								}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'filter-outline',
									color: 'white',
								}}
							/>
						),
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.TRENDING_DEALS}
					component={TrendingDeals}
					options={{
						headerShown: true,
						headerShadowVisible: false,
						headerTitle: constants.SCREENS.TRENDING_DEALS,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() => navigation.navigate(constants.ROUTE.DEALS)}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
						headerRight: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() =>
									navigation.dispatch(
										StackActions.push(constants.ROUTE.DEAL_FILTERS),
									)
								}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'filter-outline',
									color: 'white',
								}}
							/>
						),
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.NEW_DEALS}
					component={NewDeals}
					options={{
						headerShown: true,
						headerShadowVisible: false,
						headerTitle: constants.SCREENS.NEW_DEALS,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() => navigation.navigate(constants.ROUTE.DEALS)}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
						headerRight: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() =>
									navigation.dispatch(
										StackActions.push(constants.ROUTE.DEAL_FILTERS),
									)
								}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'filter-outline',
									color: 'white',
								}}
							/>
						),
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.COLLECTIONS_DEALS}
					component={CollectionDeals}
					options={{
						headerShown: true,
						headerShadowVisible: false,
						headerTitle: constants.SCREENS.COLLECTIONS_DEALS,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() => navigation.navigate(constants.ROUTE.DEALS)}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
						headerRight: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() =>
									navigation.dispatch(
										StackActions.push(constants.ROUTE.DEAL_FILTERS),
									)
								}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'filter-outline',
									color: 'white',
								}}
							/>
						),
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.DEAL_FILTERS}
					component={DealFilters}
					options={() => ({
						headerShown: true,
						headerTitle: constants.SCREENS.FILTERS,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
						headerRight: () => (
							<Button variant="unstyled" _text={{color: 'white', fontSize: 12}}>
								Clear All
							</Button>
						),
					})}
				/>
				<Stack.Screen
					name={constants.ROUTE.ADD_ADDRESS}
					component={AddAddress}
					options={{
						headerShown: true,
						headerTitle: constants.SCREENS.ADD_ADDRESS,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() => navigation.navigate(constants.ROUTE.DEALS)}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.SEARCH_DEALS}
					component={SearchDeal}
					options={({route}) => ({
						headerShown: true,
						headerTitle: route.params?.search,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() => navigation.navigate(constants.ROUTE.DEALS)}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
					})}
				/>
			</Stack.Navigator>
		</>
	);
};

export default DealsNavigation;

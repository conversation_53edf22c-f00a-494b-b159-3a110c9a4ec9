import React from 'react';
import {StatusBar} from 'react-native';
import {createStackNavigator} from '@react-navigation/stack';

import constants from '../utils/constants';
import SignIn from '../screens/SignIn';
import SignUp from '../screens/SignUp';
import ForgotPassword from '../screens/ForgotPassword';
import Verification from '../screens/Verification';
import BottomTabNavigation from './BottomTabNavigation';

const Stack = createStackNavigator();

const AuthNavigation = () => {
	return (
		<>
			<StatusBar
				backgroundColor={constants.COLORS.ORANGE}
				barStyle="light-content"
			/>
			<Stack.Navigator
				initialRouteName={constants.ROUTE.SIGN_IN}
				sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
				screenOptions={{
					cardStyle: { backgroundColor: '#fff' },
					animationEnabled: false
				}}>
				<Stack.Screen
					name={constants.ROUTE.SIGN_IN}
					component={SignIn}
					options={{
						headerShown: false,
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.SIGN_UP}
					component={SignUp}
					options={{
						headerShown: false,
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.FORGOT_PASSWORD}
					component={ForgotPassword}
					options={{
						headerShown: false,
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.VERIFICATION}
					component={Verification}
					options={{
						headerShown: false,
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.BOTTOM_NAVIGATION}
					component={BottomTabNavigation}
					options={{
						headerShown: false,
					}}
				/>
			</Stack.Navigator>
		</>
	);
};

export default AuthNavigation;

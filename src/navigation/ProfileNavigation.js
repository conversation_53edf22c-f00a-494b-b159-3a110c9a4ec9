import React, {useLayoutEffect} from 'react';

import {IconButton} from 'native-base';
import {createStackNavigator} from '@react-navigation/stack';
import {getFocusedRouteNameFromRoute} from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import constants from '../utils/constants';
import Profile from '../screens/Profile';
import EditProfile from '../screens/EditProfile';
import ChangePassword from '../screens/ChangePassword';
import Notifications from '../screens/Notifications';
import ContactUs from '../screens/ContactUs';
import EditBusinessProfile from '../screens/EditBusinessProfile';
import RequestProduct from '../screens/RequestAddProduct';

const Stack = createStackNavigator();

const ProfileNavigation = ({navigation, route}) => {
	useLayoutEffect(() => {
		const tabHiddenRoutes = [constants.ROUTE.EDIT_PROFILE];

		if (tabHiddenRoutes.includes(getFocusedRouteNameFromRoute(route))) {
			navigation.setOptions({tabBarStyle: {display: 'none'}});
		} else {
			navigation.setOptions({tabBarStyle: {display: 'flex'}});
		}
	}, [navigation, route]);

	return (
		<Stack.Navigator
			initialRouteName={constants.SCREENS.PROFILE}
			sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
			screenOptions={{
				headerStyle: {
					backgroundColor: constants.COLORS.ORANGE,
				},
				headerTitleStyle: {
					color: constants.COLORS.WHITE,
					fontSize: 18,
					letterSpacing: 0.5,
				},
				headerTitleAlign: 'center',
				cardStyle: {backgroundColor: '#fff'},
			}}>
			<Stack.Screen
				name={constants.ROUTE.PROFILE}
				component={Profile}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.PROFILE,
					headerLeft: () => null
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.EDIT_PROFILE}
				component={EditProfile}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.EDIT_PROFILE,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.PROFILE)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.EDIT_BUSINESS_PROFILE}
				component={EditBusinessProfile}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.EDIT_BUSINESS_PROFILE,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.PROFILE)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.REQUEST_ADD_PRODUCT}
				component={RequestProduct}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.REQUEST_ADD_PRODUCT,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.PROFILE)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.CHANGE_PASSWORD}
				component={ChangePassword}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.CHANGE_PASSWORD,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.PROFILE)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.NOTIFICATIONS}
				component={Notifications}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.NOTIFICATIONS,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.PROFILE)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.CONTACT_US}
				component={ContactUs}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.CONTACT_US,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.PROFILE)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
		</Stack.Navigator>
	);
};

export default ProfileNavigation;

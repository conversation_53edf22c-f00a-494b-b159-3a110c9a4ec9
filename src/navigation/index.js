import React, {useEffect, useState} from 'react';

import auth from '@react-native-firebase/auth';
import {useSelector} from 'react-redux';

import AuthNavigation from './AuthNavigation';
import BottomTabNavigation from './BottomTabNavigation';
import AgeVerificationNavigation from './AgeVerificationNavigation';
import {requestLocationPermission} from '../services/RNPermission';
import LocationNavigation from './LocationNavigation';
import constants from '../utils/constants';

const Navigation = () => {
	/**
	 * Start Initials
	 */
	// Set an initializing state whilst Firebase connects
	const [initializing, setInitializing] = useState(true);
	const [user, setUser] = useState();
	const [isLocationEnable, setIsLocationEnable] = useState(false);

	const ageVerified = useSelector(state => state.ageVerification.ageVerified);

	const storedAddress = useSelector(state => state.addAddress.address);
	const userData = useSelector(state => state.userSignIn.userData);
	const isGuestUser = useSelector(state => state.userSignIn.isGuestUser);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		(async () => {
			const locationPermission = await requestLocationPermission();
			setIsLocationEnable(locationPermission);
		})();

		auth().onAuthStateChanged(onAuthStateChanged);
	}, []);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	// Handle user state changes
	function onAuthStateChanged(val) {
		setUser(val);
		if (initializing) {
			setInitializing(false);
		}
	}
	/**
	 * End Methods
	 */
	if ((user && user?.emailVerified && userData?.status) || isGuestUser || (user && userData?.status && userData?.emailVerified)) {
		if (
			!isLocationEnable &&
			!storedAddress &&
			userData.role === constants.ROLE.CUSTOMER
		) {
			return <LocationNavigation />;
		}
		return <BottomTabNavigation />;
	}

	if (!ageVerified) {
		return <AgeVerificationNavigation />;
	}

	return <AuthNavigation />;
};

export default Navigation;

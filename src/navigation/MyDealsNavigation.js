import React, {useLayoutEffect} from 'react';

import {createStackNavigator} from '@react-navigation/stack';
import {getFocusedRouteNameFromRoute} from '@react-navigation/native';
import {IconButton} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import constants from '../utils/constants';
import MyDeals from '../screens/MyDeals';
import MyDealDetail from '../screens/MyDealDetail';
import EditDeal from '../screens/EditDeal';

const Stack = createStackNavigator();

const MyDealsNavigation = ({navigation, route}) => {
	useLayoutEffect(() => {
		const tabHiddenRoutes = [constants.ROUTE.EDIT_PROFILE];

		if (tabHiddenRoutes.includes(getFocusedRouteNameFromRoute(route))) {
			navigation.setOptions({tabBarStyle: {display: 'none'}});
		} else {
			navigation.setOptions({tabBarStyle: {display: 'flex'}});
		}
	}, [navigation, route]);

	return (
		<Stack.Navigator
			initialRouteName={constants.ROUTE.MY_DEALS}
			sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
			screenOptions={{
				headerStyle: {
					backgroundColor: constants.COLORS.ORANGE,
				},
				headerTitleStyle: {
					color: constants.COLORS.WHITE,
					fontSize: 18,
					letterSpacing: 0.5,
				},
				headerTitleAlign: 'center',
				cardStyle: {backgroundColor: '#fff'},
			}}>
			<Stack.Screen
				name={constants.ROUTE.MY_DEALS}
				component={MyDeals}
				options={{
					headerShown: false,
					headerTitle: constants.SCREENS.MY_DEALS,
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.MY_DEAL_DETAIL}
				component={MyDealDetail}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.MY_DEAL_DETAIL,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.MY_DEALS)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.EDIT_DEAL}
				component={EditDeal}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.EDIT_DEAL,
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.navigate(constants.ROUTE.MY_DEALS)}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
		</Stack.Navigator>
	);
};

export default MyDealsNavigation;

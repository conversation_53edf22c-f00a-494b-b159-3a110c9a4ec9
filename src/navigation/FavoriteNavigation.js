import React, {useLayoutEffect} from 'react';

import {IconButton} from 'native-base';
import {createStackNavigator} from '@react-navigation/stack';
import {
	getFocusedRouteNameFromRoute,
	StackActions,
} from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import constants from '../utils/constants';
import DealDetail from '../screens/DealDetail';
import ShopDetail from '../screens/ShopDetail';
import Favorite from '../screens/Favorite';

const Stack = createStackNavigator();

const ProfileNavigation = ({navigation, route}) => {
	useLayoutEffect(() => {
		const tabHiddenRoutes = [constants.ROUTE.EDIT_PROFILE];

		if (tabHiddenRoutes.includes(getFocusedRouteNameFromRoute(route))) {
			navigation.setOptions({tabBarStyle: {display: 'none'}});
		} else {
			navigation.setOptions({tabBarStyle: {display: 'flex'}});
		}
	}, [navigation, route]);

	return (
		<Stack.Navigator
			initialRouteName={constants.SCREENS.FAVORITE}
			sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
			screenOptions={{
				headerStyle: {
					backgroundColor: constants.COLORS.ORANGE,
				},
				headerTitleStyle: {
					color: constants.COLORS.WHITE,
					fontSize: 18,
					letterSpacing: 0.5,
				},
				headerTitleAlign: 'center',
				cardStyle: {backgroundColor: '#fff'},
			}}>
			<Stack.Screen
				name={constants.ROUTE.FAVORITE}
				component={Favorite}
				options={{
					headerShown: true,
					headerTitle: constants.SCREENS.FAVORITE,
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.DEAL_DETAIL}
				component={DealDetail}
				options={{
					headerShown: true,
					headerTitle: '',
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.dispatch(StackActions.pop())}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
			<Stack.Screen
				name={constants.ROUTE.SHOP_DETAILS}
				component={ShopDetail}
				options={{
					headerShown: true,
					headerTitle: '',
					headerLeft: () => (
						<IconButton
							variant="ghost"
							size="lg"
							onPress={() => navigation.dispatch(StackActions.pop())}
							_icon={{
								as: MaterialCommunityIcons,
								name: 'chevron-left',
								color: 'white',
							}}
						/>
					),
				}}
			/>
		</Stack.Navigator>
	);
};

export default ProfileNavigation;

import React from 'react';
import {StatusBar} from 'react-native';
import {createStackNavigator} from '@react-navigation/stack';

import constants from '../utils/constants';
import AgeVerification from '../screens/AgeVerification';

const Stack = createStackNavigator();

const AgeVerificationNavigation = () => {
	return (
		<>
			<StatusBar
				backgroundColor={constants.COLORS.ORANGE}
				barStyle="light-content"
			/>
			<Stack.Navigator
				initialRouteName={constants.ROUTE.AGE_VERIFICATION}
				sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
				screenOptions={{
					cardStyle: {backgroundColor: '#fff'},
				}}>
				<Stack.Screen
					name={constants.ROUTE.AGE_VERIFICATION}
					component={AgeVerification}
					options={{
						headerShown: false,
					}}
				/>
			</Stack.Navigator>
		</>
	);
};

export default AgeVerificationNavigation;

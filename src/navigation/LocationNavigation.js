import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {IconButton} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import constants from '../utils/constants';
import EnableLocation from '../screens/EnableLocation';
import ConfirmLocation from '../screens/ConfirmLocation';

const Stack = createStackNavigator();

const LocationNavigation = ({navigation}) => {
	return (
		<>
			<Stack.Navigator
				initialRouteName={constants.ROUTE.ENABLE_LOCATION}
				sceneContainerStyle={{backgroundColor: constants.COLORS.WHITE}}
				screenOptions={{
					headerStyle: {
						backgroundColor: constants.COLORS.ORANGE,
					},
					headerTitleStyle: {
						color: constants.COLORS.WHITE,
						fontSize: 18,
						letterSpacing: 0.5,
					},
					headerTitleAlign: 'center',
					cardStyle: {backgroundColor: '#fff'},
				}}>
				<Stack.Screen
					name={constants.ROUTE.ENABLE_LOCATION}
					component={EnableLocation}
					options={{
						headerShown: false,
					}}
				/>
				<Stack.Screen
					name={constants.ROUTE.CONFIRM_LOCATION}
					component={ConfirmLocation}
					options={{
						headerShown: true,
						headerTitle: constants.SCREENS.CONFIRM_LOCATION,
						headerLeft: () => (
							<IconButton
								variant="ghost"
								size="lg"
								onPress={() =>
									navigation.navigate(constants.ROUTE.ENABLE_LOCATION)
								}
								_icon={{
									as: MaterialCommunityIcons,
									name: 'chevron-left',
									color: 'white',
								}}
							/>
						),
					}}
				/>
			</Stack.Navigator>
		</>
	);
};

export default LocationNavigation;

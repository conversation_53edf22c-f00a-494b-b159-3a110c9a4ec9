import React, {useEffect} from 'react';
import {Provider} from 'react-redux';
import {NavigationContainer} from '@react-navigation/native';
import {NativeBaseProvider} from 'native-base/src/core/NativeBaseProvider';
import {PersistGate} from 'redux-persist/integration/react';
import SplashScreen from 'react-native-splash-screen';

import theme from './theme';
import Navigation from './navigation';
import {persistedStore, store} from './store';
import constants, {DEEP_LINK_BASE_URL} from './utils/constants';

const Root = () => {
	/**
	 * Start Initials
	 */

	// Configured the linking and redirection for the deep linking
	const linking = {
		prefixes: [DEEP_LINK_BASE_URL],
		config: {
			screens: {
				DealsNavigation: {
					screens: {
						DealDetail: constants.API_ROUTES.DEEP_LINK_PRODUCT_DETAILS,
					},
				},
			},
		},
	};

	useEffect(() => {
		SplashScreen.hide();
	}, []);
	/**
	 * End Initials
	 */

	return (
		<Provider store={store}>
			<PersistGate loading={null} persistor={persistedStore}>
				<NativeBaseProvider theme={theme}>
					<NavigationContainer linking={linking}>
						<Navigation />
					</NavigationContainer>
				</NativeBaseProvider>
			</PersistGate>
		</Provider>
	);
};

export default Root;

import axios from 'axios';
import 'react-native-get-random-values';
import {v4 as uuid} from 'uuid';
import {Toast} from 'native-base';
import {firebase} from '@react-native-firebase/auth';

import constants, {APP_API_URL} from '../utils/constants';
import {firebaseSignOut} from './firebaseAuth';

const apiCall = (requestData, requireAuth = false, file = false) => {
	return new Promise(async (resolve, reject) => {
		requestData.params = requestData.params || {};

		let apiHeaders = requestData.headers || {};
		if (requireAuth) {
			await firebase
				.auth()
				.currentUser.getIdTokenResult()
				.then(
					r =>
						(apiHeaders = {
							...apiHeaders,
							...{Authorization: `Bearer ${r.token}`},
						}),
				);
		}

		const request = {
			url: APP_API_URL + requestData.url,
			method: requestData.method || 'GET',
			headers: apiHeaders,
			params: requestData.params,
			data: JSON.stringify(requestData.data || undefined),
		};

		if (file) {
			request.headers['Content-Type'] = 'multipart/form-data';
			request.transformRequest = () => {
				return requestData.data; // this is doing the trick
			};
		} else {
			request.headers['Content-Type'] = 'application/json';
		}
		request.headers['correlation-id'] = uuid();

		axios(request)
			.then(function (response) {
				resolve(response);
			})
			.catch(function (error) {
				if (error.code === 'ERR_NETWORK') {
					//server down error
					Toast.show({
						avoidKeyboard: true,
						title: constants.ERROR_MESSAGE.SERVER_DOWN,
					});
				} else if (error.code === 'ERR_BAD_RESPONSE') {
					//internal server error
					Toast.show({
						avoidKeyboard: true,
						title: constants.ERROR_MESSAGE.INTERNAL_SERVER_ERROR,
					});
				} else if (error?.response?.status === 401) {
					//internal server error
					Toast.show({
						avoidKeyboard: true,
						title: constants.ERROR_MESSAGE.SESSION_EXPIRED,
					});
					firebaseSignOut();
				}
				reject(error.response ? error.response : error);
			});
	});
};

export default apiCall;

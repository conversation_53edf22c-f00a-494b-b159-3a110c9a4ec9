import {APP_GOOGLE_MAP_API_KEY} from '../utils/constants';

export const GooglePlaceAutoComplete = async text => {
	return fetch(
		`https://maps.googleapis.com/maps/api/place/autocomplete/json?key=${APP_GOOGLE_MAP_API_KEY}&input=${text}`,
	)
		.then(response => response.json())
		.then(json => {
			return json.predictions;
		})
		.catch(() => {
			return;
		});
};

export const GooglePlaceCoordinates = async place_id => {
	return fetch(
		`https://maps.googleapis.com/maps/api/place/details/json?place_id=${place_id}&key=${APP_GOOGLE_MAP_API_KEY}`,
	)
		.then(response => response.json())
		.then(json => {
			return {
				fullName: json.result.formatted_address,
				coord: json.result.geometry.location,
			};
		})
		.catch(() => {
			return;
		});
};

export const GooglePlaceName = async (lat, log) => {
	return await fetch(
		`https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${log}&sensor=true&key=${APP_GOOGLE_MAP_API_KEY}`,
	)
		.then(response => response.json())
		.then(json => {
			if (json.status === 'OK') {
				return {
					fullName: json?.results[0]?.formatted_address,
					coord: json?.results[0]?.geometry.location,
				};
			} else {
				return {
					fullName: '',
					coord: {
						lat: 0,
						lng: 0,
					},
				};
			}
		})
		.catch(e => {
			return e;
		});
};

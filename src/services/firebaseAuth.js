import auth from '@react-native-firebase/auth';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {
	AccessToken,
	GraphRequest,
	GraphRequestManager,
	LoginManager,
	Settings,
} from 'react-native-fbsdk-next';

import constants, {
	APP_FACEBOOK_APP_ID,
	APP_GOOGLE_WEB_CLIENT_ID,
} from '../utils/constants';
import appleAuth from '@invertase/react-native-apple-authentication';

Settings.setAppID(APP_FACEBOOK_APP_ID);

/**
 * Authenticate a user with Firebase using email and password.
 * @param {Object} data - User authentication data, including email and password.
 * @returns {Promise} A promise that resolves with the authenticated user's data or rejects with an error message.
 */
export const firebaseSignIn = data => {
	const result = new Promise((resolve, reject) => {
		// Sign in with email and password using Firebase auth()
		auth()
			.signInWithEmailAndPassword(data.email, data.password)
			.then(userCredential => {
				const user = userCredential.user;

				// Check if the user's email is not verified
				if (!user.emailVerified) {
					reject(constants.ERROR_MESSAGE.EMAIL_NOT_VERIFIED);
				} else {
					// Resolve with the user data
					resolve(user);
				}
			})
			.catch(error => {
				// Map Firebase error codes to custom error messages
				const errorMap = {
					'auth/invalid-credential':
						constants.ERROR_MESSAGE.INVALID_CREDENTIALS,
					'auth/invalid-email': constants.ERROR_MESSAGE.INVALID_CREDENTIALS,
					'auth/wrong-password': constants.ERROR_MESSAGE.INVALID_CREDENTIALS,
					'auth/user-not-found': constants.ERROR_MESSAGE.INVALID_CREDENTIALS,
					'auth/too-many-requests': constants.ERROR_MESSAGE.CONNECTION_TIMEOUT,
					'auth/timeout': constants.ERROR_MESSAGE.CONNECTION_TIMEOUT,
					'auth/internal-error': constants.ERROR_MESSAGE.INTERNAL_SERVER_ERROR,
				};

				// Reject with the corresponding error message or a generic server error
				reject(errorMap[error.code] || constants.ERROR_MESSAGE.SERVER_ERROR);
			});
	});
	return result;
};

/**
 * Create a new user account with Firebase using email and password.
 * @param {Object} data - User registration data, including email and password.
 * @returns {Promise} A promise that resolves when the user is successfully registered, or rejects with an error message.
 */
export const firebaseSignUp = data => {
	return new Promise((resolve, reject) => {
		// Create a new user with email and password using Firebase auth()
		auth()
			.createUserWithEmailAndPassword(data.email, data.password)
			.then(userCredential => {
				const user = userCredential.user;

				// Send email verification to the newly registered user
				user
					.sendEmailVerification()
					.then(() => {
						// Resolve when email verification is sent successfully
						resolve(user);
					})
					.catch(() => {
						// Reject with a server error if email verification sending fails
						reject(constants.ERROR_MESSAGE.SERVER_ERROR);
					});
			})
			.catch(error => {
				// Map Firebase error codes to custom error messages
				const errorMap = {
					'auth/email-already-in-use': constants.ERROR_MESSAGE.ALREADY_EXISTS,
					'auth/invalid-email': constants.ERROR_MESSAGE.INVALID_EMAIL,
					'auth/too-many-requests': constants.ERROR_MESSAGE.INVALID_CREDENTIALS,
					'auth/timeout': constants.ERROR_MESSAGE.CONNECTION_TIMEOUT,
				};

				// Reject with the corresponding error message or a generic server error
				reject(errorMap[error.code] || constants.ERROR_MESSAGE.SERVER_ERROR);
			});
	});
};

/**
 * Sign in with Google using Firebase authentication.
 * @returns {Promise} A promise that resolves with user information or rejects with an error message.
 */
export const firebaseGoogleSignIn = async () => {
	return new Promise(async (resolve, reject) => {
		try {
			// Configure Google Sign-In with your web client ID
			await GoogleSignin.configure({
				webClientId: APP_GOOGLE_WEB_CLIENT_ID,
			});

			// Check if your device supports Google Play services
			await GoogleSignin.hasPlayServices({
				showPlayServicesUpdateDialog: true,
			});

			// Sign in with Google
			const userInfo = await GoogleSignin.signIn();

			// Resolve with user information
			resolve(userInfo);
		} catch (error) {
			if (error.code === -5) {
				reject({
					message: constants.ERROR_MESSAGE.SIGN_IN_CANCELED,
				});
			} else {
				// Reject with the encountered error
				reject(error);
			}
		}
	});
};

/**
 * Sign in a user using Google credentials.
 * @param {string} idToken - The Google ID token for authentication.
 * @returns {Promise} A promise that resolves when the user is signed in or rejects with an error message.
 */
export const signInWithGoogleCredentials = idToken => {
	return new Promise((resolve, reject) => {
		// Create a Google credential with the provided ID token
		const googleCredential = auth.GoogleAuthProvider.credential(idToken);

		// Sign in the user with the Google credential
		auth()
			.signInWithCredential(googleCredential)
			.then(userCredential => {
				resolve(userCredential);
			})
			.catch(error => {
				// Handle the error by rejecting with an error message
				reject(error);
			});
	});
};

/**
 * Sign out the current user using Firebase authentication.
 * @returns {Promise} A promise that resolves when the user is signed out or rejects with an error message.
 */
export const firebaseSignOut = () => {
	return new Promise((resolve, reject) => {
		// Sign out the current user using Firebase auth()
		auth()
			.signOut()
			.then(() => resolve('User Signed Out'))
			.catch(error => {
				if (error.code === 'auth/no-current-user') {
					reject(constants.ERROR_MESSAGE.SESSION_EXPIRED);
				} else {
					// Reject with a generic server error if the error is not explicitly handled
					reject(constants.ERROR_MESSAGE.SERVER_ERROR);
				}
			});
	});
};

/**
 * Send a password reset link to the user's email using Firebase authentication.
 * @param {string} email - The email address for which to send the reset link.
 * @returns {Promise} A promise that resolves when the reset link is sent or rejects with an error message.
 */
export const firebaseResetPasswordLink = email => {
	return new Promise((resolve, reject) => {
		// Send a password reset email using Firebase auth()
		auth()
			.sendPasswordResetEmail(email)
			.then(() => {
				resolve(constants.SUCCESS_MESSAGE.PASSWORD_RESET_LINK_SENT);
			})
			.catch(error => {
				// Handle specific error conditions with meaningful error messages
				if (error.code === 'auth/user-not-found') {
					reject(constants.ERROR_MESSAGE.USER_NOT_FOUND);
				} else {
					// Reject with a generic server error if the error is not explicitly handled
					reject(constants.ERROR_MESSAGE.SERVER_ERROR);
				}
			});
	});
};

/**
 * Sign in a user using Facebook credentials.
 * @returns {Promise} A promise that resolves with user information or rejects with an error message.
 */
export async function firebaseFacebookSignIn() {
	try {
		// Attempt login with permissions
		const result = await LoginManager.logInWithPermissions([
			'email',
			'public_profile',
		]);

		if (result.isCancelled) {
			throw {message: constants.ERROR_MESSAGE.SIGN_IN_CANCELED};
		}

		// Get the current access token
		const data = await AccessToken.getCurrentAccessToken();
        
		// Fetch the user's email and name from Facebook
		const user = await fetchFacebookUserEmailName(data.accessToken);

		return {
			idToken: data.accessToken,
			user: user,
		};
	} catch (error) {
		// Handle any encountered errors by rejecting with an error message
		return Promise.reject(error);
	}
}

/**
 * Sign in a user using Apple credentials.
 * @returns {Promise} A promise that resolves when the user is signed in or rejects with an error message.
 */
export async function firebaseAppleSignIn() {
	try {
		// Attempt to login using apple account
		const appleSignInAuthResult = await appleAuth.performRequest({
			requestedOperation: appleAuth.Operation.LOGIN,
			requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
		});

		// Getting the identity token nonce and user email for check the user in our database.
		const { identityToken, nonce } = appleSignInAuthResult;

		// Create a Firebase credential with the token
		const appleCredential = auth.AppleAuthProvider.credential(identityToken, nonce);

		 // Sign-in the user with Firebase credential
		const userCredential = await auth().signInWithCredential(appleCredential);

		// Return the user data 
		return {
			user: userCredential.user
		}
	} catch (error) {
		// Handle any encountered errors by rejecting with an error message
		return Promise.reject(error);
	}
}

/**
 * Sign in a user using Facebook credentials.
 * @param {string} idToken - The Facebook ID token for authentication.
 * @returns {Promise} A promise that resolves when the user is signed in or rejects with an error message.
 */
export const signInWithFacebookCredentials = idToken => {
	return new Promise((resolve, reject) => {
		// Create a Facebook credential with the provided ID token
		const facebookCredential = auth.FacebookAuthProvider.credential(idToken);

		// Sign in the user with the Facebook credential
		auth()
			.signInWithCredential(facebookCredential)
			.then(userCredential => {
				resolve(userCredential);
			})
			.catch(error => {
				// Handle the error by rejecting with the encountered error
				reject(error);
			});
	});
};

/**
 * Fetch the user's email and name from Facebook using the provided ID token.
 * @param {string} idToken - The Facebook ID token for authentication.
 * @returns {Promise} A promise that resolves with user information or rejects with an error message.
 */
export const fetchFacebookUserEmailName = idToken => {
	return new Promise((resolve, reject) => {
		const infoRequest = new GraphRequest(
			'/me',
			{
				httpMethod: 'GET',
				version: 'v2.5',
				parameters: {
					fields: {
						string: 'email,name',
					},
					access_token: {
						string: idToken,
					},
				},
			},
			(error, result) => {
				if (error) {
					reject(error);
				} else {
					resolve(result);
				}
			},
		);

		new GraphRequestManager().addRequest(infoRequest).start();
	});
};

/**
 * Change the user's password in Firebase after verifying the current password.
 * @param {string} newPassword - The new password.
 * @param {string} currentPassword - The current password for verification.
 * @returns {Promise} A promise that resolves when the password is successfully changed or rejects with an error message.
 */
export const firebaseChangePassword = (newPassword, currentPassword) => {
	return new Promise((resolve, reject) => {
		// Get the current user
		const currentUser = auth().currentUser;

		// Verify the old password before updating the user's password
		const credential = auth.EmailAuthProvider.credential(
			currentUser.email,
			currentPassword,
		);

		currentUser
			.reauthenticateWithCredential(credential)
			.then(() => {
				// The old password is correct, update the user's password
				currentUser
					.updatePassword(newPassword)
					.then(() => {
						resolve(constants.SUCCESS_MESSAGE.PASSWORD_CHANGED_SUCCESSFULLY);
					})
					.catch(error => {
						// Handle specific error conditions with meaningful error messages
						const errorMap = {
							'auth/weak-password': constants.ERROR_MESSAGE.WEAK_PASSWORD,
							'auth/requires-recent-login':
								constants.ERROR_MESSAGE.REQUEST_RECENT_LOGIN,
							'auth/network-request-failed':
								constants.ERROR_MESSAGE.NETWORK_ERROR,
						};

						// Reject with the corresponding error message or a generic server error
						reject(
							errorMap[error.code] || constants.ERROR_MESSAGE.SERVER_ERROR,
						);
					});
			})
			.catch(error => {
				// Handle specific error conditions with meaningful error messages
				const errorMap = {
					'auth/wrong-password': constants.ERROR_MESSAGE.WRONG_PASSWORD,
					'auth/requires-recent-login':
						constants.ERROR_MESSAGE.REQUEST_RECENT_LOGIN,
					'auth/network-request-failed': constants.ERROR_MESSAGE.NETWORK_ERROR,
				};

				// Reject with the corresponding error message or a generic server error
				reject(errorMap[error.code] || constants.ERROR_MESSAGE.SERVER_ERROR);
			});
	});
};

import {Platform} from 'react-native';
import {PERMISSIONS, request, RESULTS} from 'react-native-permissions';

export const requestLocationPermission = async () => {
	let permissionStatus;
	if (Platform.OS === 'ios') {
		permissionStatus = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
	} else {
		permissionStatus = await request(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
	}

	return !(
		permissionStatus === RESULTS.DENIED ||
		permissionStatus === RESULTS.UNAVAILABLE
	);
};

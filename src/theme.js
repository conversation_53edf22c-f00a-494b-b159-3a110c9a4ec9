import {extendTheme} from 'native-base';
import {Platform} from 'react-native';

import constants from './utils/constants';

const theme = extendTheme({
	config: {
		useSystemColorMode: false,
		initialColorMode: 'light',
	},
	fonts: {
		heading: 'Quicksand',
		body: 'Quicksand',
		mono: 'Quicksand',
	},
	colors: {
		custom: {
			orange: constants.COLORS.ORANGE,
			lightOrange: constants.COLORS.LIGHT_ORANGE,
			green: constants.COLORS.GREEN,
			red: constants.COLORS.RED,
			black: constants.COLORS.BLACK,
			dark1: constants.COLORS.DARK1,
			grey1: constants.COLORS.GREY1,
			grey2: constants.COLORS.GREY2,
			grey3: constants.COLORS.GREY3,
			grey4: constants.COLORS.GREY4,
			grey5: constants.COLORS.GREY5,
			grey6: constants.COLORS.GREY6,
		},
	},
	components: {
		Heading: {
			defaultProps: {
				fontSize: 32,
				fontWeight: Platform.select({ios: '600', android: 600}),
				color: 'custom.dark1',
				letterSpacing: 0.5,
			},
		},
		Icon: {
			defaultProps: {
				color: 'custom.dark1',
			},
		},
		IconButton: {
			defaultProps: {
				colorScheme: 'custom.orange',
				borderColor: 'custom.orange',
				borderRadius: 50,
				_icon: {
					color: 'custom.orange',
				},
			},
		},
		Button: {
			defaultProps: {
				borderRadius: 10,
				_text: {
					fontSize: 16,
					fontWeight: Platform.select({ios: '600', android: 600}),
				},
			},
			variants: {
				solid: {
					bg: 'custom.orange',
					_pressed: {
						bg: 'orange.600',
					},
				},
				subtle: {
					bg: 'custom.lightOrange',
					_pressed: {
						bg: 'orange.600',
					},
				},
				outline: {
					borderColor: 'custom.orange',
					_text: {
						color: 'custom.dark1',
					},
					_pressed: {
						bg: 'orange.600',
					},
				},
				ghost: {
					_text: {
						color: 'custom.orange',
					},
					_pressed: {
						bg: 'custom.lightOrange',
					},
				},
			},
		},
		Link: {
			baseStyle: {
				_text: {
					textDecoration: 'none',
					fontSize: 15,
					fontWeight: Platform.select({ios: '600', android: 600}),
					color: 'custom.orange',
				},
			},
		},
		Text: {
			baseStyle: {
				fontSize: 15,
				fontWeight: Platform.select({ios: '500', android: 500}),
				color: 'custom.dark1',
				letterSpacing: 0.5,
			},
		},
		Input: {
			defaultProps: {
				py: 0,
				borderColor: 'custom.grey3',
				variant: 'underlined',
				color: 'custom.dark1',
				placeholderTextColor: 'grey',
				fontWeight: Platform.select({ios: '500', android: 500}),
			},
			variants: {
				underlined: {
					_focus: {
						borderColor: 'custom.orange',
						_hover: {borderColor: 'custom.orange'},
					},
				},
			},
		},
		TextArea: {
			defaultProps: {
				_focus: {
					borderColor: 'custom.orange',
					_hover: {borderColor: 'custom.orange'},
				},
				color: 'custom.dark1',
				fontSize: 16,
				fontWeight: Platform.select({ios: '500', android: 500}),
				backgroundColor: 'white',
			},
		},
		Checkbox: {
			defaultProps: {
				_checked: {
					borderColor: 'custom.orange',
					bg: 'custom.orange',
					_hover: {
						borderColor: 'custom.orange',
						bg: 'custom.orange',
						_disabled: {
							borderColor: 'custom.orange',
							bg: 'custom.orange',
						},
					},
					_pressed: {
						borderColor: 'custom.orange',
						bg: 'custom.orange',
					},
				},
			},
		},
		FormControlLabel: {
			baseStyle: {
				_text: {
					fontSize: 14,
					color: 'custom.grey5',
				},
			},
		},
	},
});

export default theme;

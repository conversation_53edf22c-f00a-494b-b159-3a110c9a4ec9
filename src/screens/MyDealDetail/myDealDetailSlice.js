import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getDealDetail = createAsyncThunk(
	'myDealDetail/getDealDetail',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.MY_DEAL_DETAIL + `/${data}`,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const myDealDetailSlice = createSlice({
	name: 'myDealDetail',
	initialState: {},
});

export default myDealDetailSlice.reducer;

import React, {useCallback, useState} from 'react';
import {
	Box,
	Text,
	Heading,
	Center,
	HStack,
	Image,
	VStack,
	Divider,
	Skeleton,
	ScrollView,
} from 'native-base';
import {Dimensions, Platform} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import DeviceInfo from 'react-native-device-info';
import dayjs from 'dayjs';

import constants from '../../utils/constants';
import {toUri} from '../../utils/helpers';
import DealModel from '../../models/dealModel';
import {getDealDetail} from './myDealDetailSlice';

const {height: viewportHeight} = Dimensions.get('window');

const MyDealDetail = ({route}) => {
	/**
	 * Start Initials
	 */
	const [loading, setLoading] = useState(DealModel);
	const [dealDetail, setDealDetail] = useState(DealModel);

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchDealDetail();
		}, []),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchDealDetail = () => {
		setLoading(true);
		dispatch(getDealDetail(route.params.dealId))
			.unwrap()
			.then(res => {
				setDealDetail(res);
				setLoading(false);
			})
			.catch(() => setLoading(false));
	};
	/**
	 * End Methods
	 */
	return (
		<ScrollView>
			<Center w="100%" backgroundColor="white">
				<Box
					w="90%"
					h={
						viewportHeight -
						Platform.select({
							ios: DeviceInfo.hasNotch() ? 200 : 40,
							android: 55,
						})
					}>
					<Box mb={4} mt={8}>
						<Center>
							<Skeleton h={255} startColor="custom.orange" isLoaded={!loading}>
								<Image
									source={{uri: toUri(dealDetail.product.photo)}}
									alt="image"
									resizeMode="contain"
									size="2xl"
								/>
							</Skeleton>
						</Center>
					</Box>
					<VStack space={2}>
						<Skeleton startColor="custom.orange" isLoaded={!loading}>
							<Heading fontSize={18} bold>
								{dealDetail.product.name}
							</Heading>
						</Skeleton>

						<HStack justifyContent="space-between">
							<Skeleton w="30%" startColor="custom.orange" isLoaded={!loading}>
								<VStack>
									<Text color="custom.orange">{constants.LABEL.PRICE}</Text>
									<HStack space={2}>
										{['fix', 'percent'].includes(dealDetail.discountType) && (
											<Text strikeThrough color="custom.red">
												${dealDetail.productPrice}
											</Text>
										)}
										<Text bold color="custom.green">
											${dealDetail.discountPrice}
										</Text>
									</HStack>
								</VStack>
							</Skeleton>
							<Skeleton w="25%" startColor="custom.orange" isLoaded={!loading}>
								<VStack>
									<Text color="custom.orange">{constants.LABEL.SIZE}</Text>
									<Text>{dealDetail.product.size} ml</Text>
								</VStack>
							</Skeleton>
						</HStack>

						<Divider bg="custom.grey3" thickness={1} my={1} />
						<Skeleton startColor="custom.orange" isLoaded={!loading}>
							<HStack justifyContent="space-between">
								<Text>{constants.LABEL.TYPE}</Text>
								<Text
									color="gray.600"
									backgroundColor={'red.900'}
									style={{textTransform: 'capitalize'}}>
									{dealDetail.type}
								</Text>
							</HStack>
						</Skeleton>

						<Divider bg="custom.grey3" thickness={1} my={1} />

						<Skeleton startColor="custom.orange" isLoaded={!loading}>
							<HStack justifyContent="space-between">
								<Text>{constants.LABEL.DISCOUNT}</Text>
								{['fix', 'percent', 'quantity'].includes(
									dealDetail.discountType,
								) ? (
									<Text color="gray.600">
										{dealDetail.discountType === 'percent'
											? `${dealDetail.discountValue}%`
											: `$${dealDetail.discountValue}`}
									</Text>
								) : (
									<Text color="gray.600">{dealDetail.discountType}</Text>
								)}
							</HStack>
						</Skeleton>

						<Divider bg="custom.grey3" thickness={1} my={1} />
						<Skeleton startColor="custom.orange" isLoaded={!loading}>
							<HStack justifyContent="space-between">
								<Text>{constants.LABEL.DEAL_ENDS_ON}</Text>
								<Text color="gray.600">
									{dealDetail.expiryDateTime &&
										dayjs(dealDetail.expiryDateTime).format(
											'MM-DD-YYYY hh:mm A',
										)}
								</Text>
							</HStack>
						</Skeleton>

						<Divider bg="custom.grey3" thickness={1} my={1} />

						<Skeleton startColor="custom.orange" isLoaded={!loading}>
							<HStack justifyContent="space-between">
								<Text>{constants.LABEL.LOW_STOCK}</Text>
								<Text color="gray.600">
									{dealDetail.lowStock ? 'YES' : 'NO'}
								</Text>
							</HStack>
						</Skeleton>

						<Divider bg="custom.grey3" thickness={1} my={1} />

						<Skeleton startColor="custom.orange" isLoaded={!loading}>
							<HStack justifyContent="space-between">
								<Text>{constants.LABEL.PACK_OF}</Text>
								<Text color="gray.600">{dealDetail.quantity}</Text>
							</HStack>
						</Skeleton>
						<Divider bg="custom.grey3" thickness={1} my={1} />
					</VStack>
				</Box>
			</Center>
		</ScrollView>
	);
};

export default MyDealDetail;

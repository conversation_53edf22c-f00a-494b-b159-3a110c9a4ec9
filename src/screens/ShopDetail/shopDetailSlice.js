import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import constants from '../../utils/constants';
import apiCall from '../../services/apiClient';

export const getShopDetail = createAsyncThunk(
	'shopDetail/getShopDetail',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_STORE + `/${data}`,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);
export const getRetailerDeals = createAsyncThunk(
	'shopDetail/getRetailerDeals',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_STORE_RELATED_DEALS + `/${data}/deals`,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

const shopDetailSlice = createSlice({
	name: 'shopDetail',
	initialState: {},
});

export default shopDetailSlice.reducer;

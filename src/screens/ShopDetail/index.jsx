import React, {useEffect, useState} from 'react';
import {Box, Center, Divider, ScrollView, Skeleton} from 'native-base';

import ShopDetailCard from './ShopDetailCard';
import DealCard from './DealCard';
import constants from '../../utils/constants';
import {TouchableOpacity} from 'react-native';
import {useDispatch} from 'react-redux';
import {getRetailerDeals, getShopDetail} from './shopDetailSlice';
import businessModel from '../../models/businessModel';
import {StackActions} from '@react-navigation/native';

const ShopDetail = ({navigation, route}) => {
	/**
	 * Start Initials
	 */
	const [shop, setShop] = useState(businessModel);
	const [deals, setDeals] = useState([]);
	const [isLoadedStore, setIsLoadedStore] = useState(true);
	const [isLoadedDeals, setIsLoadedDeals] = useState(true);

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		fetchShopDetail();
		fetchRetailerDeals();
	}, []);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchShopDetail = () => {
		setIsLoadedStore(false);
		dispatch(getShopDetail(route.params.shopId))
			.unwrap()
			.then(res => {
				setShop(res);
				setIsLoadedStore(true);
			});
	};

	const fetchRetailerDeals = () => {
		setIsLoadedDeals(false);
		dispatch(getRetailerDeals(route.params.shopId))
			.unwrap()
			.then(res => {
				setDeals(res);
				setIsLoadedDeals(true);
			});
	};
	/**
	 * End Methods
	 */
	return (
		<ScrollView>
			<Center w="100%" bg="white">
				<Box my={2} w="90%">
					<Box mb={3}>
						<ShopDetailCard shop={shop} isLoadedStore={isLoadedStore} />
					</Box>

					<Skeleton startColor="custom.orange" isLoaded={isLoadedDeals}>
						{deals.map(item => (
							<Box key={item._id}>
								<Divider bg="custom.grey3" thickness={1} my={1} />
								<TouchableOpacity
									onPress={() =>
										navigation.dispatch(
											StackActions.push(constants.ROUTE.DEAL_DETAIL, {
												dealId: item._id,
												productId: item.product._id,
												productName: item.product.name,
											}),
										)
									}>
									<DealCard
										productName={item.product?.name}
										productPhoto={item.product?.photo}
										productPrice={item.productPrice}
										discountPrice={item.discountPrice}
										discountType={item.discountType}
										discountValue={item.discountValue}
										productSize={item.product?.size}
										type={item.type}
										lowStock={item.lowStock}
										quantity={item.quantity}
									/>
								</TouchableOpacity>
							</Box>
						))}
					</Skeleton>
				</Box>
			</Center>
		</ScrollView>
	);
};

export default ShopDetail;

import React from 'react';
import {Box, Center, Flex, HStack, Image, Stack, Text} from 'native-base';
import {Platform} from 'react-native';

import {toUri} from '../../utils/helpers';
import constants from '../../utils/constants';

const DealCard = props => {
	/**
	 * Start Initials
	 */
	const {
		quantity,
		productName,
		productPhoto,
		productPrice,
		discountPrice,
		discountType,
		discountValue,
		productSize,
		lowStock,
	} = props;
	/**
	 * End Initials
	 */

	return (
		<Center w="100%">
			<Box w="100%">
				<HStack>
					<Box w="35%" alignItems="center">
						<Text
							position="absolute"
							top={2}
							left={0}
							color="custom.red"
							fontSize={10}
							zIndex={10}
							bold>
							{lowStock && 'Low \nStock'}
						</Text>
						<Box>
							<Center>
								<Image
									source={{uri: toUri(productPhoto)}}
									alt="image"
									resizeMode="contain"
									size="lg"
								/>
							</Center>
						</Box>
					</Box>

					<Stack py={1} space={2} w="65%">
						<Stack space={2}>
							<Text
								fontSize={16}
								fontWeight={Platform.select({ios: '400', android: 400})}
								isTruncated>
								{productName}
							</Text>
							<Flex flexDirection={'row'}>
								<Text fontSize={13}>{productSize} ml</Text>
								{quantity > 1 ? (
									<Text
										bg={'#F5F5F5'}
										borderRadius={8}
										px={2}
										ml={2}
										fontSize={12}>
										Pack of {quantity}
									</Text>
								) : null}
							</Flex>
						</Stack>

						<HStack space={2}>
							{[
								constants.DEAL_TYPE.FIX,
								constants.DEAL_TYPE.PERCENT,
								constants.DEAL_TYPE.QUANTITY,
							].includes(discountType) && (
								<Text fontSize={13} strikeThrough color="custom.red">
									${productPrice}
								</Text>
							)}
							<Text fontSize={15} bold color="custom.green">
								${discountPrice}
							</Text>
						</HStack>
						<Center alignItems="flex-start">
							<Box bg="custom.green" px={1} borderRadius={2} zIndex={1}>
								{[
									constants.DEAL_TYPE.FIX,
									constants.DEAL_TYPE.PERCENT,
									constants.DEAL_TYPE.QUANTITY,
								].includes(discountType) ? (
									<>
										<Text color="white" fontSize={11} bold>
											{discountType === constants.DEAL_TYPE.PERCENT ||
											discountType === constants.DEAL_TYPE.QUANTITY
												? `${discountValue}%`
												: `$${discountValue}`}{' '}
											OFF
										</Text>
									</>
								) : (
									<Text color="white" fontSize={11} bold>
										{discountType}
									</Text>
								)}
							</Box>
						</Center>
					</Stack>
				</HStack>
			</Box>
		</Center>
	);
};

export default DealCard;

import React, { useCallback, useEffect, useState } from 'react';
import { Linking, Platform } from 'react-native';
import {
	Box,
	Text,
	Heading,
	Center,
	HStack,
	Image,
	VStack,
	Divider,
	Icon,
	Skeleton,
	Button,
	Alert,
	useToast,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import { toUri } from '../../utils/helpers';
import ToastAlert from '../../components/ToastAlert';

const ShopDetailCard = props => {
	/**
	 * Start Initials
	 */
	const { shop, isLoadedStore } = props;

	const toast = useToast();
	const [storeTimings, setStoreTimings] = useState('');
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */

	useEffect(() => {
		const shopTiming = shop.timing ? JSON.parse(shop.timing) : '';
		setStoreTimings(shopTiming);
	}, [props]);

	const openWebsiteUrl = useCallback(async () => {
		// Checking if the link is supported for links with custom URL scheme.
		const supported = await Linking.canOpenURL(shop.websiteUrl);

		if (supported) {
			// Opening the link with some app, if the URL scheme is "http" the web link should be opened
			// by some browser in the mobile
			await Linking.openURL(shop.websiteUrl);
		} else {
			toast.show({
				avoidKeyboard: true,
				render: ({ id }) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={`Don't know how to open this URL: ${shop.websiteUrl}`}
						/>
					);
				},
			});
		}
	}, [shop.websiteUrl]);
	/**
	 * End Methods
	 */

	return (
		<Center w="100%">
			<Box w="100%">
				<Box mb={3}>
					<Center>
						<Skeleton
							h="220"
							startColor="custom.orange"
							isLoaded={isLoadedStore}>
							<Image
								source={{ uri: toUri(shop.photos[0]) }}
								alt="image"
								resizeMode={Platform.OS === 'android' ? 'cover' : 'center'}
								size={Platform.OS === 'android' ? 320 : '2xl'}
							/>
						</Skeleton>
					</Center>
				</Box>
				<VStack space={2}>
					<Skeleton startColor="custom.orange" isLoaded={isLoadedStore}>
						<Heading fontSize={18} bold>
							{shop.name}
						</Heading>
					</Skeleton>

					<HStack justifyContent="space-between">
						<Skeleton
							width="30%"
							startColor="custom.orange"
							isLoaded={isLoadedStore}>
							<HStack space={2}>
								<Text fontSize={16} bold>
									{shop.ratings ? shop.ratings : 0}
								</Text>
								<HStack alignItems="center">
									{[...Array(Number(shop.ratings || 0)).keys()].map(i => (
										<Icon
											key={i}
											color="custom.orange"
											size="sm"
											as={MaterialCommunityIcons}
											name="star"
										/>
									))}
									{[...Array(5 - Number(shop.ratings || 0)).keys()].map(i => (
										<Icon
											key={i}
											color="grey"
											size="sm"
											as={MaterialCommunityIcons}
											name="star"
										/>
									))}
								</HStack>
							</HStack>
						</Skeleton>
						<Skeleton
							width="30%"
							startColor="custom.orange"
							isLoaded={isLoadedStore}>
							{
								shop.websiteUrl && <Button
									size="xs"
									borderRadius={5}
									px={2}
									py={1}
									_text={{ fontSize: 11, color: 'white' }}
									bold
									onPress={openWebsiteUrl}>
									Visit Site
								</Button>
							}

						</Skeleton>
					</HStack>

					<Divider bg="custom.grey3" thickness={1} />
					<Skeleton startColor="custom.orange" isLoaded={isLoadedStore}>
						<Text fontSize={13} flexShrink={1}>
							{shop.description}
						</Text>
					</Skeleton>

					<Skeleton startColor="custom.orange" isLoaded={isLoadedStore}>
						<HStack space={1}>
							<Text fontSize={13} bold>
								Service options:
							</Text>
							<Text fontSize={13} flexShrink={1}>
								{shop.serviceOptions.join(',')}
							</Text>
						</HStack>
					</Skeleton>

					<Skeleton startColor="custom.orange" isLoaded={isLoadedStore}>
						<HStack space={1}>
							<Text fontSize={13} bold>
								Address:
							</Text>
							<Text fontSize={13} flexShrink={1}>
								{shop.streetAddress}, {shop.city}, {shop.state}, {shop.country}{' '}
								- {shop.zipCode}
							</Text>
						</HStack>
					</Skeleton>

					<Skeleton startColor="custom.orange" isLoaded={isLoadedStore}>
						<Text fontSize={13} bold>
							Opening Hours:
						</Text>
						<VStack space={1} my={1}>
							{Object.entries(storeTimings).map(([key, value]) => {
								return (
									<HStack key={key}>
										<Text fontSize={11} w={60}>
											{key.toUpperCase()}
										</Text>
										{!!value?.closed ? (
											<Text fontSize={11}>CLOSED</Text>
										) : (
											<Text fontSize={12}>
												{value.opensAt} to {value.closesAt}
											</Text>
										)}
									</HStack>
								);
							})}
						</VStack>
					</Skeleton>

					<Skeleton startColor="custom.orange" isLoaded={isLoadedStore}>
						<HStack space={1}>
							<Text fontSize={13} bold>
								Email:
							</Text>
							<Text fontSize={13} color="custom.orange">
								{shop.email}
							</Text>
						</HStack>
					</Skeleton>

					<Skeleton startColor="custom.orange" isLoaded={isLoadedStore}>
						<HStack space={1}>
							<Text fontSize={13} bold>
								Phone:
							</Text>
							<Text fontSize={13} color="custom.orange">
								{shop.phone}
							</Text>
						</HStack>
					</Skeleton>
				</VStack>
			</Box>
		</Center>
	);
};

export default ShopDetailCard;

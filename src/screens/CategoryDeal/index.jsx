import React, {useCallback, useState} from 'react';
import {FlatList, ActivityIndicator, Dimensions} from 'react-native';
import {Box, Button, Center, Text} from 'native-base';
import {useDispatch, useSelector} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';

import DealCard from '../../components/DealCard';
import {getDealByCategory} from './categoryDealSlice';
import constants from '../../utils/constants';

const {width} = Dimensions.get('window');
const DEFAULT_PAGE = 1,
	DEFAULT_LIMIT = 10;

const CategoryDeal = ({navigation, route}) => {
	/**
	 * Start Initials
	 */
	const [deals, setDeals] = useState([]);
	const [initialLoading, setInitialLoading] = useState(false);
	const [totalPages, setTotalPages] = useState(1);
	const [loading, setLoading] = useState(false);
	const [hasData, setHasData] = useState(true);
	const [page, setPage] = useState(DEFAULT_PAGE);
	const [dealMsg, setDealMsg] = useState('');

	const dispatch = useDispatch();

	const user = useSelector(state => state.userSignIn.userData);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchDealsByCategory(route.params.categoryId);
		}, []),
	);
	/**
	 * Start Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchDealsByCategory = () => {
		setInitialLoading(true);
		setPage(1);
		setHasData(true);
		dispatch(
			getDealByCategory({
				page: page,
				limit: DEFAULT_LIMIT,
				categoryId: route.params.categoryId,
			}),
		)
			.unwrap()
			.then(res => {
				setDeals(res.data);
				setTotalPages(res.totalPages);
				setInitialLoading(false);

				if (res.totalPages == 0 || res.totalPages == 1) {
					setHasData(false);
					setDealMsg(
						totalPages == 0
							? constants.LABEL.NO_DEALS_FOUND
							: constants.LABEL.NO_MORE_DEALS,
					);
				}
			})
			.catch(() => setInitialLoading(false));
	};

	const retrieveMore = () => {
		if (hasData) {
			setPage(page + 1);
			setLoading(true);
			dispatch(
				getDealByCategory({
					page: page,
					limit: DEFAULT_LIMIT,
					categoryId: route.params.categoryId,
				}),
			)
				.unwrap()
				.then(res => {
					setDeals(preDeals => [...preDeals, ...res.data]);
					setLoading(false);

					if (page == totalPages) {
						setHasData(false);
						setDealMsg(constants.LABEL.NO_MORE_DEALS);
					}
				})
				.catch(() => setLoading(false));
		}
	};

	const ItemView = ({item}) => {
		return (
			<Center w={width / 2.2} my={2}>
				<DealCard
					navigation={navigation}
					productID={item.product?._id}
					productName={item.product?.name}
					productPhoto={item.product?.photo}
					productSize={item.product?.size}
					productPrice={item.productPrice}
					discountPrice={item.discountPrice}
					discountType={item.discountType}
					discountValue={item.discountValue}
					dealId={item._id}
					user={user}
					lowStock={item.lowStock}
					quantity={item.quantity}
				/>
			</Center>
		);
	};

	const renderFooter = () => {
		return (
			<Box w="100%" justifyContent="center" alignItems="center" py={2}>
				{hasData ? (
					<Button
						variant="ghost"
						isLoading={loading}
						isLoadingText="Loading"
						onPress={retrieveMore}>
						{constants.BUTTON.LOAD_MORE}
					</Button>
				) : (
					<Text>{totalPages == 0 ? dealMsg : totalPages > 1 && dealMsg}</Text>
				)}
			</Box>
		);
	};
	/**
	 * Start Methods
	 */

	if (initialLoading) {
		return <ActivityIndicator style={{paddingTop: 15}} />;
	}

	return (
		<Center w="100%">
			<Box px={3} mt={3} flexDirection="row">
				<FlatList
					data={deals}
					keyExtractor={(item, index) => index.toString()}
					enableEmptySections={true}
					renderItem={ItemView}
					numColumns={2}
					ListFooterComponent={renderFooter}
					onRefresh={fetchDealsByCategory}
					refreshing={loading}
				/>
			</Box>
		</Center>
	);
};

export default CategoryDeal;

import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getDealByType = createAsyncThunk(
	'categoryDeal/getDealByType',
	async (data, thunkAPI) => {
		const filters = thunkAPI.getState().dealFilter.activeFilters;
		const {userData, businessData} = thunkAPI.getState().userSignIn;
		const {coordinate} = thunkAPI.getState().addAddress;

		const {ROLE} = constants;
		let longitude, latitude;
		if (userData.role === ROLE.RETAILER) {
			[longitude, latitude] = businessData.location.coordinates;
		} else {
			({longitude, latitude} = coordinate);
		}

		const url =
			constants.API_ROUTES.GET_DEAL_BY_TYPE +
			`/${data.type}?page=${data.page}&limit=${data.limit}&lat=${latitude}&log=${longitude}&categoryId=${data.categoryId}&sizes=${filters.size}&priceRange=${filters.priceRange}&brands=${filters.brand}&offers=${filters.offers}&discounts=${filters.discount}`;

		return await apiCall({url, method: 'GET'}, false)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

export const getDealByCategory = createAsyncThunk(
	'categoryDeal/getDealByCategory',
	async (data, thunkAPI) => {
		const filters = thunkAPI.getState().dealFilter.activeFilters;
		const {userData, businessData} = thunkAPI.getState().userSignIn;
		const {coordinate} = thunkAPI.getState().addAddress;

		const {ROLE} = constants;
		let longitude, latitude;
		if (userData.role === ROLE.RETAILER) {
			[longitude, latitude] = businessData.location.coordinates;
		} else {
			({longitude, latitude} = coordinate);
		}

		const url =
			constants.API_ROUTES.GET_DEAL_BY_CATEGORY +
			`/${data.categoryId}?page=${data.page}&limit=${data.limit}&lat=${latitude}&log=${longitude}&sizes=${filters.size}&priceRange=${filters.priceRange}&brands=${filters.brand}&offers=${filters.offers}&discounts=${filters.discount}`;

		return await apiCall({url, method: 'GET'}, false)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

const categoryDealSlice = createSlice({
	name: 'categoryDeal',
	initialState: {},
});

export default categoryDealSlice.reducer;

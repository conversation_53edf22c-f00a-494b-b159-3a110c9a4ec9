import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getTemplates = createAsyncThunk(
	'myTemplates/getTemplates',
	async (data, thunkAPI) => {
		const businessData = thunkAPI.getState().userSignIn.businessData;
		const query = `?businessId=${businessData._id}&page=${data.page}&limit=${data.limit}&search=${data.search}`;

		return await apiCall(
			{
				url: constants.API_ROUTES.MY_TEMPLATES + `${query}`,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const myTemplatesSlice = createSlice({
	name: 'myTemplates',
	initialState: {},
});

export default myTemplatesSlice.reducer;

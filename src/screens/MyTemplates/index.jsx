import React, {useCallback, useState} from 'react';
import {ActivityIndicator, Dimensions, FlatList, Platform} from 'react-native';
import {Box, Button, Center, Text, VStack} from 'native-base';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import DeviceInfo from 'react-native-device-info';
import {useDispatch} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';

import TemplateCard from './TemplateCard';
import constants from '../../utils/constants';
import {getTemplates} from './myTemplatesSlice';

const {height: viewportHeight} = Dimensions.get('window');
const DEFAULT_PAGE = 1,
	DEFAULT_LIMIT = 10;

const MyTemplates = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const [templates, setTemplates] = useState([]);
	const [totalPages, setTotalPages] = useState(1);
	const [initialLoading, setInitialLoading] = useState(false);
	const [loading, setLoading] = useState(false);
	const [hasData, setHasData] = useState(true);
	const [page, setPage] = useState(DEFAULT_PAGE);

	const dispatch = useDispatch();

	const insets = useSafeAreaInsets();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchDeals();
		}, []),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchDeals = () => {
		setPage(1);
		setInitialLoading(true);
		setHasData(true);
		dispatch(getTemplates({page: page, limit: DEFAULT_LIMIT}))
			.unwrap()
			.then(res => {
				setTemplates(res.data);
				setTotalPages(res.totalPages);
				setInitialLoading(false);

				if (res.totalPages == 0 || res.totalPages == 1) {
					setHasData(false);
				}
			})
			.catch(() => setInitialLoading(false));
	};

	const retrieveMore = () => {
		if (hasData) {
			setPage(page + 1);
			setLoading(true);
			dispatch(getTemplates({page: page, limit: DEFAULT_LIMIT}))
				.unwrap()
				.then(res => {
					setTemplates(prevTemplates => [...prevTemplates, ...res.data]);
					setTotalPages(res.totalPages);
					setLoading(false);

					if (page == totalPages) {
						setHasData(false);
					}
				})
				.catch(() => setLoading(false));
		}
	};

	const ItemView = ({item}) => {
		return (
			<Center w="100%" mt={2} mb={2}>
				<VStack space={3}>
					<TemplateCard template={item} navigation={navigation} />
				</VStack>
			</Center>
		);
	};

	const renderFooter = () => {
		return (
			<Box alignItems="center" py={2}>
				{hasData ? (
					<Button
						variant="ghost"
						isLoading={loading}
						isLoadingText="Loading"
						onPress={retrieveMore}>
						{constants.BUTTON.LOAD_MORE}
					</Button>
				) : (
					<Text>{constants.LABEL.NO_MORE_TEMPLATES}</Text>
				)}
			</Box>
		);
	};
	/**
	 * End Methods
	 */

	return (
		<Center w="100%">
			<Box
				w="100%"
				h={viewportHeight - Platform.select({ios: 55, android: 55})}>
				<Box
					px={2}
					pb={3}
					pt={insets.top + 5 - (DeviceInfo.hasNotch() ? -1 : 18)}
					alignItems="center"
					style={{backgroundColor: constants.COLORS.ORANGE}}>
					<Text
						color={constants.COLORS.WHITE}
						mt={4}
						fontSize={18}
						fontWeight={Platform.select({ios: '700', android: 500})}>
						{constants.SCREENS.MY_TEMPLATES}
					</Text>
				</Box>

				{initialLoading ? (
					<ActivityIndicator style={{paddingTop: 15}} />
				) : (
					<FlatList
						data={templates}
						keyExtractor={(item, index) => index.toString()}
						enableEmptySections={true}
						renderItem={ItemView}
						ListFooterComponent={renderFooter}
						onEndReached={retrieveMore}
						onRefresh={fetchDeals}
						refreshing={loading}
					/>
				)}
			</Box>
		</Center>
	);
};

export default MyTemplates;

import React from 'react';
import {Box, VStack, HStack, Image, Stack, Text, Button} from 'native-base';
import constants from '../../utils/constants';
import {StyleSheet} from 'react-native';
import {toUri} from '../../utils/helpers';

const TemplateCard = props => {
	/**
	 * Start Initials
	 */
	const {template, navigation} = props;
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const editTemplate = () => {
		navigation.navigate(constants.ROUTE.EDIT_TEMPLATE, {
			templateId: template._id,
		});
	};

	const onPressUseTemplate = () => {
		navigation.navigate(constants.ROUTE.CREATE_DEAL, {
			templateId: template._id,
		});
	};
	/**
	 * End Methods
	 */

	return (
		<Box w="90%" style={styles.cardStyle} p={2}>
			<VStack w="100%">
				<HStack w="100%" space={1}>
					<Box w="25%">
						{template.quantity > 1 ? (
							<Text
								bg={'#F5F5F5'}
								position="absolute"
								zIndex={1}
								top={-8}
								left={-5}
								borderRadius={8}
								px={1}
								fontSize={12}>
								Pack of {template.quantity}
							</Text>
						) : null}
						<Image
							source={{uri: toUri(template.product.photo)}}
							alt="image"
							resizeMode="contain"
							size="md"
						/>
						<Text
							color="custom.red"
							fontSize={10}
							bold
							textAlign="center"
							bg="white"
							w="100%"
							p={0.3}>
							{template.lowStock && 'Low Stock'}
						</Text>
					</Box>

					<Stack w="40%" space={2} mr={3}>
						<Stack>
							<Text fontSize={16} isTruncated w="120px">
								{template.product.name}
							</Text>
							<Text fontSize={12}>{template.product.size} ml</Text>
						</Stack>
						<HStack space={2}>
							{[
								constants.DEAL_TYPE.FIX,
								constants.DEAL_TYPE.PERCENT,
								constants.DEAL_TYPE.QUANTITY,
							].includes(template.discountType) && (
								<Text fontSize={14} strikeThrough color="custom.red">
									${template.productPrice}
								</Text>
							)}
							<Text fontSize={12} bold color="custom.green">
								${template.discountPrice}
							</Text>
						</HStack>
						<Box
							bg="custom.green"
							px={1}
							py={0.2}
							borderRadius={2}
							alignSelf="flex-start">
							{['fix', 'percent', 'quantity'].includes(
								template.discountType,
							) ? (
								<>
									<Text color="white" fontSize={10} bold>
										{template.discountType === 'percent'
											? `${template.discountValue}%`
											: `$${template.discountValue}`}{' '}
										OFF
									</Text>
								</>
							) : (
								<Text color="white" fontSize={10} bold>
									{template.discountType}
								</Text>
							)}
						</Box>
					</Stack>

					<VStack space={3} alignItems="flex-end">
						<Button
							size={'xs'}
							py={2}
							borderRadius={5}
							_text={{fontSize: 12}}
							onPress={editTemplate}>
							{constants.BUTTON.EDIT_TEMPLATE}
						</Button>
						<Button
							size={'xs'}
							py={2}
							borderRadius={5}
							_text={{fontSize: 12}}
							onPress={onPressUseTemplate}>
							{constants.BUTTON.USE_TEMPLATE}
						</Button>
					</VStack>
				</HStack>
			</VStack>
		</Box>
	);
};

const styles = StyleSheet.create({
	cardStyle: {
		shadowColor: constants.COLORS.GREY1,
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.3,
		shadowRadius: 2.84,
		elevation: 3,
		borderRadius: 5,
		padding: 2,
		marginHorizontal: 5,
		backgroundColor: constants.COLORS.WHITE,
	},
});

export default TemplateCard;

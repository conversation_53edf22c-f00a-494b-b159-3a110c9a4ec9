import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getTemplateDetail = createAsyncThunk(
	'editTemplate/getTemplateDetail',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_TEMPLATE + `/${data}`,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const updateTemplate = createAsyncThunk(
	'editTemplate/updateTemplate',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.UPDATE_TEMPLATE + `/${data.templateId}`,
				method: 'PUT',
				data: data.data,
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const deleteTemplate = createAsyncThunk(
	'deleteTemplate',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.DELETE_TEMPLATE + `/${data.templateId}`,
				method: 'DELETE',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const editTemplateSlice = createSlice({
	name: 'editTemplate',
	initialState: {},
});

export default editTemplateSlice.reducer;

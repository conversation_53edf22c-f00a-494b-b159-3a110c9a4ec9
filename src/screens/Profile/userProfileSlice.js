import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import constants from '../../utils/constants';
import apiCall from '../../services/apiClient';

export const getUser = createAsyncThunk(
	'userProfile/getUser',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_USER,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const userProfileSlice = createSlice({
	name: 'userProfile',
	initialState: {},
});

export default userProfileSlice.reducer;

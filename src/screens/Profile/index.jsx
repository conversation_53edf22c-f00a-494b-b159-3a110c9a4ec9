import React, {useCallback, useState} from 'react';
import {
	Box,
	VStack,
	Center,
	ScrollView,
	Avatar,
	Text,
	HStack,
	Icon,
	Divider,
	AlertDialog,
	Button,
	useToast,
} from 'native-base';
import {Linking, Platform, TouchableOpacity} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch, useSelector} from 'react-redux';
import {TabActions} from '@react-navigation/native';
import {debounce} from 'lodash';

import constants, {
	APP_NAME,
	APP_PRIVACY_POLICY_URL,
	APP_TERMS_AND_CONDITIONS_URL,
	APP_VERSION,
} from '../../utils/constants';
import {
	signOut,
	deleteAccount,
	setIsGuestUser,
} from '../SignIn/userSignInSlice';
import {toInitials, toUri} from '../../utils/helpers';
import ToastAlert from '../../components/ToastAlert';
import SpinnerOverlay from '../../components/SpinnerOverlay';
import DeviceInfo from 'react-native-device-info';

const Profile = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const userData = useSelector(state => state.userSignIn.userData);
	const isGuestUser = useSelector(state => state.userSignIn.isGuestUser);

	const [isOpen, setIsOpen] = React.useState(false);
	const [logOutOpen, setLogOutOpen] = React.useState(false);
	const [loading, setLoading] = useState(false);
	const onClose = () => setIsOpen(false);
	const onLogOutClose = () => setLogOutOpen(false);

	const cancelRef = React.useRef(null);
	const logOutCancelRef = React.useRef(null);

	const dispatch = useDispatch();

	const toast = useToast();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */

	const handleSignOutNavigation = debounce(() => {
		navigation.reset({
			index: 0,
			routes: [{ name: constants.ROUTE.DEALS_NAVIGATION }],
		});
	}, 300);

	const signOutUser = () => {
		if (isGuestUser) {
			dispatch(setIsGuestUser(false));
		} else {
			dispatch(signOut())
				.unwrap()
				.then(() => {
					handleSignOutNavigation();
				});
		}
	};

	const deleteUserAccount = () => {
		onClose();
		setLoading(true);
		dispatch(deleteAccount())
			.unwrap()
			.then(() => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.ACCOUNT_DELETE}
							/>
						);
					},
				});
				setLoading(false);
			})
			.catch(error => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={error.message ? error.message : error}
							/>
						);
					},
				});
				setLoading(false);
			});
	};

	const openPrivacyPolicy = async () => {
		const url = APP_PRIVACY_POLICY_URL;

		// Checking if the link is supported for links with custom URL scheme.
		const supported = await Linking.canOpenURL(url);

		if (supported) {
			// Opening the link with some app, if the URL scheme is "http" the web link should be opened
			// by some browser in the mobile
			await Linking.openURL(url);
		} else {
			toast.show({
				avoidKeyboard: true,
				render: ({id}) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={`Don't know how to open this URL: ${url}`}
						/>
					);
				},
			});
		}
	};

	const openTermsAndConditions = async () => {
		const url = APP_TERMS_AND_CONDITIONS_URL;

		// Checking if the link is supported for links with custom URL scheme.
		const supported = await Linking.canOpenURL(url);

		if (supported) {
			// Opening the link with some app, if the URL scheme is "http" the web link should be opened
			// by some browser in the mobile
			await Linking.openURL(url);
		} else {
			toast.show({
				avoidKeyboard: true,
				render: ({id}) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={`Don't know how to open this URL: ${url}`}
						/>
					);
				},
			});
		}
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<ScrollView>
				<Center w="100%" bg="white">
					<Box w="95%">
						<VStack space={6} alignItems="center" px={2} py={6}>
							<Box alignItems="center">
								<Avatar
									size="xl"
									bg="custom.lightOrange"
									borderWidth={2}
									borderColor="custom.grey1"
									_text={{
										color: 'custom.orange',
										fontSize: 20,
										fontWeight: Platform.select({ios: '700', android: 700}),
									}}
									// Conditionally set the source only if the profile photo exists
									source={
										userData.profilePhoto
											? {uri: toUri(userData.profilePhoto)}
											: null
									}>
									{/* Show initials if name exists, otherwise show icon */}
									{userData.name
										? toInitials(userData.name)
										: !userData.profilePhoto && (
												<Icon
													as={MaterialCommunityIcons}
													name="account" // Default user icon
													size={8}
													color="custom.orange"
												/>
											)}
								</Avatar>
								<HStack alignItems="center">
									<Text fontSize={20} color="custom.black" bold mt={2}>
										{userData.name ? userData.name : userData.email}
									</Text>
								</HStack>
							</Box>
							<Box w="100%" mt={2}>
								<VStack space={1}>
									{!isGuestUser && (
										<>
											<Divider bg="custom.grey6" thickness={1} />

											<TouchableOpacity
												onPress={() =>
													navigation.navigate(constants.ROUTE.EDIT_PROFILE)
												}
												style={{paddingVertical: 4, paddingStart: 2}}>
												<HStack justifyContent="space-between">
													<Text fontSize={16} mt={1} color="custom.black">
														{constants.SCREENS.EDIT_PROFILE}
													</Text>
													<Icon
														as={MaterialCommunityIcons}
														name="chevron-right"
														color="custom.orange"
														size="xl"
													/>
												</HStack>
											</TouchableOpacity>

											<Divider bg="custom.grey6" thickness={1} />

											<TouchableOpacity
												onPress={() =>
													navigation.navigate(constants.ROUTE.CHANGE_PASSWORD)
												}
												style={{paddingVertical: 4, paddingStart: 2}}>
												<HStack justifyContent="space-between">
													<Text mt={1} color="custom.black">
														{constants.SCREENS.CHANGE_PASSWORD}
													</Text>
													<Icon
														as={MaterialCommunityIcons}
														name="chevron-right"
														color="custom.orange"
														size="xl"
													/>
												</HStack>
											</TouchableOpacity>
										</>
									)}

									<Divider bg="custom.grey6" thickness={1} />

									{userData.role === constants.ROLE.RETAILER && (
										<>
											<TouchableOpacity
												onPress={() =>
													navigation.navigate(
														constants.ROUTE.EDIT_BUSINESS_PROFILE,
													)
												}
												style={{paddingVertical: 4, paddingStart: 2}}>
												<HStack justifyContent="space-between">
													<Text mt={1} fontSize={16} color="custom.black">
														{constants.SCREENS.EDIT_BUSINESS_PROFILE}
													</Text>
													<Icon
														as={MaterialCommunityIcons}
														name="chevron-right"
														color="custom.orange"
														size="xl"
													/>
												</HStack>
											</TouchableOpacity>

											<Divider bg="custom.grey6" thickness={1} />

											<TouchableOpacity
												onPress={() =>
													navigation.navigate(
														constants.ROUTE.REQUEST_ADD_PRODUCT,
													)
												}
												style={{paddingVertical: 4, paddingStart: 2}}>
												<HStack justifyContent="space-between">
													<Text mt={1} fontSize={16} color="custom.black">
														{constants.SCREENS.REQUEST_ADD_PRODUCT}
													</Text>
													<Icon
														as={MaterialCommunityIcons}
														name="chevron-right"
														color="custom.orange"
														size="xl"
													/>
												</HStack>
											</TouchableOpacity>

											<Divider bg="custom.grey6" thickness={1} />
										</>
									)}

									{!isGuestUser && (
										<>
											<TouchableOpacity
												onPress={() =>
													navigation.navigate(constants.ROUTE.NOTIFICATIONS)
												}
												style={{paddingVertical: 4, paddingStart: 2}}>
												<HStack justifyContent="space-between">
													<Text fontSize={16} mt={1} color="custom.black">
														{constants.SCREENS.NOTIFICATIONS}
													</Text>
													<Icon
														as={MaterialCommunityIcons}
														name="chevron-right"
														color="custom.orange"
														size="xl"
													/>
												</HStack>
											</TouchableOpacity>
											<Divider bg="custom.grey6" thickness={1} />
										</>
									)}

									<TouchableOpacity
										onPress={() =>
											navigation.navigate(constants.ROUTE.CONTACT_US)
										}
										style={{paddingVertical: 4, paddingStart: 2}}>
										<HStack justifyContent="space-between">
											<Text fontSize={16} mt={1} color="custom.black">
												{constants.SCREENS.CONTACT_US}
											</Text>
											<Icon
												as={MaterialCommunityIcons}
												name="chevron-right"
												color="custom.orange"
												size="xl"
											/>
										</HStack>
									</TouchableOpacity>

									<Divider bg="custom.grey6" thickness={1} />

									<TouchableOpacity
										onPress={openPrivacyPolicy}
										style={{paddingVertical: 4, paddingStart: 2}}>
										<HStack justifyContent="space-between">
											<Text fontSize={16} mt={1} color="custom.black">
												{constants.SCREENS.PRIVACY_POLICY}
											</Text>
											<Icon
												as={MaterialCommunityIcons}
												name="chevron-right"
												color="custom.orange"
												size="xl"
											/>
										</HStack>
									</TouchableOpacity>

									<Divider bg="custom.grey6" thickness={1} />

									<TouchableOpacity
										onPress={openTermsAndConditions}
										style={{paddingVertical: 4, paddingStart: 2}}>
										<HStack justifyContent="space-between">
											<Text fontSize={16} mt={1} color="custom.black">
												{constants.SCREENS.TERMS_CONDITIONS}
											</Text>
											<Icon
												as={MaterialCommunityIcons}
												name="chevron-right"
												color="custom.orange"
												size="xl"
											/>
										</HStack>
									</TouchableOpacity>

									<Divider bg="custom.grey6" thickness={1} />

									<TouchableOpacity
										onPress={()=> setLogOutOpen(!logOutOpen)}
										style={{paddingVertical: 4, paddingStart: 2}}>
										<HStack justifyContent="space-between">
											<Text fontSize={16} mt={1} color="custom.orange">
												{constants.SCREENS.SIGN_OUT}
											</Text>
											<Icon
												as={MaterialCommunityIcons}
												name="chevron-right"
												color="custom.orange"
												size="xl"
											/>
										</HStack>
									</TouchableOpacity>

									<Divider bg="custom.grey6" thickness={1} />

									{!isGuestUser && (
										<>
											<TouchableOpacity
												onPress={() => setIsOpen(!isOpen)}
												style={{paddingVertical: 4, paddingStart: 2}}>
												<HStack justifyContent="space-between">
													<Text mt={1} fontSize={16} color="custom.red">
														{constants.SCREENS.DELETE_ACCOUNT}
													</Text>
													<Icon
														as={MaterialCommunityIcons}
														name="chevron-right"
														color="custom.orange"
														size="xl"
													/>
												</HStack>
											</TouchableOpacity>

											<Divider bg="custom.grey6" thickness={1} />
										</>
									)}

									<Center mt={5}>
										<Text>{ `v${DeviceInfo.getVersion()}(${DeviceInfo.getBuildNumber()})` }</Text>
									</Center>
								</VStack>
							</Box>
						</VStack>
					</Box>
					<AlertDialog
						leastDestructiveRef={cancelRef}
						isOpen={isOpen}
						onClose={onClose}>
						<AlertDialog.Content>
							<AlertDialog.Header>
								{constants.TITLE.DELETE_ACCOUNT}
							</AlertDialog.Header>
							<AlertDialog.Body>
								{constants.TEXT.DELETE_ACCOUNT}
							</AlertDialog.Body>
							<AlertDialog.Footer>
								<Button.Group space={2}>
									<Button
										variant="unstyled"
										colorScheme="coolGray"
										onPress={onClose}
										ref={cancelRef}>
										{constants.BUTTON.CANCEL}
									</Button>
									<Button colorScheme="danger" onPress={deleteUserAccount}>
										{constants.BUTTON.DELETE}
									</Button>
								</Button.Group>
							</AlertDialog.Footer>
						</AlertDialog.Content>
					</AlertDialog>

					<AlertDialog
						leastDestructiveRef={logOutCancelRef}
						isOpen={logOutOpen}
						onClose={onLogOutClose}>
						<AlertDialog.Content>
							<AlertDialog.Header>
								{constants.TITLE.SIGN_OUT_CONFIRMATION}
							</AlertDialog.Header>
							<AlertDialog.Body>
								{constants.TEXT.CONFIRM_SIGN_OUT}
							</AlertDialog.Body>
							<AlertDialog.Footer>
								<Button.Group space={2}>
									<Button
										variant="unstyled"
										colorScheme="coolGray"
										onPress={onLogOutClose}
										ref={logOutCancelRef}>
										{constants.BUTTON.CANCEL}
									</Button>
									<Button colorScheme="danger" onPress={signOutUser}>
										{constants.BUTTON.LOGOUT}
									</Button>
								</Button.Group>
							</AlertDialog.Footer>
						</AlertDialog.Content>
					</AlertDialog>
				</Center>
			</ScrollView>
			<SpinnerOverlay loading={loading} />
		</>
	);
};

export default Profile;

import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import {firebaseChangePassword} from '../../services/firebaseAuth';

export const updatePassword = createAsyncThunk(
	'changePassword/updatePassword',
	async (data, thunkAPI) => {
		return await firebaseChangePassword(data.newPassword, data.currentPassword)
			.then(r => r)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

const changePasswordSlice = createSlice({
	name: 'changePassword',
	initialState: {
		user: null,
		loading: false,
	},
	reducers: {},
	extraReducers: builder => {
		builder
			.addCase(updatePassword.pending, state => {
				state.loading = true;
			})
			.addCase(updatePassword.fulfilled, state => {
				state.loading = false;
			})
			.addCase(updatePassword.rejected, state => {
				state.loading = false;
			});
	},
});

export default changePasswordSlice.reducer;

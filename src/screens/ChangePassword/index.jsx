import React, {useState} from 'react';

import {
	Box,
	VStack,
	Center,
	FormControl,
	Input,
	Button,
	Icon,
	Pressable,
	useToast,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useForm, Controller} from 'react-hook-form';
import {useDispatch} from 'react-redux';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import _ from 'lodash';

import constants from '../../utils/constants.js';
import ToastAlert from '../../components/ToastAlert';
import {updatePassword} from './changePasswordSlice';
import SpinnerOverlay from '../../components/SpinnerOverlay';

const ChangePassword = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const [showCurrentPassword, setShowCurrentPassword] = useState(false);
	const [showNewPassword, setShowNewPassword] = useState(false);
	const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);
	const [loading, setLoading] = useState(false);

	const {
		control,
		handleSubmit,
		reset,
		watch,
		formState: {errors},
	} = useForm({
		defaultValues: {
			currentPassword: '',
			newPassword: '',
			confirmNewPassword: '',
		},
	});

	const dispatch = useDispatch();

	const toast = useToast();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const onSubmit = data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}
		setLoading(true);

		dispatch(updatePassword(data))
			.unwrap()
			.then(() => {
				setLoading(false);
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.PASSWORD_CHANGED}
							/>
						);
					},
				});
				navigation.navigate(constants.ROUTE.PROFILE);
				reset();
			})
			.catch(err => {
				setLoading(false);
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert id={id} toast={toast} type="error" message={err} />
						);
					},
				});
			});
	};

	const debouncedSubmit = _.debounce(onSubmit, 1000, {
		leading: true,
		trailing: false,
	});
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Center w="100%" backgroundColor="white">
					<Box mt={6} w="90%">
						<VStack space={3}>
							<FormControl>
								<FormControl.Label>
									{constants.LABEL.CURRENT_PASSWORD}
								</FormControl.Label>
								<Controller
									control={control}
									name="currentPassword"
									rules={{
										required: {
											value: true,
											message:
												constants.VALIDATION_MESSAGES.CURRENT_PASSWORD_REQUIRED,
										},
										minLength: {
											value: 6,
											message: constants.VALIDATION_MESSAGES.PASSWORD_LENGTH,
										},
									}}
									render={({field: {onChange, onBlur, value}}) => (
										<Input
											size="xl"
											secureTextEntry={!showCurrentPassword}
											InputRightElement={
												<Pressable
													onPress={() =>
														setShowCurrentPassword(!showCurrentPassword)
													}>
													<Icon
														as={MaterialCommunityIcons}
														name={
															!showCurrentPassword
																? 'eye-outline'
																: 'eye-off-outline'
														}
														size="md"
													/>
												</Pressable>
											}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.currentPassword && (
									<FormControl.HelperText _text={{color: 'custom.red'}}>
										{errors.currentPassword.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.NEW_PASSWORD}
								</FormControl.Label>
								<Controller
									control={control}
									name="newPassword"
									rules={{
										required: {
											value: true,
											message:
												constants.VALIDATION_MESSAGES.NEW_PASSWORD_REQUIRED,
										},
										minLength: {
											value: 6,
											message: constants.VALIDATION_MESSAGES.PASSWORD_LENGTH,
										},
										pattern: {
											value: constants.REGEX.PASSWORD,
											message: constants.VALIDATION_MESSAGES.WEAK_PASSWORD,
										},
									}}
									render={({field: {onChange, onBlur, value}}) => (
										<Input
											size="xl"
											secureTextEntry={!showNewPassword}
											InputRightElement={
												<Pressable
													onPress={() => setShowNewPassword(!showNewPassword)}>
													<Icon
														as={MaterialCommunityIcons}
														name={
															!showNewPassword
																? 'eye-outline'
																: 'eye-off-outline'
														}
														size="md"
													/>
												</Pressable>
											}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.newPassword && (
									<FormControl.HelperText _text={{color: 'custom.red'}}>
										{errors.newPassword.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.CONFIRM_NEW_PASSWORD}
								</FormControl.Label>
								<Controller
									control={control}
									name="confirmNewPassword"
									rules={{
										required: {
											value: false,
											message:
												constants.VALIDATION_MESSAGES.NEW_PASSWORD_REQUIRED,
										},
										validate: val => {
											if (watch('newPassword') !== val) {
												return constants.VALIDATION_MESSAGES
													.CONFIRM_PASSWORD_NOT_MATCH;
											}
										},
									}}
									render={({field: {onChange, onBlur, value}}) => (
										<Input
											size="xl"
											secureTextEntry={!showConfirmNewPassword}
											InputRightElement={
												<Pressable
													onPress={() =>
														setShowConfirmNewPassword(!showConfirmNewPassword)
													}>
													<Icon
														as={MaterialCommunityIcons}
														name={
															!showConfirmNewPassword
																? 'eye-outline'
																: 'eye-off-outline'
														}
														size="md"
													/>
												</Pressable>
											}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.confirmNewPassword && (
									<FormControl.HelperText _text={{color: 'custom.red'}}>
										{errors.confirmNewPassword.message}
									</FormControl.HelperText>
								)}
							</FormControl>
							<Button
								mt="3"
								onPress={handleSubmit(debouncedSubmit)}
								isDisabled={loading}>
								{constants.BUTTON.SAVE}
							</Button>
						</VStack>
					</Box>
				</Center>
			</KeyboardAwareScrollView>
			<SpinnerOverlay loading={loading} />
		</>
	);
};

export default ChangePassword;

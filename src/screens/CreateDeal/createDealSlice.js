import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getOffers = createAsyncThunk(
	'createDeal/getOffers',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_DEAL_OFFERS,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const getProducts = createAsyncThunk(
	'createDeal/getProducts',
	async (data, thunkAPI) => {
		const query = `?search=${data.search}&page=${data.page}&limit=${data.limit}`;

		return await apiCall(
			{
				url: constants.API_ROUTES.GET_PRODUCTS + query,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const addDeal = createAsyncThunk(
	'addDeal/getProducts',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.CREATE_DEAL,
				method: 'POST',
				data: data,
			},
			true,
		)
			.then(r => r)
			.catch(error => {
				return thunkAPI.rejectWithValue(error?.data);
			});
	},
);

const createDealSlice = createSlice({
	name: 'createDeal',
	initialState: {},
});

export default createDealSlice.reducer;

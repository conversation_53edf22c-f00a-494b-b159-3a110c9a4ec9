import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Platform, StyleSheet} from 'react-native';
import {
	Box,
	Button,
	Center,
	FormControl,
	Heading,
	HStack,
	Icon,
	Image,
	Input,
	Stack,
	Text,
	useToast,
	VStack,
	Switch,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useFocusEffect} from '@react-navigation/native';
import {useForm, Controller} from 'react-hook-form';
import {useDispatch, useSelector} from 'react-redux';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import _ from 'lodash';
import RNPickerSelect from 'react-native-picker-select';

import constants, {
	DEAL_TYPE_OPTIONS,
	DISCOUNT_TYPE_OPTIONS,
} from '../../utils/constants';
import SpinnerOverlay from '../../components/SpinnerOverlay';
import {addDeal, getOffers} from './createDealSlice';
import ToastAlert from '../../components/ToastAlert';
import {getTemplateDetail} from '../EditTemplate/editTemplateSlice';
import ProductSearch from '../../components/ProductSearch';
import {toUri} from '../../utils/helpers';

const CreateDeal = ({route}) => {
	/**
	 * Start Initials
	 */
	const {
		control,
		handleSubmit,
		setValue,
		getValues,
		reset,
		watch,
		formState: {errors},
	} = useForm({
		defaultValues: {
			type: 'flash',
			discountType: 'percent',
		},
	});
	const watchDiscountType = watch('discountType');
	const watchProductPrice = watch('productPrice');
	const watchDiscountPercent = watch('discountPercent');
	const watchDiscountPrice = watch('discountPrice');
	const watchQuantity = watch('quantity');

	const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
	const [offers, setOffers] = useState([]);
	const [loading, setLoading] = useState(false);
	const [lowStock, setLowStock] = useState(false);
	const [selectedProduct, setSelectedProduct] = useState(null);
	const [discountedPrice, setDiscountedPrice] = useState(0);

	const dispatch = useDispatch();

	const businessData = useSelector(state => state.userSignIn.businessData);

	const toast = useToast();

	const productSearchRef = useRef(null);

	dayjs.extend(customParseFormat);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchOffers();
			reset();
			setLowStock(false);
			if (route.params?.templateId) {
				fetchTemplateDetail();
			}
		}, []),
	);

	useEffect(() => {
		setTimeout(() => {
			if (watchProductPrice) {
				setDiscountedPrice(watchProductPrice);
			} else {
				setDiscountedPrice(0);
			}

			if (watchDiscountType === 'fix') {
				if (watchProductPrice && watchDiscountPrice) {
					const calculatedPrice = watchProductPrice - watchDiscountPrice;
					const finalCalculatedPrice = parseFloat(calculatedPrice).toFixed(2);
					setDiscountedPrice(finalCalculatedPrice);
				}
			} else if (
				watchDiscountType === 'percent' ||
				watchDiscountType === 'quantity'
			) {
				if (watchProductPrice && watchDiscountPercent) {
					const calculatedPrice =
						watchProductPrice -
						(watchProductPrice * watchDiscountPercent) / 100;
					const finalCalculatedPrice = parseFloat(calculatedPrice).toFixed(2);
					setDiscountedPrice(finalCalculatedPrice);
				}
			}
		}, 1);
	}, [
		watchProductPrice,
		watchDiscountPercent,
		watchDiscountPrice,
		watchDiscountType,
	]);

	useEffect(() => {
		switch (watchDiscountType) {
			case 'percent':
				setValue('discountPercent', '');
				setValue('discountPrice', '');
				setValue('productPrice', '');
				break;
			case 'fix':
				setValue('discountPrice', '');
				setValue('productPrice', '');
				break;
			default:
				setValue('discountPercent', '');
				setValue('discountPrice', '');
				setValue('productPrice', '');
		}
	}, [watchDiscountType]);

	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchTemplateDetail = () => {
		setLoading(true);
		dispatch(getTemplateDetail(route.params?.templateId))
			.unwrap()
			.then(res => {
				setSelectedProduct(res.product);
				setValue('discountType', res.discountType);
				setValue('productPrice', res.productPrice.toString());
				setValue('discountPercent', res.discountValue.toString());
				setValue('discountPrice', res.discountValue.toString());
				setLoading(false);
			})
			.catch(() => setLoading(false));
	};

	const fetchOffers = () => {
		dispatch(getOffers())
			.unwrap()
			.then(res => {
				setOffers(res);
			});
	};

	const onSubmit = async data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}

		setLoading(true);

		if (!selectedProduct) {
			toast.show({
				avoidKeyboard: true,
				render: ({id}) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={constants.VALIDATION_MESSAGES.PRODUCT_REQUIRED}
						/>
					);
				},
			});
			setLoading(false);
			return;
		}

		let discountValue = 0;
		let discountPrice = data.productPrice;

		if (data.discountType === 'fix') {
			discountValue = data.discountPrice;
			discountPrice = parseFloat(data.productPrice - data.discountPrice).toFixed(2);
		} else if (
			data.discountType === 'percent' ||
			data.discountType === 'quantity'
		) {
			discountValue = data.discountPercent;
			discountPrice = discountedPrice;
		}

		const expiryDateTime = dayjs(data.expiryDateTime, 'MM-DD-YYYY hh:mm A');
		if (dayjs(expiryDateTime).isBefore(dayjs())) {
			toast.show({
				avoidKeyboard: true,
				render: ({id}) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={constants.VALIDATION_MESSAGES.VALID_DEAL_ENDS_ON}
						/>
					);
				},
			});
			setLoading(false);
			return false;
		}

		const deal = {
			type: data.type,
			productPrice: Number(data.productPrice),
			discountType: data.discountType,
			discountPrice: discountPrice,
			discountValue: Number(discountValue),
			expiryDateTime: expiryDateTime,
			business: businessData._id,
			product: selectedProduct._id,
			lowStock: lowStock,
			quantity: Number(watchQuantity) > 1 ? Number(watchQuantity) : 1,
		};

		await dispatch(addDeal(deal))
			.unwrap()
			.then(() => {
				setSelectedProduct(null);
				reset();

				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.DEAL_CREATED}
							/>
						);
					},
				});
				setLoading(false);
			})
			.catch(err => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								// TODO: we need to change message coming from backend the condition is bypass for now.
								message={
									err.message && err.type
										? err.message
										: err.message && err.responseCode === 500
											? constants.ERROR_MESSAGE.BUSINESS_PROFILE_REQUIRED
											: constants.ERROR_MESSAGE.SOMETHING_WRONG
								}
							/>
						);
					},
				});
				setLoading(false);
			});
	};

	const debouncedSubmit = _.debounce(onSubmit, 1000, {
		leading: true,
		trailing: false,
	});

	const toggleDatePicker = () => {
		setDatePickerVisibility(prevState => !prevState);
	};

	const handleDateConfirm = date => {
		setValue('expiryDateTime', dayjs(date).format('MM-DD-YYYY hh:mm A'));
		toggleDatePicker();
	};

	const openActionsheet = () => {
		productSearchRef.current.openActionsheet();
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Center w="100%" backgroundColor="white">
					<Box py={5} w="90%">
						<VStack space={5}>
							<Stack space={5}>
								<FormControl>
									<FormControl.Label>
										{constants.LABEL.PRODUCT}
									</FormControl.Label>
									<Button
										variant="Unstyled"
										onPress={openActionsheet}
										borderRadius={0}
										borderBottomWidth={1}
										borderBottomColor="muted.300"
										justifyContent="flex-start"
										p={0}
										_text={
											selectedProduct
												? {fontSize: 16}
												: {color: 'muted.300', fontSize: 16}
										}>
										{selectedProduct ? selectedProduct.name : 'Select Product'}
									</Button>
									{selectedProduct && (
										<Box style={styles.cardStyle}>
											<VStack>
												<HStack space={1}>
													<Box w="20%">
														<Image
															source={{uri: toUri(selectedProduct.photo)}}
															alt="image"
															resizeMode="contain"
															size="md"
														/>
													</Box>

													<Stack w="80%" space={2}>
														<Stack>
															<Text fontSize={16}>{selectedProduct.name}</Text>
															<Text fontSize={12}>
																{selectedProduct.size} ml
															</Text>
														</Stack>
													</Stack>
												</HStack>
											</VStack>
										</Box>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.DEAL_TYPE}
									</FormControl.Label>
									<Controller
										control={control}
										name="type"
										rules={{
											required: {
												value: true,
												message:
													constants.VALIDATION_MESSAGES.DEAL_TYPE_REQUIRED,
											},
										}}
										render={({field: {onChange, value}}) => (
											<RNPickerSelect
												pickerProps={{
													accessibilityLabel: 'Choose deal type',
												}}
												style={{
													inputIOS: {
														borderBottomColor: constants.COLORS.GREY3,
														borderBottomWidth: 1,
														padding: 1,
														width: '100%',
														color: constants.COLORS.DARK1,
														fontSize: 16,
													},
													inputAndroid: {
														borderBottomColor: constants.COLORS.GREY3,
														borderBottomWidth: 1,
														padding: 1,
														width: '100%',
														color: constants.COLORS.DARK1,
														fontSize: 16,
													},
													placeholder: {
														color: constants.COLORS.DARK1,
													},
												}}
												selectedValue={value}
												value={value}
												onValueChange={onChange}
												placeholder={{label: 'Choose deal type'}}
												items={DEAL_TYPE_OPTIONS}
											/>
										)}
									/>
									{errors.type && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.type.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.DISCOUNT_TYPE}
									</FormControl.Label>
									<Controller
										control={control}
										name="discountType"
										rules={{
											required: {
												value: true,
												message:
													constants.VALIDATION_MESSAGES.DISCOUNT_TYPE_REQUIRED,
											},
										}}
										render={({field: {onChange, value}}) => (
											<RNPickerSelect
												pickerProps={{
													accessibilityLabel: 'Choose discount type',
												}}
												style={{
													inputIOS: {
														borderBottomColor: constants.COLORS.GREY3,
														borderBottomWidth: 1,
														padding: 1,
														width: '100%',
														color: constants.COLORS.DARK1,
														fontSize: 16,
													},
													inputAndroid: {
														borderBottomColor: constants.COLORS.GREY3,
														borderBottomWidth: 1,
														padding: 1,
														width: '100%',
														color: constants.COLORS.DARK1,
														fontSize: 16,
													},
													placeholder: {
														color: constants.COLORS.DARK1,
													},
												}}
												selectedValue={value}
												value={value}
												onValueChange={onChange}
												placeholder={{label: 'Choose discount type'}}
												items={[
													...DISCOUNT_TYPE_OPTIONS,
													...offers.map(v => ({label: v.name, value: v.name})),
												]}
											/>
										)}
									/>
									{errors.discountType && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.discountType.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label mb={1}>
										{constants.LABEL.PRODUCT_PRICE}
									</FormControl.Label>
									<Controller
										control={control}
										name="productPrice"
										rules={{
											required: {
												value: true,
												message: constants.VALIDATION_MESSAGES.PRODUCT_PRICE,
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												keyboardType="decimal-pad"
												autoCorrect={false}
												onBlur={onBlur}
												onChangeText={onChange}
												value={value}
												returnKeyType="done"
												returnKeyLabel="done"
												InputLeftElement={
													<Icon
														as={MaterialCommunityIcons}
														name="currency-usd"
													/>
												}
											/>
										)}
									/>
									{errors.productPrice && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.productPrice.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								{['fix', 'percent', 'quantity'].includes(watchDiscountType) && (
									<FormControl>
										{watchDiscountType === 'fix' ? (
											<>
												<FormControl.Label mb={1}>
													{constants.LABEL.DISCOUNT_PRICE}
												</FormControl.Label>
												<Controller
													control={control}
													name="discountPrice"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PRICE_REQUIRED,
														},
														validate: value => {
															if (value > parseFloat(watchProductPrice)) {
																return constants.VALIDATION_MESSAGES
																	.MATCH_DISCOUNT_PRICE;
															}
														},
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															keyboardType="decimal-pad"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
															returnKeyType="done"
															returnKeyLabel="done"
															InputLeftElement={
																<Icon
																	as={MaterialCommunityIcons}
																	name="currency-usd"
																/>
															}
														/>
													)}
												/>
												{errors.discountPrice && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.discountPrice.message}
													</FormControl.HelperText>
												)}
											</>
										) : watchDiscountType === 'percent' ? (
											<>
												<FormControl.Label mb={1}>
													{constants.LABEL.DISCOUNT_PERCENT}
												</FormControl.Label>
												<Controller
													control={control}
													name="discountPercent"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_REQUIRED,
														},
														pattern: {
															value: constants.REGEX.DISCOUNT_PERCENT,
															message:
																constants.VALIDATION_MESSAGES
																	.VALID_DISCOUNT_PERCENT,
														},
														min: {
															value: 10,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_MIN,
														},
														max: {
															value: 100,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_MAX,
														},
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															keyboardType="number-pad"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
															returnKeyType="done"
															returnKeyLabel="done"
															InputRightElement={
																<Icon
																	as={MaterialCommunityIcons}
																	name="percent-outline"
																	color="custom.grey1"
																/>
															}
														/>
													)}
												/>
												{errors.discountPercent && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.discountPercent.message}
													</FormControl.HelperText>
												)}
											</>
										) : (
											<>
												<FormControl.Label mb={1}>
													{constants.LABEL.QUANTITY}
												</FormControl.Label>
												<Controller
													control={control}
													name="quantity"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES.QUANTITY_REQUIRED,
														},
														pattern: {
															value: /^[1-9]\d*$/, // Ensures only positive whole numbers
															message:
																'Please enter a valid quantity.',
														},
														validate: value =>
															parseInt(value, 10) >= 1 ||
															'Quantity must be 1 or greater',
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															keyboardType="number-pad"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
															returnKeyType="done"
															returnKeyLabel="done"
														/>
													)}
												/>
												{errors.quantity && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.quantity.message}
													</FormControl.HelperText>
												)}

												<FormControl.Label mt={5}>
													{constants.LABEL.DISCOUNT_PERCENT}
												</FormControl.Label>
												<Controller
													control={control}
													name="discountPercent"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_REQUIRED,
														},
														pattern: {
															value: constants.REGEX.DISCOUNT_PERCENT,
															message:
																constants.VALIDATION_MESSAGES
																	.VALID_DISCOUNT_PERCENT,
														},
														min: {
															value: 10,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_MIN,
														},
														max: {
															value: 100,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_MAX,
														},
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															keyboardType="number-pad"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
															returnKeyType="done"
															returnKeyLabel="done"
															InputRightElement={
																<Icon
																	as={MaterialCommunityIcons}
																	name="percent-outline"
																	color="custom.grey1"
																/>
															}
														/>
													)}
												/>
												{errors.discountPercent && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.discountPercent.message}
													</FormControl.HelperText>
												)}
											</>
										)}
									</FormControl>
								)}

								<FormControl my={1}>
									<HStack justifyContent="space-between">
										<FormControl.Label>
											{constants.LABEL.LOW_STOCK}
										</FormControl.Label>
										<Switch
											onToggle={() => {
												setLowStock(!lowStock);
											}}
											value={lowStock}
											colorScheme="orange"
										/>
									</HStack>
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.DEAL_ENDS_ON}
									</FormControl.Label>
									<Input
										size="xl"
										value={getValues('expiryDateTime')}
										isRequired={true}
										showSoftInputOnFocus={false}
										InputRightElement={
											<Icon
												as={MaterialCommunityIcons}
												size={6}
												name="calendar"
												ml="2"
												onPress={toggleDatePicker}
											/>
										}
									/>
								</FormControl>

								<FormControl alignItems="center">
									<Heading
										fontSize={20}
										justifyContent="center"
										alignItems="center">
										{constants.LABEL.DISCOUNTED_PRICE}
										<Text fontSize={25} color="custom.green" bold>
											{' '}
											${discountedPrice}
										</Text>
									</Heading>
								</FormControl>
							</Stack>

							<Button
								mt={2}
								onPress={handleSubmit(debouncedSubmit)}
								isDisabled={loading}>
								{constants.BUTTON.SAVE_DEAL}
							</Button>
						</VStack>
					</Box>
				</Center>
			</KeyboardAwareScrollView>

			<DateTimePickerModal
				isVisible={isDatePickerVisible}
				mode="datetime"
				onConfirm={handleDateConfirm}
				onCancel={toggleDatePicker}
				minimumDate={new Date(dayjs().add(1, 'day'))}
				display={Platform.select({
					ios: 'inline',
					android: 'datetime',
				})}
			/>

			<ProductSearch
				ref={productSearchRef}
				selectedProduct={selectedProduct}
				setSelectedProduct={setSelectedProduct}
			/>

			<SpinnerOverlay loading={loading} />
		</>
	);
};

const styles = StyleSheet.create({
	cardStyle: {
		shadowColor: constants.COLORS.GREY1,
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.3,
		shadowRadius: 2.84,
		elevation: 3,
		borderRadius: 5,
		padding: 2,
		marginHorizontal: 5,
		backgroundColor: constants.COLORS.WHITE,
		marginTop: 5,
	},
});

export default CreateDeal;

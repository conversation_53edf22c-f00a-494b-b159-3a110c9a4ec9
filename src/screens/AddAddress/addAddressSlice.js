import {createSlice} from '@reduxjs/toolkit';

const addAddressSlice = createSlice({
	name: 'addAddress',
	initialState: {
		coordinate: {latitude: 0, longitude: 0},
		address: null,
	},
	reducers: {
		setCoordinates(state, action) {
			state.coordinate = action.payload;
		},
		setAddress(state, action) {
			state.address = action.payload;
		},
	},
});

export const {setCoordinates, setAddress} = addAddressSlice.actions;

export default addAddressSlice.reducer;

import React, {useCallback, useRef, useState} from 'react';
import {Dimensions, StyleSheet, Platform, TouchableOpacity} from 'react-native';

import {
	Box,
	VStack,
	Center,
	ScrollView,
	FormControl,
	Input,
	Button,
	Icon,
	Text,
	KeyboardAvoidingView,
	useToast,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MapView, {PROVIDER_GOOGLE, Marker} from 'react-native-maps';
import Geolocation from '@react-native-community/geolocation';
import {useFocusEffect} from '@react-navigation/native';
import DeviceInfo from 'react-native-device-info';
import {useDispatch, useSelector} from 'react-redux';

import constants from '../../utils/constants.js';
import ToastAlert from '../../components/ToastAlert';
import {setAddress, setCoordinates} from './addAddressSlice';
import {
	GooglePlaceAutoComplete,
	GooglePlaceCoordinates,
	GooglePlaceName,
} from '../../services/RNGoogleMaps';

const {width, height} = Dimensions.get('window');
const initialCoordinates = {
	latitude: 28.6129,
	longitude: 77.2295,
	latitudeDelta: 8,
	longitudeDelta: 8 * (width / height),
};

const AddAddress = () => {
	/**
	 * Start Initials
	 */
	const [coordinate, setCoordinate] = useState({
		latitude: initialCoordinates.latitude,
		longitude: initialCoordinates.longitude,
		longitudeDelta: 0,
		latitudeDelta: 0,
	});

	const [markerCoordinates, setMarkerCoordinates] = useState({
		latitude: initialCoordinates.latitude,
		longitude: initialCoordinates.longitude,
	});

	const [locations, setLocations] = useState(null);
	const [isSearched, setIsSearched] = useState(false);
	const [addressDesc, setAddressDesc] = useState('');
	const [scrollViewObject, setScrollViewObject] = useState(null);

	const mapRef = useRef();

	const toast = useToast();

	const dispatch = useDispatch();

	const storedCoordinate = useSelector(state => state.addAddress.coordinate);
	const storedAddress = useSelector(state => state.addAddress.address);
	/**
	 * End Initials
	 */

	/**
	 * Google Maps location functions start
	 */
	const getLocations = async text => {
		setAddressDesc(text);
		const data = await GooglePlaceAutoComplete(text);
		if (data && data != {}) setLocations(data);
		setIsSearched(true);
	};

	const getPlaceCoordinates = async place_id => {
		const data = await GooglePlaceCoordinates(place_id);
		setAddressDesc(data.fullName);
		if (data && data != {}) {
			setCoordinate({
				latitude: data.coord.lat,
				longitude: data.coord.lng,
				longitudeDelta: 0.01 * (width / height),
				latitudeDelta: 0.01,
			});
			setIsSearched(false);
		}
	};

	const getPlaceName = async (lat, long, latDelta, longDelta) => {
		const data = await GooglePlaceName(lat, long);
		setMarkerCoordinates({
			latitude: lat,
			longitude: long,
		});

		if (data && data != {}) {
			setCoordinate({
				latitude: data.coord.lat,
				longitude: data.coord.lng,
				longitudeDelta: longDelta,
				latitudeDelta: latDelta,
			});

			setAddressDesc(data.fullName);
		}
	};
	/**
	 * Google maps location functions end
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			(async () => {
				if (!storedCoordinate) {
					Geolocation.getCurrentPosition(
						async info => {
							setCoordinate({
								latitude: info.coords.latitude,
								longitude: info.coords.longitude,
								longitudeDelta: 0.01 * (width / height),
								latitudeDelta: 0.01,
							});

							setMarkerCoordinates({
								latitude: info.coords.latitude,
								longitude: info.coords.longitude,
							});

							dispatch(setCoordinates(coordinate));
						},
						error => {},
						{
							enableHighAccuracy: false,
							timeout: 2000,
						},
					);
				} else {
					setCoordinate({
						latitude: storedCoordinate.latitude,
						longitude: storedCoordinate.longitude,
						longitudeDelta: 0.01 * (width / height),
						latitudeDelta: 0.01,
					});

					setMarkerCoordinates({
						latitude: storedCoordinate.latitude,
						longitude: storedCoordinate.longitude,
					});

					if (storedAddress) return setAddressDesc(storedAddress);
				}
			})();
			mapRef.current.animateToRegion(coordinate, 1000);
		}, [storedCoordinate, storedAddress]),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const GenerateList = () => {
		return isSearched ? (
			<Box>
				{locations?.map(item => {
					return (
						<Box
							borderBottomWidth="1"
							_dark={{
								borderColor: 'muted.50',
							}}
							borderColor="muted.800"
							pl={['0', '4']}
							pr={['0', '5']}
							py="2"
							key={item.place_id}>
							<TouchableOpacity
								style={styles.button}
								onPress={() => getPlaceCoordinates(item.place_id)}>
								<Text>{item.description}</Text>
							</TouchableOpacity>
						</Box>
					);
				})}
			</Box>
		) : null;
	};

	const goToMyLocation = async () => {
		mapRef.current.animateCamera({
			center: {latitude: coordinate.latitude, longitude: coordinate.longitude},
		});
		setMarkerCoordinates({
			latitude: coordinate.latitude,
			longitude: coordinate.longitude,
		});

		dispatch(setCoordinates(coordinate));
		dispatch(setAddress(addressDesc));

		toast.show({
			avoidKeyboard: true,
			render: ({id}) => {
				return (
					<ToastAlert
						id={id}
						toast={toast}
						type="success"
						message={constants.SUCCESS_MESSAGE.ADDRESS_SAVED}
					/>
				);
			},
		});
	};

	const clearAddressInput = () => {
		setAddressDesc('');
	};
	/**
	 * End Methods
	 */

	return (
		<KeyboardAvoidingView
			behavior={Platform.OS === 'ios' ? 'position' : 'height'}
			style={{flex: 1, paddingBottom: 2}}>
			<ScrollView
				ref={ref => {
					setScrollViewObject(ref);
				}}
				onContentSizeChange={() =>
					scrollViewObject?.scrollToEnd({animated: true})
				}>
				<VStack
					h={
						!isSearched
							? height -
								Platform.select({
									ios: DeviceInfo.hasNotch() ? 180 : 110,
									android: 130,
								})
							: height + 200
					}>
					<Center w="100%">
						<Box w="100%" bg="white">
							<VStack>
								<MapView
									ref={mapRef}
									provider={PROVIDER_GOOGLE}
									style={styles.map}
									showsIndoors={false}
									showsBuildings={false}
									showsScale={true}
									showsUserLocation={true}
									showsMyLocationButton={true}
									initialRegion={{
										latitude: coordinate.latitude,
										longitude: coordinate.longitude,
										latitudeDelta: coordinate.latitudeDelta,
										longitudeDelta: coordinate.longitudeDelta,
									}}
									onRegionChangeComplete={region => {
										getPlaceName(
											region.latitude,
											region.longitude,
											region.latitudeDelta,
											region.longitudeDelta,
										);
									}}>
									{coordinate && (
										<Marker
											draggable
											coordinate={{
												latitude: markerCoordinates.latitude,
												longitude: markerCoordinates.longitude,
											}}
											onDragEnd={e =>
												setMarkerCoordinates(e.nativeEvent.coordinate)
											}
											showsUserLocation={true}
											followsUserLocation={true}
										/>
									)}
								</MapView>

								<VStack space={2} px={4} mt={5}>
									<FormControl>
										<Input
											size="xl"
											fontSize={16}
											value={addressDesc}
											onFocus={_ => { 
												setIsSearched(true);
												getLocations(addressDesc);
											}}
											onChangeText={text => getLocations(text)}
											InputLeftElement={
												<Icon
													as={MaterialCommunityIcons}
													name="map-marker-radius-outline"
													size={6}
												/>
											}
											InputRightElement={
												addressDesc ? (
													<TouchableOpacity onPress={clearAddressInput}>
														<Icon
															as={MaterialCommunityIcons}
															name="close-circle-outline"
															size={6}
														/>
													</TouchableOpacity>
												) : (
													''
												)
											}
										/>
										<GenerateList />
									</FormControl>
									<Button
										mt={Platform.OS === 'android' ? '1' : 3}
										onPress={goToMyLocation}>
										{constants.BUTTON.SAVE_ADDRESS}
									</Button>
								</VStack>
							</VStack>
						</Box>
					</Center>
				</VStack>
			</ScrollView>
		</KeyboardAvoidingView>
	);
};

const styles = StyleSheet.create({
	flexOne: {
		flex: 1,
	},
	map: {
		width: width,
		height: height - height / 2.5,
	},
});

export default AddAddress;

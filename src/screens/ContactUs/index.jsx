import React from 'react';
import {Dimensions, Linking, Platform, TouchableOpacity, SafeAreaView} from 'react-native';

import {Box, Center, HStack, Icon, ScrollView, Stack, Text} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import ContactUsSvg from './ContactUsSvg';
import {
	APP_CONTACT_ADDRESS,
	APP_CONTACT_COORDINATES,
	APP_CONTACT_MAIL,
	APP_CONTACT_PHONE,
} from '../../utils/constants';

const {height: viewportHeight} = Dimensions.get('window');

const ContactUs = () => {
	/**
	 * Start Methods
	 */
	const openDialer = () => {
		Platform.OS === 'ios'
			? Linking.openURL(`telprompt:${APP_CONTACT_PHONE}`)
			: Linking.openURL(`tel:${APP_CONTACT_PHONE}`);
	};
	const openMailBox = () => {
		Linking.openURL(`mailto:${APP_CONTACT_MAIL}`);
	};

	const openMap = () => {
		Platform.OS === 'ios'
			? Linking.openURL(`maps:${APP_CONTACT_COORDINATES}`)
			: Linking.openURL(`geo:${APP_CONTACT_COORDINATES}`);
	};
	/**
	 * End Methods
	 */

	return (
		<SafeAreaView style={{ marginTop: 10 }}>
			<Center w="100%" backgroundColor="white">
				<Box w="90%" my={10} h={viewportHeight}>
					<Stack space={6} mx={3}>
						<TouchableOpacity onPress={openDialer}>
							<HStack space={2} justifyContent="flex-start" alignItems="center">
								<Icon
									as={MaterialCommunityIcons}
									name="cellphone"
									size={6}
									color="custom.orange"
								/>
								<Text>{APP_CONTACT_PHONE}</Text>
							</HStack>
						</TouchableOpacity>

						<TouchableOpacity onPress={openMailBox}>
							<HStack space={2} justifyContent="flex-start" alignItems="center">
								<Icon
									as={MaterialCommunityIcons}
									name="email"
									size={6}
									color="custom.orange"
								/>
								<Text>{APP_CONTACT_MAIL}</Text>
							</HStack>
						</TouchableOpacity>

						<TouchableOpacity onPress={openMap}>
							<HStack
								space={2}
								mr={3}
								justifyContent="flex-start"
								alignItems="center">
								<Icon
									as={MaterialCommunityIcons}
									name="map-marker"
									size={6}
									color="custom.orange"
								/>
								<Text>{APP_CONTACT_ADDRESS}</Text>
							</HStack>
						</TouchableOpacity>

						<Box m="auto">
							<ContactUsSvg style={{marginTop: '10%'}} />
						</Box>
					</Stack>
				</Box>
			</Center>
		</SafeAreaView>
	);
};

export default ContactUs;

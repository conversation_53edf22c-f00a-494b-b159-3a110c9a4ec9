import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getDealDetail = createAsyncThunk(
	'editDeal/getDealDetail',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.MY_DEAL_DETAIL + `/${data}`,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const updateDeal = createAsyncThunk(
	'editDeal/updateDeal',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.UPDATE_DEAL + `/${data.dealId}`,
				method: 'PUT',
				data: data.data,
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const saveTemplate = createAsyncThunk(
	'editDeal/saveTemplate',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.SAVE_TEMPLATE,
				method: 'POST',
				data: data,
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const editDealSlice = createSlice({
	name: 'editDeal',
	initialState: {},
});

export default editDealSlice.reducer;

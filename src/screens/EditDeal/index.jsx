import * as React from 'react';
import {
	Box,
	Button,
	Center,
	FormControl,
	Heading,
	HStack,
	Icon,
	Image,
	Input,
	Stack,
	Switch,
	Text,
	useToast,
	VStack,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useFocusEffect} from '@react-navigation/native';
import {Controller, useForm} from 'react-hook-form';
import {useCallback, useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import dayjs from 'dayjs';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import {Platform} from 'react-native';
import _ from 'lodash';
import RNPickerSelect from 'react-native-picker-select';

import constants, {
	DEAL_TYPE_OPTIONS,
	DISCOUNT_TYPE_OPTIONS,
} from '../../utils/constants';
import {getOffers} from '../CreateDeal/createDealSlice';
import DealModel from '../../models/dealModel';
import {getDealDetail} from '../MyDealDetail/myDealDetailSlice';
import {saveTemplate, updateDeal} from './editDealSlice';
import ToastAlert from '../../components/ToastAlert';
import SpinnerOverlay from '../../components/SpinnerOverlay';
import {toUri} from '../../utils/helpers';

const EditDeal = ({route}) => {
	/**
	 * Start Initials
	 */
	const {
		control,
		handleSubmit,
		setValue,
		getValues,
		watch,
		formState: {errors},
	} = useForm({
		defaultValues: {
			type: 'flash',
			discountType: 'percent',
		},
	});
	const watchDiscountType = watch('discountType');
	const watchProductPrice = watch('productPrice');
	const watchDiscountPercent = watch('discountPercent');
	const watchDiscountPrice = watch('discountPrice');
	const watchQuantity = watch('quantity');

	const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
	const [offers, setOffers] = useState([]);
	const [loading, setLoading] = useState(false);
	const [lowStock, setLowStock] = useState(undefined);
	const [dealDetail, setDealDetail] = useState(DealModel);
	const [discountedPrice, setDiscountedPrice] = useState(0);

	const dispatch = useDispatch();

	const toast = useToast();

	dayjs.extend(customParseFormat);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchOffers();
			fetchDealDetail();
		}, []),
	);

	useEffect(() => {
		setTimeout(() => {
			if (watchProductPrice) {
				setDiscountedPrice(watchProductPrice);
			} else {
				setDiscountedPrice(0);
			}

			if (watchDiscountType === 'fix') {
				if (watchProductPrice && watchDiscountPrice) {
					const calculatedPrice = watchProductPrice - watchDiscountPrice;
					setDiscountedPrice(calculatedPrice);
				}
			} else if (
				watchDiscountType === 'percent' ||
				watchDiscountType === 'quantity'
			) {
				if (watchProductPrice && watchDiscountPercent) {
					const calculatedPrice =
						watchProductPrice -
						(watchProductPrice * watchDiscountPercent) / 100;
					const finalCalculatedPrice = parseFloat(calculatedPrice).toFixed(2);
					setDiscountedPrice(finalCalculatedPrice);
				}
			}
		}, 1);
	}, [
		watchProductPrice,
		watchDiscountPercent,
		watchDiscountPrice,
		watchDiscountType,
		watchQuantity,
	]);

	useEffect(() => {
		setValue(
			'discountPercent',
			dealDetail?.discountPercent ? dealDetail.discountPercent.toString() : '',
		);
		setValue(
			'discountPrice',
			dealDetail?.discountPrice ? dealDetail.discountPrice.toString() : '',
		);
		setValue(
			'quantity',
			dealDetail?.quantity ? dealDetail.quantity.toString() : 1,
		);
	}, [watchDiscountType]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchDealDetail = () => {
		setLoading(true);
		dispatch(getDealDetail(route.params.dealId))
			.unwrap()
			.then(res => {
				setDealDetail(res);
				setValue('type', res.type);
				setValue('discountType', res.discountType);
				setValue('productPrice', res.productPrice.toString());
				setValue('discountPercent', res.discountValue.toString());
				setValue('discountPrice', res.discountValue.toString());
				setValue('quantity', res.quantity ? res.quantity.toString() : 1);
				setValue(
					'expiryDateTime',
					dayjs(res.expiryDateTime).format('MM-DD-YYYY hh:mm A'),
				);
				setLowStock(res.lowStock);
				setLoading(false);
			})
			.catch(() => setLoading(false));
	};

	const fetchOffers = () => {
		setLoading(true);
		dispatch(getOffers())
			.unwrap()
			.then(res => {
				setOffers(res);
				setLoading(false);
			})
			.catch(() => {
				setLoading(false);
			});
	};

	const onSubmit = data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}
		setLoading(true);

		let discountValue = 0;
		let discountPrice = data.productPrice;

		if (data.discountType === 'fix') {
			discountValue = data.discountPrice;
			discountPrice = data.productPrice - data.discountPrice;
		} else if (
			data.discountType === 'percent' ||
			data.discountType === 'quantity'
		) {
			discountValue = data.discountPercent;
			discountPrice = discountedPrice;
		}

		const expiryDateTime = dayjs(data.expiryDateTime, 'MM-DD-YYYY hh:mm A');
		if (dayjs(expiryDateTime).isBefore(dayjs())) {
			toast.show({
				avoidKeyboard: true,
				render: ({id}) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={constants.VALIDATION_MESSAGES.VALID_DEAL_ENDS_ON}
						/>
					);
				},
			});
			setLoading(false);
			return false;
		}

		const deal = {
			type: data.type,
			productPrice: Number(data.productPrice),
			discountType: data.discountType,
			discountPrice: discountPrice,
			discountValue: Number(discountValue),
			expiryDateTime: expiryDateTime,
			lowStock: lowStock,
			quantity: Number(watchQuantity) > 1 ? Number(watchQuantity) : 1,
		};

		dispatch(updateDeal({dealId: route.params.dealId, data: deal}))
			.unwrap()
			.then(() => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.DEAL_UPDATED}
							/>
						);
					},
				});
				setLoading(false);
			})
			.catch(err => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={err.message}
							/>
						);
					},
				});
				setLoading(false);
			});
	};

	const debouncedSubmit = _.debounce(onSubmit, 1000, {
		leading: true,
		trailing: false,
	});

	const saveAsTemplate = () => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}

		setLoading(true);
		dispatch(saveTemplate(dealDetail))
			.unwrap()
			.then(() => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.TEMPLATE_SAVED}
							/>
						);
					},
				});
				setLoading(false);
				fetchDealDetail();
			})
			.catch(err => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={err.message}
							/>
						);
					},
				});
				setLoading(false);
			});
	};

	const debouncedSaveAsTemplate = _.debounce(saveAsTemplate, 1000, {
		leading: true,
		trailing: false,
	});

	const toggleDatePicker = () => {
		setDatePickerVisibility(prevState => !prevState);
	};

	const handleDateConfirm = date => {
		setValue('expiryDateTime', dayjs(date).format('MM-DD-YYYY hh:mm A'));
		toggleDatePicker();
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Center w="100%">
					<Box pt={6} pb={6} w="90%">
						<VStack space={5} px={3}>
							{dealDetail && (
								<HStack space={1}>
									<Box w="30%">
										<Image
											source={{uri: toUri(dealDetail.product.photo)}}
											alt="image"
											resizeMode="contain"
											size="xl"
										/>
									</Box>
									<Stack space={2} w="70%" justifyContent="center">
										<Stack space={2}>
											<Heading
												color="custom.black"
												fontSize={16}
												fontStyle="normal">
												{dealDetail.product.name}
											</Heading>
											<Text fontSize={12}>{dealDetail.product.size} ml</Text>
										</Stack>
										<HStack space={3}>
											{['fix', 'percent', 'quantity'].includes(
												dealDetail.discountType,
											) && (
												<Text fontSize={12} strikeThrough>
													${dealDetail.productPrice}
												</Text>
											)}
											<Text fontSize={14} bold>
												${dealDetail.discountPrice}
											</Text>
										</HStack>
									</Stack>
								</HStack>
							)}

							<Stack space={5}>
								<FormControl>
									<FormControl.Label>
										{constants.LABEL.DEAL_TYPE}
									</FormControl.Label>
									<Controller
										control={control}
										name="type"
										rules={{
											required: {
												value: true,
												message:
													constants.VALIDATION_MESSAGES.DEAL_TYPE_REQUIRED,
											},
										}}
										render={({field: {onChange, value}}) => (
											<RNPickerSelect
												pickerProps={{
													accessibilityLabel: 'Choose deal type',
												}}
												style={{
													inputIOS: {
														borderBottomColor: constants.COLORS.GREY3,
														borderBottomWidth: 1,
														padding: 1,
														width: '100%',
														color: constants.COLORS.DARK1,
														fontSize: 16,
													},
													inputAndroid: {
														borderBottomColor: constants.COLORS.GREY3,
														borderBottomWidth: 1,
														padding: 1,
														width: '100%',
														color: constants.COLORS.DARK1,
														fontSize: 16,
													},
													placeholder: {
														color: constants.COLORS.DARK1,
													},
												}}
												selectedValue={value}
												value={value}
												onValueChange={onChange}
												placeholder={{label: 'Choose deal type'}}
												items={DEAL_TYPE_OPTIONS}
											/>
										)}
									/>
									{errors.type && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.type.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.DISCOUNT_TYPE}
									</FormControl.Label>
									<Controller
										control={control}
										name="discountType"
										rules={{
											required: {
												value: true,
												message:
													constants.VALIDATION_MESSAGES.DISCOUNT_TYPE_REQUIRED,
											},
										}}
										render={({field: {onChange, value}}) => (
											<RNPickerSelect
												pickerProps={{
													accessibilityLabel: 'Choose discount type',
												}}
												style={{
													inputIOS: {
														borderBottomColor: constants.COLORS.GREY3,
														borderBottomWidth: 1,
														padding: 1,
														width: '100%',
														color: constants.COLORS.DARK1,
														fontSize: 16,
													},
													inputAndroid: {
														borderBottomColor: constants.COLORS.GREY3,
														borderBottomWidth: 1,
														padding: 1,
														width: '100%',
														color: constants.COLORS.DARK1,
														fontSize: 16,
													},
													placeholder: {
														color: constants.COLORS.DARK1,
													},
												}}
												selectedValue={value}
												value={value}
												onValueChange={onChange}
												placeholder={{label: 'Choose discount type'}}
												items={[
													...DISCOUNT_TYPE_OPTIONS,
													...offers.map(v => ({label: v.name, value: v.name})),
												]}
											/>
										)}
									/>
									{errors.discountType && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.discountType.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label mb={1}>
										{constants.LABEL.PRODUCT_PRICE}
									</FormControl.Label>
									<Controller
										control={control}
										name="productPrice"
										rules={{
											required: {
												value: true,
												message: constants.VALIDATION_MESSAGES.PRODUCT_PRICE,
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												keyboardType="decimal-pad"
												autoCorrect={false}
												onBlur={onBlur}
												onChangeText={onChange}
												value={value}
												returnKeyType="done"
												returnKeyLabel="done"
												InputLeftElement={
													<Icon
														as={MaterialCommunityIcons}
														name="currency-usd"
													/>
												}
											/>
										)}
									/>
									{errors.productPrice && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.productPrice.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								{['fix', 'percent', 'quantity'].includes(watchDiscountType) && (
									<FormControl>
										{watchDiscountType === 'fix' ? (
											<>
												<FormControl.Label mb={1}>
													{constants.LABEL.DISCOUNT_PRICE}
												</FormControl.Label>
												<Controller
													control={control}
													name="discountPrice"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PRICE_REQUIRED,
														},
														pattern: {
															value: constants.REGEX.DISCOUNT_PRICE,
															message:
																constants.VALIDATION_MESSAGES
																	.VALID_DISCOUNT_PRICE,
														},
														validate: value => {
															if (value > parseFloat(watchProductPrice)) {
																return constants.VALIDATION_MESSAGES
																	.MATCH_DISCOUNT_PRICE;
															}
														},
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															keyboardType="number-pad"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
															returnKeyType="done"
															returnKeyLabel="done"
															InputLeftElement={
																<Icon
																	as={MaterialCommunityIcons}
																	name="currency-usd"
																/>
															}
														/>
													)}
												/>
												{errors.discountPrice && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.discountPrice.message}
													</FormControl.HelperText>
												)}
											</>
										) : watchDiscountType === 'percent' ? (
											<>
												<FormControl.Label mb={1}>
													{constants.LABEL.DISCOUNT_PERCENT}
												</FormControl.Label>
												<Controller
													control={control}
													name="discountPercent"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_REQUIRED,
														},
														pattern: {
															value: constants.REGEX.DISCOUNT_PERCENT,
															message:
																constants.VALIDATION_MESSAGES
																	.VALID_DISCOUNT_PERCENT,
														},
														min: {
															value: 10,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_MIN,
														},
														max: {
															value: 100,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_MAX,
														},
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															keyboardType="number-pad"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
															returnKeyType="done"
															returnKeyLabel="done"
															InputRightElement={
																<Icon
																	as={MaterialCommunityIcons}
																	name="percent"
																/>
															}
														/>
													)}
												/>
												{errors.discountPercent && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.discountPercent.message}
													</FormControl.HelperText>
												)}
											</>
										) : (
											<>
												<FormControl.Label mb={1}>
													{constants.LABEL.QUANTITY}
												</FormControl.Label>
												<Controller
													control={control}
													name="quantity"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES.QUANTITY_REQUIRED,
														},
														pattern: {
															value: /^[1-9]\d*$/, // Ensures only positive whole numbers
															message: 'Please enter a valid quantity.',
														},
														validate: value =>
															parseInt(value, 10) >= 1 ||
															'Quantity must be 1 or greater',
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															keyboardType="number-pad"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
															returnKeyType="done"
															returnKeyLabel="done"
														/>
													)}
												/>
												{errors.quantity && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.quantity.message}
													</FormControl.HelperText>
												)}

												<FormControl.Label mt={5}>
													{constants.LABEL.DISCOUNT_PERCENT}
												</FormControl.Label>
												<Controller
													control={control}
													name="discountPercent"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_REQUIRED,
														},
														pattern: {
															value: constants.REGEX.DISCOUNT_PERCENT,
															message:
																constants.VALIDATION_MESSAGES
																	.VALID_DISCOUNT_PERCENT,
														},
														min: {
															value: 10,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_MIN,
														},
														max: {
															value: 100,
															message:
																constants.VALIDATION_MESSAGES
																	.DISCOUNT_PERCENT_MAX,
														},
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															keyboardType="number-pad"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
															returnKeyType="done"
															returnKeyLabel="done"
															InputRightElement={
																<Icon
																	as={MaterialCommunityIcons}
																	name="percent-outline"
																	color="custom.grey1"
																/>
															}
														/>
													)}
												/>
												{errors.discountPercent && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.discountPercent.message}
													</FormControl.HelperText>
												)}
											</>
										)}
									</FormControl>
								)}

								<FormControl my={1}>
									<HStack justifyContent="space-between">
										<FormControl.Label>
											{constants.LABEL.LOW_STOCK}
										</FormControl.Label>
										<Switch
											onToggle={() => {
												setLowStock(!lowStock);
											}}
											defaultIsChecked={lowStock}
											value={lowStock}
											colorScheme="orange"
										/>
									</HStack>
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.DEAL_ENDS_ON}
									</FormControl.Label>
									<Input
										size="xl"
										value={getValues('expiryDateTime')}
										showSoftInputOnFocus={false}
										InputRightElement={
											<Icon
												as={MaterialCommunityIcons}
												size={6}
												name="calendar"
												ml="2"
												onPress={toggleDatePicker}
											/>
										}
									/>
								</FormControl>

								<FormControl alignItems="center">
									<Heading
										fontSize={20}
										justifyContent="center"
										alignItems="center">
										{constants.LABEL.DISCOUNTED_PRICE}
										<Text fontSize={25} color="custom.green" bold>
											{' '}
											${discountedPrice}
										</Text>
									</Heading>
								</FormControl>
							</Stack>

							<Button
								mt={4}
								onPress={handleSubmit(debouncedSubmit)}
								isDisabled={loading}>
								{constants.BUTTON.SAVE_DEAL}
							</Button>

							<Button
								variant="outline"
								_text={{color: 'black'}}
								backgroundColor="white"
								borderColor="custom.orange"
								onPress={debouncedSaveAsTemplate}
								isDisabled={loading}>
								{constants.LINK.SAVE_AS_TEMPLATE}
							</Button>
						</VStack>
					</Box>
				</Center>
			</KeyboardAwareScrollView>

			<DateTimePickerModal
				isVisible={isDatePickerVisible}
				display={Platform.select({
					ios: 'inline',
					android: 'calendar',
				})}
				mode="datetime"
				onConfirm={handleDateConfirm}
				onCancel={toggleDatePicker}
				minimumDate={new Date(dayjs().add(1, 'day'))}
				date={
					new Date(dayjs(getValues('expiryDateTime'), 'MM-DD-YYYY hh:mm A'))
				}
			/>

			<SpinnerOverlay loading={loading} />
		</>
	);
};

export default EditDeal;

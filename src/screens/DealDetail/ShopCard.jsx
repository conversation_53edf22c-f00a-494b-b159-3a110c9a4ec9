import React, {useCallback} from 'react';
import {
	Box,
	Text,
	Heading,
	VStack,
	Center,
	HStack,
	Pressable,
	Button,
	useToast,
} from 'native-base';
import {Linking, TouchableOpacity} from 'react-native';
import {StackActions} from '@react-navigation/native';

import constants from '../../utils/constants';
import ToastAlert from '../../components/ToastAlert';

const ShopCard = props => {
	/**
	 * Start Initials
	 */
	const {
		navigation,
		name,
		description,
		serviceOptions,
		websiteUrl,
		shopId,
		productPrice,
		discountPrice,
		discountType,
		discountValue,
		distance = 0,
	} = props;

	const toast = useToast();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const openWebsiteUrl = useCallback(async () => {
		// Checking if the link is supported for links with custom URL scheme.
		const supported = await Linking.canOpenURL(websiteUrl);

		if (supported) {
			// Opening the link with some app, if the URL scheme is "http" the web link should be opened
			// by some browser in the mobile
			await Linking.openURL(websiteUrl);
		} else {
			toast.show({
				avoidKeyboard: true,
				render: ({id}) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={`Don't know how to open this URL: ${websiteUrl}`}
						/>
					);
				},
			});
		}
	}, [websiteUrl]);
	/**
	 * End Methods
	 */

	return (
		<Center w="100%">
			<Box w="100%">
				<VStack space={1} alignItems="center">
					<HStack w="100%" justifyContent="space-between" space={2}>
						<VStack space={1} width="50%">
							<TouchableOpacity
								onPress={() =>
									navigation.dispatch(
										StackActions.push(constants.ROUTE.SHOP_DETAILS, {
											shopId: shopId,
										}),
									)
								}>
								<Heading fontSize={15} bold>
									{name}
								</Heading>

								<Text fontSize={12}>{description}</Text>

								<Text fontSize={12} flexShrink={1}>
									<Text fontSize={12} bold>
										Service Option:
									</Text>{' '}
									{serviceOptions.join(', ')}
								</Text>

								<Text fontSize={12}>
									<Text fontSize={12} bold>
										Distance:{' '}
									</Text>
									{(parseFloat(distance) / 1609).toFixed(2)} miles
								</Text>
							</TouchableOpacity>
						</VStack>
						<VStack justifyContent="space-between">
							<HStack
								space={2}
								justifyContent="center"
								alignItems="center"
								mt={1}>
								<VStack>
									{[
										constants.DEAL_TYPE.FIX,
										constants.DEAL_TYPE.PERCENT,
										constants.DEAL_TYPE.QUANTITY,
									].includes(discountType) && (
										<Text fontSize={12} strikeThrough color="custom.red">
											${productPrice}
										</Text>
									)}
									<Text fontSize={13} bold color="custom.green">
										${discountPrice}
									</Text>
								</VStack>
								{
									websiteUrl && <Pressable>
									{({isHovered, isFocused, isPressed}) => {
										return (
											<Box
												bg={
													isPressed
														? 'coolGray.200'
														: isHovered
															? 'coolGray.200'
															: 'white'
												}
												borderWidth={1}
												borderColor="custom.grey3"
												borderRadius={3}
												style={{
													transform: [
														{
															scale: isPressed ? 0.96 : 1,
														},
													],
												}}>
												<Button
													size="xs"
													bg="white"
													borderRadius={1}
													px={2}
													py={1}
													_text={{
														fontSize: 12,
														color: isPressed ? 'white' : 'custom.orange',
													}}
													bold
													onPress={ openWebsiteUrl}>
													Visit Site
												</Button>
											</Box>
										);
									}}
								</Pressable>
								}								
							</HStack>

							<Center bg="custom.green" px={1} borderRadius={3}>
								{[
									constants.DEAL_TYPE.FIX,
									constants.DEAL_TYPE.PERCENT,
									constants.DEAL_TYPE.QUANTITY,
								].includes(discountType) ? (
									<>
										<Text color="white" fontSize={11} bold>
											{discountType === constants.DEAL_TYPE.PERCENT ||
											discountType === constants.DEAL_TYPE.QUANTITY
												? `${discountValue}%`
												: `$${discountValue}`}{' '}
											OFF
										</Text>
									</>
								) : (
									<Text color="white" fontSize={11} bold>
										{discountType}
									</Text>
								)}
							</Center>
						</VStack>
					</HStack>
				</VStack>
			</Box>
		</Center>
	);
};

export default ShopCard;

import React, {useEffect, useState} from 'react';
import {Box, Center, Divider, ScrollView, Skeleton} from 'native-base';
import {useDispatch} from 'react-redux';

import DealDetailCard from './DealDetailCard';
import ShopCard from './ShopCard';
import {getDealDetail, getDealRetailers} from './dealDetailSlice';
import dealModel from '../../models/dealModel';
import {useRoute} from '@react-navigation/native';

const DealDetail = ({navigation, route}) => {
	/**
	 * Start Initials
	 */
	const [deal, setDeal] = useState(dealModel);
	const [productId, setProductId] = useState(null);
	const [businessId, setBusinessId] = useState(null);
	const [shops, setShops] = useState([]);
	const [isDealLoaded, setIsDealLoaded] = useState(true);
	const [isStoresLoaded, setIsStoresLoaded] = useState(true);

	// Define the router for get the id from the deep link
	const router = useRoute();

	// Get deal id from the deep link
	const {id} = router.params;

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		if (id) {
			fetchDealDetail(id);
		} else {
			fetchDealDetail(route.params.dealId);
		}
	}, []);
	useEffect(() => {
		if (productId && businessId) fetchDealRetailers();
	}, [productId, businessId]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchDealDetail = (dealId) => {
		setIsDealLoaded(false);
		dispatch(getDealDetail(dealId))
			.unwrap()
			.then(res => {
				setDeal(res[0]);
				setProductId(res[0].product._id);
				setBusinessId(res[0].business._id);
				setIsDealLoaded(true);
			});
	};

	const fetchDealRetailers = () => {
		setIsStoresLoaded(false);
		dispatch(getDealRetailers({businessId: businessId, productId: productId}))
			.unwrap()
			.then(res => {
				setShops(res);
				setIsStoresLoaded(true);
			});
	};
	/**
	 * End Methods
	 */

	return (
		<ScrollView>
			<Center w="100%" bg="white">
				<Box my={5} w="90%">
					{deal && (
						<DealDetailCard
							productPrice={deal.productPrice}
							discountPrice={deal.discountPrice}
							product={deal.product}
							brand={deal.brand}
							category={deal.category}
							subcategory={deal.subcategory}
							discountValue={deal.discountValue}
							discountType={deal.discountType}
							dealId={id ? id : route.params.dealId}
							isDealLoaded={isDealLoaded}
							quantity={deal.quantity}
							lowStock={deal.lowStock}
						/>
					)}

					<Skeleton
						h={120}
						mt={5}
						startColor="custom.orange"
						isLoaded={isStoresLoaded}>
						{shops.map(item => (
							<Box key={item._id}>
								<Divider bg="custom.grey3" thickness={1} my={3} />
								<ShopCard
									navigation={navigation}
									shopId={item._id}
									name={item.name}
									description={item.description}
									serviceOptions={item.serviceOptions}
									websiteUrl={item.websiteUrl}
									productPrice={item.deal.productPrice}
									discountPrice={item.deal.discountPrice}
									discountType={item.deal.discountType}
									discountValue={item.deal.discountValue}
									distance={item.distance}
								/>
							</Box>
						))}
					</Skeleton>
				</Box>
			</Center>
		</ScrollView>
	);
};

export default DealDetail;

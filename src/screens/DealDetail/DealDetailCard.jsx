import React, { useEffect, useState } from 'react';
import {
	Box,
	Text,
	Heading,
	Center,
	HStack,
	Image,
	VStack,
	Divider,
	Link,
	IconButton,
	Skeleton,
	Icon,
	Flex,
	Row,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useDispatch, useSelector } from 'react-redux';

import constants, { DEAL_DETAILS_DEEP_LINK } from '../../utils/constants';
import { toUri } from '../../utils/helpers';
import { markFavoriteDeal, unFavoriteDeal } from '../Favorite/favoriteDealSlice';
import { Share } from 'react-native';

const DealDetailCard = props => {
	/**
	 * Start Initials
	 */
	const {
		product,
		quantity,
		discountPrice,
		productPrice,
		brand,
		category,
		subcategory,
		dealId,
		isDealLoaded,
		discountValue,
		discountType,
		lowStock,
	} = props;

	const [expanded, setExpanded] = useState(false);
	const [favorite, setFavorite] = useState(null);

	const dispatch = useDispatch();

	const fetchFavoriteDealsIDs = useSelector(
		state => state.favoriteDeal.favorites,
	);

	const user = useSelector(state => state.userSignIn.userData);
	const isGuestUser = useSelector(state => state.userSignIn.isGuestUser);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		setFavoriteDeal();
	}, [fetchFavoriteDealsIDs]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const setFavoriteDeal = () => {
		fetchFavoriteDealsIDs.some(o => {
			if (o.deal === dealId) {
				setFavorite(o);
			}
		});
	};

	const markDealUnFavorite = () => {
		dispatch(unFavoriteDeal(favorite._id));
	};

	const markDealFavorite = () => {
		dispatch(markFavoriteDeal(dealId));
	};

	// Share the deal url with id using dynamic link.
	const shareLink = async () => {
		try {
			await Share.share({
				message: `${DEAL_DETAILS_DEEP_LINK}/${dealId}`,
				url: `${DEAL_DETAILS_DEEP_LINK}/${dealId}`,
				title: constants.API_ROUTES.DELETE_DEAL,
			});

		} catch (error) {
			console.warn('Error sharing the link:', error.message);
		}
	};

	/**
	 * End Methods
	 */

	return (
		<Center w="100%">
			<Box w="100%">
				<Box mb={4}>
					<Center>
						<Skeleton
							h="250"
							startColor="custom.orange"
							isLoaded={isDealLoaded}>
							<Image
								source={{ uri: toUri(product.photo) }}
								alt="image"
								resizeMode="contain"
								size="2xl"
							/>
						</Skeleton>

						<Box position="absolute" top={0} right={-10}>
							{!isDealLoaded ? (
								<Text />
							) : (
								<IconButton
									variant="ghost"
									size="lg"
									_icon={{
										as: MaterialCommunityIcons,
										name: 'share-outline',
										color: 'custom.dark1',
									}}
									onPress={shareLink}
								/>
							)}
						</Box>

						<Box position="absolute" top={10} right={-10}>
							{!isGuestUser && user?.role === constants.ROLE.CUSTOMER ? (
								fetchFavoriteDealsIDs.some(o => o.deal === dealId) ? (
									<IconButton
										variant="ghost"
										size="lg"
										_icon={{
											as: MaterialCommunityIcons,
											name: 'heart',
											color: 'custom.red',
										}}
										onPress={markDealUnFavorite}
									/>
								) : (
									<IconButton
										variant="ghost"
										size="lg"
										_icon={{
											as: MaterialCommunityIcons,
											name: 'heart-outline',
											color: 'custom.dark1',
										}}
										onPress={markDealFavorite}
									/>
								)
							) : (
								<Text />
							)}
						</Box>
					</Center>
				</Box>
				<VStack space={2}>
					<Skeleton startColor="custom.orange" isLoaded={isDealLoaded}>
						<Flex flexDirection="row">
							<Heading fontSize={18} bold>
								{product.name}
								{quantity > 1 ? (
									<>
										<Text> - </Text>
										<Text bg={'#F5F5F5'} borderRadius={8} ml={2} fontSize={12}>
											{' '}
											Pack of {quantity}{' '}
										</Text>
									</>
								) : null}
							</Heading>
						</Flex>
					</Skeleton>

					<HStack justifyContent="space-between">
						<Skeleton
							w="30%"
							startColor="custom.orange"
							isLoaded={isDealLoaded}>
							<VStack>
								<Text color="custom.orange">{constants.LABEL.PRICE}</Text>
								<HStack alignItems="center" space={2}>
									{['fix', 'percent'].includes(discountType) && (
										<Text strikeThrough color="custom.red">
											${productPrice}
										</Text>
									)}
									<Text bold fontSize={21} color="custom.green">
										${discountPrice}
									</Text>
									<Text color="custom.red" fontSize={10} bold mt={2}>
										{lowStock && constants.TEXT.LOW_STOCK}
									</Text>
								</HStack>
							</VStack>
						</Skeleton>
						<Skeleton
							w="25%"
							startColor="custom.orange"
							isLoaded={isDealLoaded}>
							<VStack>
								<Text color="custom.orange">{constants.LABEL.VOLUME}</Text>
								<Text>ABV {product.abv ? `${product.abv}%` : 'N/A'}</Text>
							</VStack>
						</Skeleton>
					</HStack>

					<Divider bg="custom.grey3" thickness={1} />

					<Heading fontSize={16}>{constants.TITLE.PRODUCT_DETAILS}</Heading>

					<Skeleton startColor="custom.orange" isLoaded={isDealLoaded}>
						<Box>
							{expanded ? (
								<VStack space={1}>
									<Text fontSize={13} mb={2}>
										{product.description}
									</Text>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.QUANTITY} -
										</Text>
										<Text
											textTransform="capitalize"
											fontSize={13}
											flexShrink="1">
											{quantity}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.BRAND} -
										</Text>
										<Text
											textTransform="capitalize"
											fontSize={13}
											flexShrink="1">
											{brand.name}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.CATEGORY} -
										</Text>
										<Text
											textTransform="capitalize"
											fontSize={13}
											flexShrink="1">
											{category.name}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.SUBCATEGORY} -
										</Text>
										<Text
											textTransform="capitalize"
											fontSize={13}
											flexShrink="1">
											{subcategory.name}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.SIZE} -
										</Text>
										<Text fontSize={13} flexShrink="1">
											{product.size} ml
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.COUNTRY} -
										</Text>
										<Text
											textTransform="capitalize"
											fontSize={13}
											flexShrink="1">
											{product.country}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.REGION} -
										</Text>
										<Text
											textTransform="capitalize"
											fontSize={13}
											flexShrink="1">
											{product.region}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.ABV} -
										</Text>
										<Text fontSize={13} flexShrink="1">
											{product.abv ? `${product.abv}%` : 'N/A'}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'flex-start' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.TASTE} -
										</Text>
										<Text
											fontSize={13}
											_android={{ lineHeight: '16' }}
											flexShrink="1">
											{product.taste}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.UPC} -
										</Text>
										<Text fontSize={13} flexShrink="1">
											{product.upc}
										</Text>
									</HStack>

									<HStack space={1} _android={{ alignItems: 'center' }}>
										<Text fontSize={13}>
											<Icon as={MaterialCommunityIcons} name="star-outline" />{' '}
											{constants.LABEL.PRODUCER} -
										</Text>
										<Text fontSize={13} flexShrink="1">
											{product.producer}
										</Text>
									</HStack>
								</VStack>
							) : (
								<Text fontSize={13}>
									{product.description && product.description.slice(0, 150)}
								</Text>
							)}
							<Link
								alignSelf="flex-end"
								onPress={() => setExpanded(!expanded)}
								_text={{ color: 'custom.orange', fontSize: 12 }}>
								{expanded ? constants.LINK.READ_LESS : constants.LINK.READ_MORE}
							</Link>
						</Box>
					</Skeleton>
				</VStack>
			</Box>
		</Center>
	);
};

export default DealDetailCard;

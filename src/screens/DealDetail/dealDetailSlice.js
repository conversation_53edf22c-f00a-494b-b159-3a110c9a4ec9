import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getDealDetail = createAsyncThunk(
	'dealDetail/getDealDetail',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_DEAL + `/${data}`,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const getDealRetailers = createAsyncThunk(
	'dealDetail/getDealRetailers',
	async (data, thunkAPI) => {
		const {userData, businessData} = thunkAPI.getState().userSignIn;
		const {coordinate} = thunkAPI.getState().addAddress;

		const {ROLE} = constants;
		let longitude, latitude;
		if (userData.role === ROLE.RETAILER) {
			[longitude, latitude] = businessData.location.coordinates;
		} else {
			({longitude, latitude} = coordinate);
		}

		const url =
			constants.API_ROUTES.GET_DEAL_RELATED_STORES +
			`/${data.productId}/stores?businessId=${data.businessId}&lat=${latitude}&log=${longitude}`;

		return await apiCall({url, method: 'GET'}, false)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const dealDetailSlice = createSlice({
	name: 'dealDetail',
	initialState: {},
});

export default dealDetailSlice.reducer;

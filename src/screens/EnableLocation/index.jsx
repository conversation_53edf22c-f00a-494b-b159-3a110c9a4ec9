import React, {useEffect, useRef} from 'react';
import {Dimensions, Linking} from 'react-native';
import {
	Box,
	Button,
	Center,
	Heading,
	Icon,
	Image,
	ScrollView,
	Text,
	VStack,
} from 'native-base';

import constants from '../../utils/constants';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import GooglePlacesSearch from './GooglePlacesSearch';
import {requestLocationPermission} from '../../services/RNPermission';

const {height: viewportHeight} = Dimensions.get('window');

const EnableLocation = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const placeSearchRef = useRef(null);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		(async () => {
			await requestLocationPermission();
		})();
	}, []);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const openDeviceLocationSetting = () => {
		Linking.openSettings();
	};

	const openEnterLocationScreen = () => {
		navigation.navigate(constants.ROUTE.SELECT_LOCATION);
	};

	const openActionsheet = () => {
		placeSearchRef.current.openActionsheet();
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<ScrollView>
				<Center
					w="100%"
					alignItems="center"
					flexDirection="row"
					h={viewportHeight}>
					<Box w="90%">
						<VStack>
							<Center mb={3}>
								<Image
									source={require('../../assets/images/location.png')}
									alt="image"
									resizeMode="contain"
									size="xl"
								/>
							</Center>

							<Heading fontSize={20} textAlign="center" my={3}>
								{constants.TEXT.WE_DONT_HAVE_LOCATION}
							</Heading>

							<Text textAlign="center">
								{constants.TEXT.SET_LOCATION_TO_EXPLORE_DEALS}
							</Text>

							<Button
								mt={10}
								onPress={openDeviceLocationSetting}
								leftIcon={
									<Icon
										as={MaterialCommunityIcons}
										name="crosshairs-gps"
										size="md"
									/>
								}>
								{constants.BUTTON.ENABLE_DEVICE_LOCATION}
							</Button>
							<Button variant="outline" mt={4} onPress={openActionsheet}>
								{constants.BUTTON.ENTER_LOCATION_MANUALLY}
							</Button>
						</VStack>
					</Box>
				</Center>
			</ScrollView>
			<GooglePlacesSearch ref={placeSearchRef} navigation={navigation} />
		</>
	);
};

export default EnableLocation;

import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {
	Box,
	VStack,
	Center,
	useDisclose,
	Actionsheet,
	Input,
	Icon,
	Text,
} from 'native-base';
import {Dimensions, FlatList, TouchableOpacity} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import DeviceInfo from 'react-native-device-info';

import constants from '../../utils/constants';
import {GooglePlaceAutoComplete} from '../../services/RNGoogleMaps';

const {height: viewportHeight} = Dimensions.get('window');

const GooglePlacesSearch = forwardRef((props, ref) => {
	/**
	 * Start Initials
	 */
	const {navigation} = props;

	const [places, setPlaces] = useState([]);
	const [search, setSearch] = useState('');

	const {isOpen, onOpen, onClose} = useDisclose();

	const insets = useSafeAreaInsets();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const onChangeSearch = async value => {
		setSearch(value);
		const data = await GooglePlaceAutoComplete(value);
		if (data && data != {}) setPlaces(data);
	};

	useImperativeHandle(ref, () => ({
		openActionsheet() {
			onOpen();
		},
	}));

	const onSelectPlace = item => {
		onClose();
		navigation.navigate(constants.ROUTE.CONFIRM_LOCATION, {
			placeId: item.place_id,
		});
	};

	const ItemView = ({item}) => {
		return (
			<TouchableOpacity onPress={() => onSelectPlace(item)}>
				<Box mx={1} my={1} pb={3} borderColor="gray.300" borderBottomWidth={1}>
					<Text>{item.description}</Text>
				</Box>
			</TouchableOpacity>
		);
	};

	return (
		<Box ref={ref}>
			<Actionsheet isOpen={isOpen} onClose={onClose} size="full">
				<Actionsheet.Content
					h={viewportHeight - (insets.top + (DeviceInfo.hasNotch() ? 0 : 133))}>
					<Box w="99%">
						<VStack space={2}>
							<Input
								size="xl"
								placeholder="Search"
								py={3}
								fontSize={14}
								value={search}
								onChangeText={onChangeSearch}
								returnKeyType="search"
								returnKeyLabel="search"
								InputLeftElement={
									<Icon
										as={MaterialCommunityIcons}
										name="magnify"
										size={6}
										ml={2}
										mr={2}
										color="custom.orange"
									/>
								}
							/>

							<FlatList
								data={places}
								keyExtractor={(item, index) => index.toString()}
								enableEmptySections={true}
								renderItem={ItemView}
								style={{
									height:
										viewportHeight -
										(insets.top + (DeviceInfo.hasNotch() ? 100 : 266)),
								}}
							/>
						</VStack>
					</Box>
				</Actionsheet.Content>
			</Actionsheet>
		</Box>
	);
});

export default GooglePlacesSearch;

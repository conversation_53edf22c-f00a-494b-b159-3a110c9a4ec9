import React, {useCallback, useState} from 'react';
import {Box, Button, Center, Text} from 'native-base';
import {useDispatch} from 'react-redux';
import {FlatList, ActivityIndicator, Dimensions} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';

import DealCard from '../../components/DealCard';
import {fetchSearchDeals} from './searchDealsSlice';
import constants from '../../utils/constants';

const {width} = Dimensions.get('window');
const DEFAULT_PAGE = 1,
	DEFAULT_LIMIT = 10;

const SearchDeal = ({navigation, route}) => {
	/**
	 * Start Initials
	 */
	const [products, setProducts] = useState([]);
	const [initialLoading, setInitialLoading] = useState(false);
	const [totalPages, setTotalPages] = useState(1);
	const [loading, setLoading] = useState(false);
	const [hasData, setHasData] = useState(true);
	const [page, setPage] = useState(DEFAULT_PAGE);
	const [dealMsg, setDealMsg] = useState('');

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			searchDeals();
		}, []),
	);
	/**
	 * Start Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const searchDeals = () => {
		setInitialLoading(true);
		setPage(1);
		setHasData(true);

		dispatch(
			fetchSearchDeals({
				page: page,
				limit: DEFAULT_LIMIT,
				search: route.params.search,
			}),
		)
			.unwrap()
			.then(res => {
				setProducts(res.data);
				setTotalPages(res.totalPages);
				setInitialLoading(false);

				if (totalPages == 0 || totalPages == 1) {
					setHasData(false);
					setDealMsg(
						totalPages == 0
							? constants.LABEL.NO_DEALS_FOUND
							: constants.LABEL.NO_MORE_DEALS,
					);
				}
			})
			.catch(() => setInitialLoading(false));
	};

	const retrieveMore = () => {
		if (hasData) {
			setPage(page + 1);
			setLoading(true);
			dispatch(
				fetchSearchDeals({
					page: page,
					limit: DEFAULT_LIMIT,
					search: route.params.search,
				}),
			)
				.unwrap()
				.then(res => {
					setProducts(prevProducts => [...prevProducts, ...res.data]);
					setLoading(false);

					if (page == totalPages) {
						setHasData(false);
						setDealMsg(constants.LABEL.NO_MORE_DEALS);
					}
				})
				.catch(() => setLoading(false));
		}
	};

	const ItemView = ({item}) => {
		return (
			<Center w={width / 2.1} my={2}>
				<DealCard
					navigation={navigation}
					productID={item.product?._id}
					productName={item.product?.name}
					productPhoto={item.product?.photo}
					productSize={item.product?.size}
					productPrice={item.productPrice}
					discountPrice={item.discountPrice}
					discountType={item.discountType}
					discountValue={item.discountValue}
					dealId={item._id}
					lowStock={item.lowStock}
					quantity={item.quantity}
				/>
			</Center>
		);
	};

	const renderFooter = () => {
		return (
			<Box w="100%" justifyContent="center" alignItems="center" py={2}>
				{hasData ? (
					<Button
						variant="ghost"
						isLoading={loading}
						isLoadingText="Loading"
						onPress={retrieveMore}>
						{constants.BUTTON.LOAD_MORE}
					</Button>
				) : (
					<Text>{dealMsg}</Text>
				)}
			</Box>
		);
	};
	/**
	 * Start Methods
	 */

	if (initialLoading) {
		return <ActivityIndicator style={{paddingTop: 15}} />;
	}

	return (
		<Center w="100%" bg="white">
			<Box px={3} mt={3} flexDirection="row">
				<FlatList
					data={products}
					keyExtractor={(item, index) => index.toString()}
					enableEmptySections={true}
					renderItem={ItemView}
					numColumns={2}
					ListFooterComponent={renderFooter}
					onEndReached={retrieveMore}
					onRefresh={searchDeals}
					refreshing={loading}
				/>
			</Box>
		</Center>
	);
};

export default SearchDeal;

import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const fetchSearchDeals = createAsyncThunk(
	'categoryDeal/fetchSearchDeals',
	async (data, thunkAPI) => {
		const {userData, businessData} = thunkAPI.getState().userSignIn;
		const {coordinate} = thunkAPI.getState().addAddress;

		const {ROLE} = constants;
		let longitude, latitude;
		if (userData.role === ROLE.RETAILER) {
			[longitude, latitude] = businessData.location.coordinates;
		} else {
			({longitude, latitude} = coordinate);
		}

		const query = `?page=${data.page}&limit=${data.limit}&search=${data.search}&lat=${latitude}&log=${longitude}`;

		return await apiCall(
			{
				url: constants.API_ROUTES.SEARCH_DEALS + query,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

const searchDealsSlice = createSlice({
	name: 'searchDeals',
	initialState: {},
});

export default searchDealsSlice.reducer;

import React, {useEffect, useState} from 'react';
import {Dimensions, Platform, StyleSheet} from 'react-native';

import {
	Box,
	Text,
	Heading,
	VStack,
	FormControl,
	Button,
	Center,
	HStack,
	Link,
	Image,
} from 'native-base';
import {
	<PERSON><PERSON><PERSON>,
	Cursor,
	useBlurOnFulfill,
	useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DeviceInfo from 'react-native-device-info';

import constants from '../../utils/constants';
import SpinnerOverlay from '../../components/SpinnerOverlay';

const {height: viewportHeight} = Dimensions.get('window');

const CELL_COUNT = 4;
const RESEND_OTP_TIME_LIMIT = 30;

const Verification = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const [loading, setLoading] = useState(false);

	let resendOtpTimerInterval;

	const [value, setValue] = useState('');
	const [resendButtonDisabledTime, setResendButtonDisabledTime] = useState(
		RESEND_OTP_TIME_LIMIT,
	);

	const [props, getCellOnLayoutHandler] = useClearByFocusCell({
		value,
		setValue,
	});

	const ref = useBlurOnFulfill({value, cellCount: CELL_COUNT});
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		startResendOtpTimer();
		return () => {
			if (resendOtpTimerInterval) {
				clearInterval(resendOtpTimerInterval);
			}
		};
	}, [resendButtonDisabledTime]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const startResendOtpTimer = () => {
		if (resendOtpTimerInterval) {
			clearInterval(resendOtpTimerInterval);
		}
		resendOtpTimerInterval = setInterval(() => {
			if (resendButtonDisabledTime <= 0) {
				clearInterval(resendOtpTimerInterval);
			} else {
				setResendButtonDisabledTime(resendButtonDisabledTime - 1);
			}
		}, 1000);
	};

	const onResendOtpButtonPress = () => {
		setValue('');
		setResendButtonDisabledTime(RESEND_OTP_TIME_LIMIT);
		startResendOtpTimer();
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Box bg="custom.orange" style={styles.mainBox}>
					<Box style={styles.logoContainer}>
						<Image
							source={require('../../assets/images/logo.png')}
							style={{width: 100, height: 100}}
						/>
					</Box>
					<Box style={styles.signInContainer}>
						<Center
							w="100%"
							backgroundColor="white"
							borderTopLeftRadius={'2xl'}
							borderTopRightRadius={'2xl'}>
							<Box safeArea p="2" py="8" w="90%" h={viewportHeight}>
								<VStack space={1} alignItems="center">
									<Heading
										fontWeight={Platform.select({ios: '600', android: 600})}
										fontSize={32}
										color="muted.900"
										lineHeight={40}>
										{constants.TITLE.VERIFICATION}
									</Heading>
									<Text
										marginTop="30px"
										color="muted.900"
										textAlign="center"
										fontSize="16px"
										lineHeight={20}>
										{constants.SUBTITLE.VERIFICATION_CODE_SENT}
									</Text>
								</VStack>

								<VStack space={3} mt="60">
									<FormControl alignItems="center">
										<CodeField
											ref={ref}
											{...props}
											value={value}
											onChangeText={setValue}
											cellCount={CELL_COUNT}
											rootStyle={styles.codeFieldRoot}
											keyboardType="number-pad"
											textContentType="oneTimeCode"
											renderCell={({index, symbol, isFocused}) => (
												<Box
													// Make sure that you pass onLayout={getCellOnLayoutHandler(index)} prop to root component of "Cell"
													onLayout={getCellOnLayoutHandler(index)}
													key={index}
													style={[
														styles.cellRoot,
														isFocused && styles.focusCell,
													]}>
													<Text style={styles.cellText}>
														{symbol || (isFocused ? <Cursor /> : null)}
													</Text>
												</Box>
											)}
										/>
									</FormControl>

									<HStack mt={4} justifyContent="center">
										{resendButtonDisabledTime > 0 ? (
											<Text
												fontSize={15}
												fontWeight={Platform.select({
													ios: '500',
													android: 500,
												})}>
												{constants.TEXT.RESENT_CODE_IN}{' '}
												{resendButtonDisabledTime} sec
											</Text>
										) : (
											<>
												<Text
													fontSize={15}
													fontWeight={Platform.select({
														ios: '500',
														android: 500,
													})}>
													{constants.TEXT.DID_NOT_RECEIVE_CODE}
												</Text>
												<Link
													_text={{color: 'custom.red'}}
													fontSize={15}
													fontWeight={Platform.select({
														ios: '500',
														android: 500,
													})}>
													{constants.LINK.RESEND}
												</Link>
											</>
										)}
									</HStack>

									<Button
										mt={4}
										_text={{
											fontSize: 15,
											fontWeight: Platform.select({ios: '500', android: 500}),
										}}
										onPress={() =>
											navigation.navigate(constants.ROUTE.BOTTOM_NAVIGATION)
										}>
										{constants.BUTTON.VERIFY}
									</Button>
								</VStack>
							</Box>
						</Center>
					</Box>
				</Box>
			</KeyboardAwareScrollView>
			<SpinnerOverlay loading={loading} />
		</>
	);
};

const styles = StyleSheet.create({
	codeFieldRoot: {
		marginTop: 20,
		width: '90%',
	},
	cellRoot: {
		width: 60,
		height: 60,
		justifyContent: 'center',
		alignItems: 'center',
		borderBottomColor: constants.COLORS.GREY2,
		borderBottomWidth: 1,
	},
	cellText: {
		color: constants.COLORS.BLACK,
		fontSize: 24,
		textAlign: 'center',
	},
	focusCell: {
		borderBottomColor: constants.COLORS.ORANGE,
		borderBottomWidth: 2,
	},
	mainBox: {
		flex: 1,
	},
	logoContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		marginTop: DeviceInfo.hasNotch() ? '15%' : '10%',
	},
	signInContainer: {
		flex: 2,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		backgroundColor: 'white',
		marginTop: '5%',
	},
});

export default Verification;

import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import constants from '../../utils/constants';
import apiCall from '../../services/apiClient';

export const createRequestAddProduct = createAsyncThunk(
	'requestAddProduct/createRequestAddProduct',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.REQUEST_ADD_PRODUCT,
				method: 'POST',
				data: data,
			},
			true,
			true,
		)
			.then(r => r.data)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const requestAddProductSlice = createSlice({
	name: 'requestAddProduct',
	initialState: {},
});

export default requestAddProductSlice.reducer;

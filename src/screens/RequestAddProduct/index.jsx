import React, {useState} from 'react';
import {
	Actionsheet,
	AspectRatio,
	Box,
	Button,
	Center,
	FormControl,
	Icon,
	Image,
	Input,
	Pressable,
	Text,
	useDisclose,
	useToast,
	VStack,
} from 'native-base';
import {useForm, Controller} from 'react-hook-form';
import {useDispatch, useSelector} from 'react-redux';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import _ from 'lodash';

import constants from '../../utils/constants';
import {createRequestAddProduct} from './requestAddProductSlice';
import ToastAlert from '../../components/ToastAlert';
import SpinnerOverlay from '../../components/SpinnerOverlay';
import {PermissionsAndroid} from 'react-native';

const RequestProduct = () => {
	/**
	 * Start Initials
	 */
	const {
		control,
		handleSubmit,
		reset,
		formState: {errors},
	} = useForm();

	const businessData = useSelector(state => state.userSignIn.businessData);

	const [photo, setPhoto] = useState(null);
	const [loading, setLoading] = useState(false);
	const [photoFile, setPhotoFile] = useState({});

	const dispatch = useDispatch();

	const toast = useToast();

	const {isOpen, onOpen, onClose} = useDisclose();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */

	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const pickPhoto = () => {
		onClose();
		setLoading(true);
		launchImageLibrary({noData: true, quality: 0.5}).then(r => {
			if (r.assets?.length > 0) {
				setPhoto(r.assets[0]?.uri);
				setPhotoFile({
					uri: r.assets[0]?.uri,
					type: r.assets[0]?.type,
					size: r.assets[0]?.fileSize,
					name: r.assets[0]?.fileName,
				});
			} else {
				setLoading(false);
				return;
			}
			setLoading(false);
		});
	};

	const takePhoto = async () => {
		if (Platform.OS === 'android') {
			try {
				const granted = await PermissionsAndroid.request(
					PermissionsAndroid.PERMISSIONS.CAMERA,
					{
						title: constants.VALIDATION_MESSAGES.CAMERA_PERMISSION,
						message: constants.VALIDATION_MESSAGES.CAMERA_PERMISSION_MESSAGE,
						buttonNeutral: constants.VALIDATION_MESSAGES.ASK_ME_LATER,
						buttonNegative: constants.VALIDATION_MESSAGES.CANCEL,
						buttonPositive: constants.VALIDATION_MESSAGES.OK,
					},
				);
				if (granted === PermissionsAndroid.RESULTS.GRANTED) {
					onClose();
					setLoading(true);
					launchCamera({noData: true, quality: 0.5}).then(r => {
						if (!r?.errorCode) {
							if (r?.didCancel) {
								setLoading(false);
								return;
							}
							setPhoto(r?.assets[0]?.uri);
							setPhotoFile({
								uri: r.assets[0].uri,
								type: r.assets[0].type,
								size: r.assets[0].fileSize,
								name: r.assets[0].fileName,
							});
						} else {
							if (r.errorCode === 'camera_unavailable') {
								toast.show({
									avoidKeyboard: true,
									render: ({id}) => {
										return (
											<ToastAlert
												id={id}
												toast={toast}
												type="error"
												message={constants.ERROR_MESSAGE.CAMERA_NOT_AVAILABLE}
											/>
										);
									},
								});
							} else {
								toast.show({
									avoidKeyboard: true,
									render: ({id}) => {
										return (
											<ToastAlert
												id={id}
												toast={toast}
												type="error"
												message={
													r?.errorMessage
														? r.errorMessage
														: constants.ERROR_MESSAGE.SOMETHING_WRONG
												}
											/>
										);
									},
								});
							}
						}
						setLoading(false);
					});
				} else {
					alert('Camera permission denied');
				}
			} catch (err) {
				return;
			}
		} else {
			onClose();
			setLoading(true);
			launchCamera({noData: true, quality: 0.5}).then(r => {
				if (!r?.errorCode) {
					setPhoto(r.assets[0].uri);
					setPhotoFile({
						uri: r.assets[0].uri,
						type: r.assets[0].type,
						size: r.assets[0].fileSize,
						name: r.assets[0].fileName,
					});
				} else {
					if (r.errorCode === 'camera_unavailable') {
						toast.show({
							avoidKeyboard: true,
							render: ({id}) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={constants.ERROR_MESSAGE.CAMERA_NOT_AVAILABLE}
									/>
								);
							},
						});
					} else {
						toast.show({
							avoidKeyboard: true,
							render: ({id}) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={
											r?.errorMessage
												? r.errorMessage
												: constants.ERROR_MESSAGE.SOMETHING_WRONG
										}
									/>
								);
							},
						});
					}
				}
				setLoading(false);
			});
		}
	};

	const onSubmit = data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}
		setLoading(true);

		const formData = new FormData();
		formData.append('photo', photoFile);
		formData.append('business', businessData._id);

		for (const key in data) {
			formData.append(key, data[key]);
		}

		dispatch(createRequestAddProduct(formData))
			.unwrap()
			.then(() => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.REQUEST_ADD_PRODUCT}
							/>
						);
					},
				});
				setLoading(false);
				setPhoto(null);
				setPhotoFile({});
				reset();
			})
			.catch(err => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={err.message}
							/>
						);
					},
				});
				setLoading(false);
			});
	};

	const debouncedSubmit = _.debounce(onSubmit, 1000, {
		leading: true,
		trailing: false,
	});
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Center w="100%">
					<Box w="90%" px={1} mb={6}>
						<VStack space={3}>
							<VStack space={3} mt="5">
								<FormControl>
									<FormControl.Label>
										{constants.LABEL.PRODUCT_NAME}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Controller
										control={control}
										name="name"
										rules={{
											required: {
												value: true,
												message:
													constants.VALIDATION_MESSAGES.PRODUCT_NAME_REQUIRED,
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												autoCapitalize="none"
												autoCorrect={false}
												onBlur={onBlur}
												onChangeText={onChange}
												value={value}
											/>
										)}
									/>
									{errors.name && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.name.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.UPC}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Controller
										control={control}
										name="upc"
										rules={{
											required: {
												value: true,
												message: constants.VALIDATION_MESSAGES.UPC_REQUIRED,
											},
											pattern: {
												value: constants.REGEX.UPC,
												message: constants.VALIDATION_MESSAGES.VALID_UPC,
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												autoCapitalize="none"
												autoCorrect={false}
												keyboardType="number-pad"
												onBlur={onBlur}
												onChangeText={onChange}
												value={value}
												returnKeyType="done"
												returnKeyLabel="done"
											/>
										)}
									/>
									{errors.upc && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.upc.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.PRODUCT_PHOTO}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Pressable onPress={onOpen}>
										<Box borderColor="custom.grey2" borderWidth="1">
											<AspectRatio w="100%" ratio={16 / 9}>
												{photo && (
													<Image
														source={{
															uri: photo,
														}}
														alt="image"
													/>
												)}
											</AspectRatio>

											{photo ? (
												<Center
													bg="custom.orange"
													position="absolute"
													top="0"
													right="0"
													px="3"
													py="1.5">
													<Icon
														as={MaterialCommunityIcons}
														name="lead-pencil"
														size={4}
														color="white"
													/>
												</Center>
											) : (
												<Center position="absolute" top="35%" left="42%">
													<Icon
														as={MaterialCommunityIcons}
														name="plus"
														size={12}
														color="custom.grey1"
													/>
												</Center>
											)}
										</Box>
									</Pressable>
								</FormControl>

								<Button
									mt="3"
									onPress={handleSubmit(debouncedSubmit)}
									isDisabled={loading}>
									{constants.BUTTON.SAVE}
								</Button>
							</VStack>
						</VStack>
					</Box>
				</Center>
			</KeyboardAwareScrollView>
			<Actionsheet isOpen={isOpen} onClose={onClose} size="full">
				<Actionsheet.Content>
					<Actionsheet.Item
						startIcon={
							<Icon as={MaterialCommunityIcons} name="image" size="6" />
						}
						onPress={pickPhoto}>
						{constants.LABEL.SELECT_PHOTO}
					</Actionsheet.Item>
					<Actionsheet.Item
						startIcon={
							<Icon as={MaterialCommunityIcons} size="6" name="camera" />
						}
						onPress={takePhoto}>
						{constants.LABEL.TAKE_PHOTO}
					</Actionsheet.Item>
				</Actionsheet.Content>
			</Actionsheet>
			<SpinnerOverlay loading={loading} />
		</>
	);
};

export default RequestProduct;

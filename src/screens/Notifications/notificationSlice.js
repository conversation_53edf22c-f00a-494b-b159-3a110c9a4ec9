import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getNotifications = createAsyncThunk(
	'notification/getNotifications',
	async (data, thunkAPI) => {
		const query = `page=${data.page}&limit=${data.limit}`;

		return await apiCall(
			{
				url: constants.API_ROUTES.GET_NOTIFICATIONS + `?${query}`,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

const notificationSlice = createSlice({
	name: 'notification',
	initialState: {},
});

export default notificationSlice.reducer;

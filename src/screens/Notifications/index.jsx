import React, {useCallback, useState} from 'react';
import {Dimensions, Platform, StyleSheet} from 'react-native';
import {
	Box,
	VStack,
	Center,
	Heading,
	HStack,
	Text,
	Button,
	FlatList,
} from 'native-base';
import {useFocusEffect} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import dayjs from 'dayjs';

import constants from '../../utils/constants';
import {getNotifications} from './notificationSlice';

const {width: viewportWidth} = Dimensions.get('window');
const DEFAULT_PAGE = 1,
	DEFAULT_LIMIT = 10;

const Notifications = () => {
	/**
	 * Start Initials
	 */
	const [notifications, setNotifications] = useState([]);
	const [loading, setLoading] = useState(false);
	const [hasDataLoading, setHasDataLoading] = useState(false);
	const [hasData, setHasData] = useState(true);
	const [page, setPage] = useState(DEFAULT_PAGE);
	const [totalPages, setTotalPages] = useState(1);
	const [notificationMsg, setNotificationMsg] = useState('');

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchNotifications();
		}, []),
	);
	/**
	 * Start Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchNotifications = () => {
		setHasData(true);
		setLoading(true);
		setPage(1);
		dispatch(getNotifications({page: page, limit: DEFAULT_LIMIT}))
			.unwrap()
			.then(res => {
				setNotifications(res.data);
				setTotalPages(res.totalPages);
				setLoading(false);

				if (res.totalPages == 0 || res.totalPages == 1) {
					setHasData(false);
					setNotificationMsg(
						totalPages == 0
							? constants.LABEL.NO_NOTIFICATIONS_FOUND
							: constants.LABEL.NO_MORE_NOTIFICATIONS,
					);
				}
			})
			.catch(() => setLoading(false));
	};

	const retrieveMore = () => {
		if (hasData) {
			setHasDataLoading(true);
			setPage(page + 1);
			dispatch(getNotifications({page: page, limit: DEFAULT_LIMIT}))
				.unwrap()
				.then(res => {
					setNotifications(prevNotifications => [
						...prevNotifications,
						...res.data,
					]);
					setHasDataLoading(true);

					if (res.totalPages == totalPages) {
						setHasData(false);
						setNotificationMsg(constants.LABEL.NO_MORE_NOTIFICATIONS);
					}
				})
				.catch(() => setHasDataLoading(true));
		}
	};

	const ItemView = ({item}) => {
		return (
			<Center mt={2} mb={2}>
				<Box style={styles.cardStyle} w={viewportWidth - 30}>
					<VStack space={1}>
						<HStack justifyContent="space-between">
							<Heading
								fontSize={16}
								fontWeight={Platform.select({ios: '700', android: 700})}>
								{item.title}
							</Heading>
							<Text color="custom.grey4" fontSize={10}>
								{dayjs(item?.createdAt).isValid() &&
									dayjs(item.createdAt).fromNow()}
							</Text>
						</HStack>
						<Text
							fontSize={10}
							fontWeight={Platform.select({ios: '400', android: 400})}>
							{item.description}
						</Text>
					</VStack>
				</Box>
			</Center>
		);
	};

	const renderFooter = () => {
		return (
			<Box w="100%" justifyContent="center" alignItems="center" py={2}>
				{hasData ? (
					<Button
						variant="ghost"
						isLoading={hasDataLoading}
						isLoadingText="Loading"
						onPress={retrieveMore}>
						{loading ? '' : constants.BUTTON.LOAD_MORE}
					</Button>
				) : (
					<Text>{notificationMsg}</Text>
				)}
			</Box>
		);
	};
	/**
	 * Start Methods
	 */

	return (
		<Center w="100%">
			<Box h="100%" flexDirection="row">
				<FlatList
					data={notifications}
					keyExtractor={(item, index) => index.toString()}
					enableEmptySections={true}
					renderItem={ItemView}
					ListFooterComponent={renderFooter}
					onEndReached={retrieveMore}
					onEndReachedThreshold={0.1}
					onRefresh={fetchNotifications}
					refreshing={loading}
				/>
			</Box>
		</Center>
	);
};

const styles = StyleSheet.create({
	cardStyle: {
		shadowColor: constants.COLORS.GREY1,
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.3,
		shadowRadius: 2.84,
		elevation: 3,
		borderRadius: 5,
		padding: 7,
		backgroundColor: constants.COLORS.WHITE,
	},
});

export default Notifications;

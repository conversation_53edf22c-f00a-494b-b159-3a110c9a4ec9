import React, {useState} from 'react';
import {
	Box,
	Text,
	Heading,
	VStack,
	FormControl,
	Input,
	Button,
	Center,
	useToast,
	Link,
	HStack,
	Image,
} from 'native-base';
import {StyleSheet} from 'react-native';
import {useForm, Controller} from 'react-hook-form';
import {useDispatch} from 'react-redux';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DeviceInfo from 'react-native-device-info';

import constants, {APP_NAME} from '../../utils/constants';
import ToastAlert from '../../components/ToastAlert';
import {sendResetPasswordLink} from './forgotPasswordSlice';
import SpinnerOverlay from '../../components/SpinnerOverlay';

function ForgotPassword({navigation}) {
	/**
	 * Start Initials
	 */
	const {
		control,
		handleSubmit,
		reset,
		formState: {errors},
	} = useForm({
		defaultValues: {
			email: '',
		},
	});
	const [loading, setLoading] = useState(false);

	const dispatch = useDispatch();

	const toast = useToast();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const onSubmit = data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}
		setLoading(true);

		dispatch(sendResetPasswordLink(data.email))
			.unwrap()
			.then(() => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.RESET_PASSWORD_LINK_SENT}
							/>
						);
					},
				});
				reset();
			})
			.catch(err =>
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert id={id} toast={toast} type="error" message={err} />
						);
					},
				}),
			)
			.finally(() => setLoading(false));
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Box bg="custom.orange" style={styles.mainBox}>
					<Box style={styles.logoContainer}>
						<Image
							source={require('../../assets/images/logo.png')}
							style={{width: 100, height: 100}}
							alt={APP_NAME}
						/>
					</Box>
					<Box style={styles.signInContainer}>
						<Center
							safeArea
							w="100%"
							bg="white"
							style={{borderTopLeftRadius: 20, borderTopRightRadius: 20}}>
							<Box w="90%">
								<VStack p={2} justifyContent="space-between">
									<VStack>
										<VStack space={1} alignItems="center">
											<Heading>{constants.TITLE.FORGOT_PASSWORD}</Heading>

											<Text
												mt={5}
												mb={3}
												fontSize={16}
												mx="auto"
												textAlign="center">
												{constants.TEXT.EMAIL_YOU_LINK_TO_RESET_PASSWORD}
											</Text>
										</VStack>

										<VStack space={3} mt="5">
											<FormControl>
												<FormControl.Label>
													{constants.LABEL.YOUR_EMAIL_ID}
												</FormControl.Label>
												<Controller
													control={control}
													name="email"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES.EMAIL_REQUIRED,
														},
														pattern: {
															value: constants.REGEX.EMAIL,
															message:
																constants.VALIDATION_MESSAGES.VALID_EMAIL,
														},
													}}
													render={({field: {onChange, onBlur, value}}) => (
														<Input
															size="xl"
															autoCapitalize="none"
															keyboardType="email-address"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
														/>
													)}
												/>
												{errors.email && (
													<FormControl.HelperText _text={{color: 'custom.red'}}>
														{errors.email.message}
													</FormControl.HelperText>
												)}
											</FormControl>
											<Button
												mt="3"
												onPress={handleSubmit(onSubmit)}
												isDisabled={loading}>
												{constants.BUTTON.SEND}
											</Button>
										</VStack>
									</VStack>

									<HStack justifyContent="center" alignItems="flex-end" my={6}>
										<Text>{constants.TEXT.BACK_TO}</Text>
										<Link
											onPress={() =>
												navigation.navigate(constants.ROUTE.SIGN_IN)
											}>
											{constants.LINK.SIGN_IN}
										</Link>
									</HStack>
								</VStack>
							</Box>
						</Center>
					</Box>
				</Box>
			</KeyboardAwareScrollView>
			<SpinnerOverlay loading={loading} />
		</>
	);
}

const styles = StyleSheet.create({
	mainBox: {
		flex: 1,
	},
	logoContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		marginTop: DeviceInfo.hasNotch() ? '15%' : '10%',
	},
	signInContainer: {
		flex: 2,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		backgroundColor: 'white',
		marginTop: '5%',
	},
});
export default ForgotPassword;

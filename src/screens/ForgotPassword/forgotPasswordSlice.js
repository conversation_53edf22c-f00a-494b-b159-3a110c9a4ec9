import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import {firebaseResetPasswordLink} from '../../services/firebaseAuth';

export const sendResetPasswordLink = createAsyncThunk(
	'forgotPassword/sendResetPasswordLink',
	async (data, thunkAPI) => {
		return await firebaseResetPasswordLink(data)
			.then(res => res)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

const forgotPasswordSlice = createSlice({
	name: 'forgotPassword',
	initialState: {},
});

export default forgotPasswordSlice.reducer;

import React from 'react';
import {Box, VStack, HStack, Image, Stack, Text} from 'native-base';
import {StyleSheet, TouchableOpacity} from 'react-native';
import dayjs from 'dayjs';

import constants from '../../utils/constants';
import {toUri} from '../../utils/helpers';

const DealCard = props => {
	/**
	 * Start Initials
	 */
	const {deal, navigation} = props;
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const viewDealDetail = () => {
		navigation.navigate(constants.ROUTE.MY_DEAL_DETAIL, {
			dealId: deal._id,
		});
	};
	/**
	 * End Methods
	 */

	return (
		<Box w="100%" style={styles.cardStyle} p={2}>
			<VStack>
				<HStack space={1}>
					<Box w="25%">
						{deal.quantity > 1 ? (
							<Text
								bg={'#F5F5F5'}
								position="absolute"
								zIndex={1}
								top={-8}
								left={-5}
								borderRadius={8}
								px={1}
								fontSize={12}>
								Pack of {deal.quantity}
							</Text>
						) : null}
						<Image
							source={{uri: toUri(deal.product.photo)}}
							alt="image"
							resizeMode="contain"
							size="md"
						/>
						<Text
							color="custom.red"
							fontSize={10}
							bold
							textAlign="center"
							bg="white"
							w="100%"
							p={0.3}>
							{deal.lowStock && 'Low Stock'}
						</Text>
					</Box>

					<TouchableOpacity onPress={viewDealDetail}>
						<Stack space={2} mr={4}>
							<Text fontSize={16} isTruncated w="200px">
								{deal.product.name}
							</Text>
							<Text fontSize={12}>{deal.product.size} ml</Text>
							<HStack space={2}>
								{[
									constants.DEAL_TYPE.FIX,
									constants.DEAL_TYPE.PERCENT,
									constants.DEAL_TYPE.QUANTITY,
								].includes(deal.discountType) && (
									<Text fontSize={14} strikeThrough color="custom.red">
										${deal.productPrice}
									</Text>
								)}
								<Text fontSize={12} bold color="custom.green">
									${deal.discountPrice}
								</Text>
							</HStack>
							<Box
								bg={
									dayjs(deal.expiryDateTime) > dayjs()
										? 'custom.green'
										: 'custom.grey1'
								}
								px={1}
								py={0.2}
								alignSelf="flex-start"
								borderRadius={2}>
								{[
									constants.DEAL_TYPE.FIX,
									constants.DEAL_TYPE.PERCENT,
									constants.DEAL_TYPE.QUANTITY,
								].includes(deal.discountType) ? (
									<>
										<Text color="white" fontSize={10} bold>
											{deal.discountType === constants.DEAL_TYPE.PERCENT ||
											deal.discountType === constants.DEAL_TYPE.QUANTITY
												? `${deal.discountValue}%`
												: `$${deal.discountValue}`}{' '}
											OFF
										</Text>
									</>
								) : (
									<Text color="white" fontSize={10} bold>
										{deal.discountType}
									</Text>
								)}
							</Box>
						</Stack>
					</TouchableOpacity>

					<VStack space={4}>
						<Stack alignItems="flex-end">
							<Text fontSize={10}>Status</Text>
							{dayjs(deal.expiryDateTime) > dayjs() ? (
								<Text color="custom.orange" fontSize={10}>
									Active
								</Text>
							) : (
								<Text color="custom.grey" fontSize={10}>
									Closed
								</Text>
							)}
						</Stack>
					</VStack>
				</HStack>
			</VStack>
		</Box>
	);
};

const styles = StyleSheet.create({
	cardStyle: {
		shadowColor: constants.COLORS.GREY1,
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.3,
		shadowRadius: 2.84,
		elevation: 3,
		borderRadius: 5,
		padding: 2,
		marginHorizontal: 5,
		backgroundColor: constants.COLORS.WHITE,
	},
});

export default DealCard;

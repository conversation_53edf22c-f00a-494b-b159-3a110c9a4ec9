import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getDeals = createAsyncThunk(
	'myDeals/getDeals',
	async (data, thunkAPI) => {
		const businessData = thunkAPI.getState().userSignIn.businessData;
		const query = `?page=${data.page}&limit=${data.limit}&search=${data.search}`;

		return await apiCall(
			{
				url:
					constants.API_ROUTES.MY_DEALS + `/${businessData._id}/deal${query}`,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const deleteDeal = createAsyncThunk(
	'myDeals/deleteDeal',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.DELETE_DEAL + `/${data}`,
				method: 'DELETE',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const myDealsSlice = createSlice({
	name: 'myDeals',
	initialState: {},
});

export default myDealsSlice.reducer;

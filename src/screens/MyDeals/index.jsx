import React, {useCallback, useState} from 'react';
import {
	Box,
	Button,
	Center,
	Icon,
	Input,
	Stack,
	Text,
	useToast,
	View,
	VStack,
	AlertDialog,
} from 'native-base';
import {ActivityIndicator, Dimensions, Platform} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import DeviceInfo from 'react-native-device-info';
import {SwipeListView, SwipeRow} from 'react-native-swipe-list-view';

import constants from '../../utils/constants';
import DealCard from './DealCard';
import {deleteDeal, getDeals} from './myDealsSlice';
import ToastAlert from '../../components/ToastAlert';

const {height: viewportHeight} = Dimensions.get('window');
const DEFAULT_PAGE = 1,
	DEFAULT_LIMIT = 10;

const MyDeals = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const [deals, setDeals] = useState([]);
	const [totalPages, setTotalPages] = useState(1);
	const [initialLoading, setInitialLoading] = useState(false);
	const [loading, setLoading] = useState(false);
	const [hasData, setHasData] = useState(true);
	const [page, setPage] = useState(DEFAULT_PAGE);
	const [search, setSearch] = useState('');

	const [isOpen, setIsOpen] = React.useState(false);

	const onClose = () => setIsOpen(false);

	const cancelRef = React.useRef(null);

	const dispatch = useDispatch();

	const toast = useToast();

	const insets = useSafeAreaInsets();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchDeals();
		}, []),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchDeals = () => {
		setPage(1);
		setInitialLoading(true);
		setHasData(true);
		dispatch(getDeals({search: search, page: page, limit: DEFAULT_LIMIT}))
			.unwrap()
			.then(res => {
				setDeals(res.data);
				setTotalPages(res.totalPages);
				setInitialLoading(false);

				if (res.totalPages == 0 || res.totalPages == 1) {
					setHasData(false);
				}
			})
			.catch(() => setInitialLoading(false));
	};

	const retrieveMore = () => {
		if (hasData) {
			setPage(page + 1);
			setLoading(true);
			dispatch(getDeals({search: search, page: page, limit: DEFAULT_LIMIT}))
				.unwrap()
				.then(res => {
					setDeals(prevDeals => [...prevDeals, ...res.data]);
					setTotalPages(res.totalPages);
					setLoading(false);

					if (page == totalPages) {
						setHasData(false);
					}
				})
				.catch(() => setLoading(false));
		}
	};

	const handleKeyDown = () => {
		fetchDeals();
	};

	const ItemView = (rowData, rowMap) => {
		const onPressEditDeal = () => {
			navigation.navigate(constants.ROUTE.EDIT_DEAL, {
				dealId: rowData.item._id,
			});
		};

		const openDeleteAlert = () => {
			setIsOpen(!isOpen);
		};

		const onPressDeleteDeal = () => {
			dispatch(deleteDeal(rowData.item._id))
				.unwrap()
				.then(() => {
					rowMap[rowData.index].closeRow();
					setIsOpen(false);
					setDeals(deals.filter(i => i._id !== rowData.item._id));
					toast.show({
						avoidKeyboard: true,
						render: ({id}) => {
							return (
								<ToastAlert
									id={id}
									toast={toast}
									type="success"
									message={constants.SUCCESS_MESSAGE.DEAL_DELETED}
								/>
							);
						},
					});
				})
				.catch(err => {
					toast.show({
						avoidKeyboard: true,
						render: ({id}) => {
							return (
								<ToastAlert
									id={id}
									toast={toast}
									type="error"
									message={err.message}
								/>
							);
						},
					});
				});
		};
		return (
			<SwipeRow
				disableRightSwipe={parseInt(rowData.item.key) % 2 !== 0}
				disableLeftSwipe={parseInt(rowData.item.key) % 2 === 0}
				leftOpenValue={20 + parseInt(rowData.item.key) * 5}
				rightOpenValue={-90}
				leftActivationValue={200}>
				<View>
					<Box
						w="100%"
						mt={5}
						pr={8}
						alignItems="flex-end"
						justifyContent="center">
						<VStack
							space={3}
							alignItems="center"
							justifyContent="center"
							textAlign="center">
							<Button onPress={onPressEditDeal}>
								<Icon
									as={MaterialCommunityIcons}
									name="square-edit-outline"
									color="white"
								/>
							</Button>
							<Button onPress={openDeleteAlert} bg="custom.red">
								<Icon as={MaterialCommunityIcons} name="delete" color="white" />
							</Button>
						</VStack>
					</Box>
				</View>
				<View>
					<Center w="100%" mt={2} mb={2}>
						<AlertDialog
							leastDestructiveRef={cancelRef}
							isOpen={isOpen}
							onClose={onClose}>
							<AlertDialog.Content>
								<AlertDialog.CloseButton />
								<AlertDialog.Header>Delete Deal</AlertDialog.Header>
								<AlertDialog.Body>
									Are You Sure Want to Delete the Deal
								</AlertDialog.Body>
								<AlertDialog.Footer>
									<Button.Group space={2}>
										<Button
											variant="unstyled"
											colorScheme="coolGray"
											onPress={onClose}
											ref={cancelRef}>
											Cancel
										</Button>
										<Button colorScheme="danger" onPress={onPressDeleteDeal}>
											Delete
										</Button>
									</Button.Group>
								</AlertDialog.Footer>
							</AlertDialog.Content>
						</AlertDialog>
						<VStack space={3}>
							<DealCard deal={rowData.item} navigation={navigation} />
						</VStack>
					</Center>
				</View>
			</SwipeRow>
		);
	};

	const renderFooter = () => {
		return (
			<Box alignItems="center" py={2}>
				{hasData ? (
					<Button
						variant="ghost"
						isLoading={loading}
						isLoadingText="Loading"
						onPress={retrieveMore}>
						{constants.BUTTON.LOAD_MORE}
					</Button>
				) : (
					<Text>{constants.LABEL.NO_MORE_DEALS}</Text>
				)}
			</Box>
		);
	};

	const onChangeSearch = value => {
		setSearch(value);
	};
	/**
	 * End Methods
	 */

	return (
		<Center>
			<Stack
				w="100%"
				h={viewportHeight - Platform.select({ios: 55, android: 55})}>
				<Box
					px={2}
					pb={3}
					pt={insets.top + 5 - (DeviceInfo.hasNotch() ? -1 : 18)}
					alignItems="center"
					style={{backgroundColor: constants.COLORS.ORANGE}}>
					<Text
						color={constants.COLORS.WHITE}
						fontSize={18}
						mt={4}
						fontWeight={Platform.select({ios: '700', android: 500})}>
						{constants.SCREENS.MY_DEALS}
					</Text>
				</Box>

				<Box mb={1}>
					<Input
						size="xl"
						placeholder={constants.LABEL.SEARCH_FOR_DEALS}
						py={3}
						fontSize={14}
						value={search}
						onChangeText={onChangeSearch}
						onSubmitEditing={handleKeyDown}
						returnKeyType="search"
						returnKeyLabel="search"
						_focus={{borderBottomColor: 'custom.grey3'}}
						InputLeftElement={
							<Icon
								as={MaterialCommunityIcons}
								name="magnify"
								size={6}
								ml={2}
								mr={2}
							/>
						}
					/>
				</Box>

				{initialLoading ? (
					<ActivityIndicator style={{paddingTop: 15}} />
				) : (
					<SwipeListView
						data={deals}
						keyExtractor={(item, index) => index.toString()}
						enableEmptySections={true}
						renderItem={ItemView}
						ListFooterComponent={renderFooter}
						onEndReached={retrieveMore}
						onRefresh={fetchDeals}
						refreshing={loading}
					/>
				)}
			</Stack>
		</Center>
	);
};

export default MyDeals;

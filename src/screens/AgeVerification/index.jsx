import React from 'react';
import { Linking, StyleSheet } from 'react-native';
import {
	Box,
	Button,
	Center,
	Heading,
	Image,
	ScrollView,
	Text,
	useToast,
	VStack,
} from 'native-base';
import DeviceInfo from 'react-native-device-info';
import { useDispatch } from 'react-redux';

import constants, {
	APP_AGE_LIMIT,
	APP_PRIVACY_POLICY_URL,
	APP_TERMS_AND_CONDITIONS_URL,
} from '../../utils/constants';
import ToastAlert from '../../components/ToastAlert';
import { setAgeVerified } from './ageVerificationSlice';

const AgeVerification = () => {
	/**
	 * Start Initials
	 */
	const toast = useToast();

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const openTermsAndConditions = async () => {
		const url = APP_TERMS_AND_CONDITIONS_URL;

		// Checking if the link is supported for links with custom URL scheme.
		const supported = await Linking.canOpenURL(url);

		if (supported) {
			// Opening the link with some app, if the URL scheme is "http" the web link should be opened
			// by some browser in the mobile
			await Linking.openURL(url);
		} else {
			toast.show({
				avoidKeyboard: true,
				render: ({ id }) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={`Don't know how to open this URL: ${url}`}
						/>
					);
				},
			});
		}
	};

	const openPrivacyPolicy = async () => {
		const url = APP_PRIVACY_POLICY_URL;

		// Checking if the link is supported for links with custom URL scheme.
		const supported = await Linking.canOpenURL(url);

		if (supported) {
			// Opening the link with some app, if the URL scheme is "http" the web link should be opened
			// by some browser in the mobile
			await Linking.openURL(url);
		} else {
			toast.show({
				avoidKeyboard: true,
				render: ({ id }) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={`Don't know how to open this URL: ${url}`}
						/>
					);
				},
			});
		}
	};

	const onPressSetAgeVerification = () => {
		dispatch(setAgeVerified(true));
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<ScrollView>
				<Box bg="custom.orange" style={styles.mainBox}>
					<Box style={styles.logoContainer}>
						<Image
							source={require('../../assets/images/new_logo.png')}
							style={{ width: 90, height: 90 }}
							alt="image"
						/>
					</Box>
					<Box style={styles.signInContainer}>
						<Center w="100%">
							<Box w="90%" mt={5}>
								<VStack justifyContent="space-between">
									<Heading
										fontSize={20}
										textAlign="center"
										mt={3}
										mb={5}>{`Are you ${APP_AGE_LIMIT} or older`}</Heading>

									<Text textAlign="center">
										{constants.TEXT.CONFIRM_YOUR_AGE}
									</Text>

									<Button
										mt={10}
										onPress={
											onPressSetAgeVerification
										}>{`I am above the age of ${APP_AGE_LIMIT}`}</Button>

									<Text mt={10} textAlign="center">
										By entering the application, the user agrees to our
										<Text
											color="custom.orange"
											fontWeight={600}
											mt={-0.5}
											onPress={openTermsAndConditions}>
											{` ${constants.LINK.TERMS_AND_CONDITIONS} `}
										</Text>
										<Text mt={-0.5}>and</Text>
										<Text
											color="custom.orange"
											fontWeight={600}
											mt={-0.5}
											onPress={openPrivacyPolicy}>
											{` ${constants.LINK.PRIVACY_POLICY}`}
										</Text>
										.
									</Text>
								</VStack>
							</Box>
						</Center>
					</Box>
				</Box>
			</ScrollView>
		</>
	);
};

const styles = StyleSheet.create({
	mainBox: {
		flex: 1,
	},
	logoContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		marginTop: DeviceInfo.hasNotch() ? '15%' : '10%',
	},
	signInContainer: {
		flex: 2,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		backgroundColor: 'white',
		marginTop: '5%',
	},
});

export default AgeVerification;

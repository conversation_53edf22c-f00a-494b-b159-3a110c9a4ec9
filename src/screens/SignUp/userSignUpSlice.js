import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import {Toast} from 'native-base';

import {
	firebaseAppleSignIn,
	firebaseFacebookSignIn,
	firebaseGoogleSignIn,
	firebaseSignOut,
	firebaseSignUp,
	signInWithFacebookCredentials,
	signInWithGoogleCredentials,
} from '../../services/firebaseAuth';
import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';
import {
	addUpdateDevice,
	checkUser,
	getFavoriteDealIDs,
	getUserData,
	verifyEmail,
} from '../SignIn/userSignInSlice';

export const signUp = createAsyncThunk(
	'user/signUp',
	async (data, thunkAPI) => {
		return await firebaseSignUp(data)
			.then(async res => {
				const userData = {
					uid: res.uid,
					role: data.role,
					name: res.displayName,
					email: res.email,
					phone: res.phoneNumber,
					photo: res.photoURL,
					provider: 'email',
				};
				await thunkAPI.dispatch(addUser(userData));
				await firebaseSignOut();
			})
			.catch(error => {
				return thunkAPI.rejectWithValue(error?.data ? error.data : error);
			});
	},
);

export const addUser = createAsyncThunk(
	'user/addUser',
	async (data, thunkAPI) => {
		return await apiCall({
			url: constants.API_ROUTES.ADD_USER,
			method: 'POST',
			data: {
				uid: data.uid,
				role: data.role,
				name: data.name ? data.name : '',
				email: data.email,
				phone: data.phone,
				photo: data.photo,
				emailVerified: true,
				provider: data.provider,
				socialId: data.socialId ? data.socialId : '',
			},
		})
			.then(res => res)
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const googleSignUp = createAsyncThunk(
	'user/googleSignUp',
	async (data, thunkAPI) => {
		return await firebaseGoogleSignIn()
			.then(async res => {
				const user = await thunkAPI
					.dispatch(checkUser(res.user.email))
					.unwrap()
					.then(r => r);

				if (user) {
					Toast.show({
						avoidKeyboard: true,
						title: constants.ERROR_MESSAGE.ALREADY_HAVE_ACCOUNT,
					});
					return user;
				} else {
					const userData = await signInWithGoogleCredentials(res.idToken);
					userData.role = data;
					userData.socialId = res.user.id;
					userData.provider = 'google';
					await thunkAPI.dispatch(addUser(userData));

					await thunkAPI.dispatch(addUpdateDevice());
					await thunkAPI.dispatch(getFavoriteDealIDs());

					await thunkAPI.dispatch(getUserData());
					return userData;
				}
			})
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const facebookSignUp = createAsyncThunk(
	'user/facebookSignUp',
	async (data, thunkAPI) => {
		return await firebaseFacebookSignIn()
			.then(async res => {
				const user = await thunkAPI
					.dispatch(checkUser(res.user.email))
					.unwrap()
					.then(r => r);

				if (user) {
					Toast.show({
						avoidKeyboard: true,
						title: constants.ERROR_MESSAGE.ALREADY_HAVE_ACCOUNT,
					});
					return user;
				} else {
					const userData = await signInWithFacebookCredentials(res.idToken);
				
					userData.role = data;
					userData.socialId = res.user.id;
					userData.provider = 'facebook';
					userData.uid = userData.user.uid;
					userData.name = userData.user.displayName;
					userData.email = userData.user.email;
					userData.phone = userData.user.phoneNumber;
					userData.photo = userData.user.photoURL;

					await thunkAPI.dispatch(addUser(userData));

					await thunkAPI.dispatch(addUpdateDevice());
					await thunkAPI.dispatch(getFavoriteDealIDs());

					await thunkAPI.dispatch(getUserData());
					return userData;
				}
			})
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

// SignUp with apple account and login if account is exist in database with message.
export const appleSignUp = createAsyncThunk(
	'user/appleSignUp', // Action type for appleSignUp
	async (data, thunkAPI) => {
		// Start the async function for Apple sign-up
		return await firebaseAppleSignIn() // Call the firebaseAppleSignIn function
			.then(async res => {
				// Once the sign-in is successful, execute the following
				const user = await thunkAPI
					.dispatch(checkUser(res.user.email)) // Dispatch checkUser action with the user's email
					.unwrap() // Unwrap the result of the promise returned by checkUser
					.then(r => r); // Store the result in the user variable

				if (user) {
					// If a user with the email already exists, show an error toast
					Toast.show({
						avoidKeyboard: true, // Avoid showing the toast over the keyboard
						title: constants.SUCCESS_MESSAGE.LOGGED_IN_WITH_APPLE_ACCOUNT, // Display account already exists account.
					});
					return user; // Return the existing user
				} else {
					// If no existing user is found, proceed to register the new user
					res.user.role = data; // Set the role of the user from the provided data
					res.user.provider = 'apple.com'; // Set the provider to Apple sign-in
					res.user.socialId = ''; // Set an empty social ID (can be modified based on the business logic)

					await thunkAPI.dispatch(addUser(res.user)); // Dispatch addUser action to create the new user

					await thunkAPI.dispatch(addUpdateDevice()); // Dispatch addUpdateDevice to update or register the user's device
					await thunkAPI.dispatch(getFavoriteDealIDs()); // Dispatch getFavoriteDealIDs to fetch the user's favorite deals

					await thunkAPI.dispatch(getUserData()); // Dispatch getUserData to fetch the full user data after registration
					return res.user; // Return the newly registered user data
				}
			});
	},
);

const userSignUpSlice = createSlice({
	name: 'userSignUp',
	initialState: {},
});

export default userSignUpSlice.reducer;

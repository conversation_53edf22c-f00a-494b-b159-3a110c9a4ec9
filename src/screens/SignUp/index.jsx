import React, { useState } from 'react';
import { Platform, StyleSheet } from 'react-native';
import {
	Box,
	Text,
	Heading,
	VStack,
	FormControl,
	Input,
	Link,
	Button,
	HStack,
	Center,
	Pressable,
	Icon,
	useToast,
	Image,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useForm, Controller } from 'react-hook-form';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import DeviceInfo from 'react-native-device-info';

import constants from '../../utils/constants';
import { useDispatch } from 'react-redux';
import { signUp } from './userSignUpSlice';
import ToastAlert from '../../components/ToastAlert';
import SpinnerOverlay from '../../components/SpinnerOverlay';
import SocialLoginButton from '../../components/SocialLoginButton';

const SignUp = ({ navigation }) => {
	/**
	 * Start Initials
	 */
	const [userRole, setUserRole] = useState(constants.ROLE.CUSTOMER);
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [loading, setLoading] = useState(false);

	const {
		control,
		handleSubmit,
		watch,
		reset,
		formState: { errors, isValid },
	} = useForm({
		defaultValues: {
			email: '',
			password: '',
			confirm_password: '',
			userRole: userRole,
		},
	});

	const dispatch = useDispatch();

	const toast = useToast();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const onSubmit = data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}
		setLoading(true);
		data.role = userRole;

		dispatch(signUp(data))
			.unwrap()
			.then(() => {
				toast.show({
					avoidKeyboard: true,
					render: ({ id }) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.VERIFICATION_EMAIL_SENT}
							/>
						);
					},
				});
				reset();
				navigation.navigate(constants.ROUTE.SIGN_IN);
			})
			.catch(err =>
				toast.show({
					avoidKeyboard: true,
					render: ({ id }) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={err?.message || err}
							/>
						);
					},
				}),
			)
			.finally(() => setLoading(false));
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Box bg="custom.orange" style={styles.mainBox}>
					<Box style={styles.logoContainer}>
						<Image
							source={require('../../assets/images/new_logo.png')}
							style={{ width: 90, height: 90 }}
							alt="image"
						/>
					</Box>
					<Box style={styles.signInContainer}>
						<Center w="100%">
							<Box w="90%" mt={'5'}>
								<VStack justifyContent="space-between">
									<VStack>
										<VStack space={3} alignItems="center">
											<Heading>{constants.TITLE.CREATE_AN_ACCOUNT}</Heading>

											<Button.Group
												isAttached
												mt={4}
												mx={{
													base: 'auto',
													md: 0,
												}}
												size="sm"
												borderRadius={15}
												backgroundColor="custom.lightOrange">
												<Button
													variant={
														userRole === constants.ROLE.CUSTOMER
															? 'solid'
															: 'subtle'
													}
													borderRadius={
														userRole === constants.ROLE.CUSTOMER ? 15 : 0
													}
													onPress={() => setUserRole(constants.ROLE.CUSTOMER)}
													_text={{
														fontSize: 14,
														fontWeight: Platform.select({
															ios: '600',
															android: 600,
														}),
													}}>
													{constants.ROLE.CUSTOMER}
												</Button>
												<Button
													variant={
														userRole === constants.ROLE.CUSTOMER
															? 'subtle'
															: 'solid'
													}
													borderRadius={
														userRole === constants.ROLE.CUSTOMER ? 0 : 15
													}
													onPress={() => setUserRole(constants.ROLE.RETAILER)}
													_text={{
														fontSize: 14,
														fontWeight: Platform.select({
															ios: '600',
															android: 600,
														}),
													}}>
													{constants.ROLE.RETAILER}
												</Button>
											</Button.Group>
										</VStack>

										<VStack space={7} mt="6">
											<FormControl>
												<FormControl.Label>
													{constants.LABEL.YOUR_EMAIL_ID}
												</FormControl.Label>
												<Controller
													control={control}
													name="email"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES.EMAIL_REQUIRED,
														},
														pattern: {
															value: constants.REGEX.EMAIL,
															message:
																constants.VALIDATION_MESSAGES.VALID_EMAIL,
														},
													}}
													render={({ field: { onChange, onBlur, value } }) => (
														<Input
															size="xl"
															autoCapitalize="none"
															keyboardType="email-address"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
														/>
													)}
												/>
												{errors.email && (
													<FormControl.HelperText _text={{ color: 'custom.red' }}>
														{errors.email.message}
													</FormControl.HelperText>
												)}
											</FormControl>

											<FormControl>
												<FormControl.Label>
													{constants.LABEL.PASSWORD}
												</FormControl.Label>
												<Controller
													control={control}
													name="password"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES.PASSWORD_REQUIRED,
														},
														minLength: {
															value: 6,
															message:
																constants.VALIDATION_MESSAGES.PASSWORD_LENGTH,
														},
														pattern: {
															value: constants.REGEX.PASSWORD,
															message:
																constants.VALIDATION_MESSAGES.WEAK_PASSWORD,
														},
													}}
													render={({ field: { onChange, onBlur, value } }) => (
														<Input
															size="xl"
															secureTextEntry={!showPassword}
															InputRightElement={
																<Pressable
																	onPress={() =>
																		setShowPassword(!showPassword)
																	}>
																	<Icon
																		as={MaterialCommunityIcons}
																		name={
																			!showPassword
																				? 'eye-outline'
																				: 'eye-off-outline'
																		}
																		size="md"
																	/>
																</Pressable>
															}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
														/>
													)}
												/>
												{errors.password && (
													<FormControl.HelperText _text={{ color: 'custom.red' }}>
														{errors.password.message}
													</FormControl.HelperText>
												)}
											</FormControl>

											<FormControl>
												<FormControl.Label>
													{constants.LABEL.CONFIRM_PASSWORD}
												</FormControl.Label>
												<Controller
													control={control}
													name="confirm_password"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES
																	.CONFIRM_PASSWORD_REQUIRED,
														},
														validate: val => {
															if (watch('password') !== val) {
																return constants.VALIDATION_MESSAGES
																	.CONFIRM_PASSWORD_NOT_MATCH;
															}
														},
													}}
													render={({ field: { onChange, onBlur, value } }) => (
														<Input
															size="xl"
															secureTextEntry={!showConfirmPassword}
															InputRightElement={
																<Pressable
																	onPress={() =>
																		setShowConfirmPassword(!showConfirmPassword)
																	}>
																	<Icon
																		as={MaterialCommunityIcons}
																		name={
																			!showConfirmPassword
																				? 'eye-outline'
																				: 'eye-off-outline'
																		}
																		size="md"
																	/>
																</Pressable>
															}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
														/>
													)}
												/>
												{errors.confirm_password && (
													<FormControl.HelperText _text={{ color: 'custom.red' }}>
														{errors.confirm_password.message}
													</FormControl.HelperText>
												)}
											</FormControl>

											<Button
												onPress={handleSubmit(onSubmit)}
												isDisabled={loading}>
												{constants.BUTTON.SIGN_UP}
											</Button>
										</VStack>

										<VStack space={5} mt="5" alignItems="center">
											<Text mb="1">{constants.TEXT.OR_SIGN_UP_WITH}</Text>
											<SocialLoginButton
												userRole={userRole}
												setLoading={setLoading}
											/>
										</VStack>
									</VStack>

									<HStack my={6} justifyContent="center" alignItems="flex-end">
										<Text>{constants.TEXT.ALREADY_HAVE_AN_ACCOUNT}</Text>
										<Link
											onPress={() =>
												navigation.navigate(constants.ROUTE.SIGN_IN)
											}>
											{constants.LINK.SIGN_IN}
										</Link>
									</HStack>
								</VStack>
							</Box>
						</Center>
					</Box>
				</Box>
			</KeyboardAwareScrollView>
			<SpinnerOverlay loading={loading} />
		</>
	);
};

const styles = StyleSheet.create({
	mainBox: {
		flex: 1,
	},
	logoContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		marginTop: DeviceInfo.hasNotch() ? '15%' : '10%',
	},
	signInContainer: {
		flex: 2,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		backgroundColor: 'white',
		marginTop: '5%',
	},
});

export default SignUp;

import React from 'react';
import {Circle, G, Rect, Text} from 'react-native-svg';
import {Dimensions} from 'react-native';
import constants from '../../utils/constants';

const screenWidth = Dimensions.get('window').width;

const Tooltip = props => {
	/**
	 * Start Initials
	 */
	const {data, tooltipPos} = props;

	const textX = data.labels[data.index];
	const position = data.labels.length === data.index + 1 ? 'left' : 'right';

	let tipW = 136,
		tipH = 36,
		tipX = 5,
		tipY = -9,
		tipTxtX = 12,
		tipTxtY = 6;
	const posY = tooltipPos.y;
	const posX = tooltipPos.x;

	if (posX > screenWidth - tipW) {
		tipX = -(tipX + tipW);
		tipTxtX = tipTxtX - tipW - 6;
	}

	const boxPosX = position === 'left' ? posX - tipW - 10 : posX;
	/**
	 * End Initials
	 */

	return (
		<G>
			<Circle
				cx={posX}
				cy={posY}
				r={4}
				stroke={'transparent'}
				strokeWidth={2}
				fill={'transparent'}
			/>
			<G x={boxPosX < 40 ? 40 : boxPosX} y={posY}>
				<Rect
					x={tipX + 1}
					y={tipY - 1}
					width={tipW - 100}
					height={tipH - 12}
					fill={'rgba(255, 255, 255, 0.9)'}
					rx={2}
					ry={2}
				/>
				<Rect
					x={tipX}
					y={tipY}
					width={tipW - 100}
					height={tipH - 12}
					rx={2}
					ry={2}
					fill={constants.COLORS.WHITE}
					stroke={constants.COLORS.GREY6}
				/>
				<Text x={tipTxtX} y={tipTxtY} fontSize="10" textAnchor="start">
					{tooltipPos.value}
				</Text>

				<Text
					x={tipTxtX}
					y={tipTxtY}
					fontSize="12"
					fill={constants.COLORS.BLACK}
					textAnchor="start">
					{Math.round(tooltipPos.value)}
				</Text>
			</G>
		</G>
	);
};

export default Tooltip;

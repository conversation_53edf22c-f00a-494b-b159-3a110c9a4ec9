import React from 'react';
import {
	Box,
	Center,
	VStack,
	HStack,
	Image,
	Stack,
	Heading,
	Text,
	Button,
} from 'native-base';

import constants from '../../utils/constants';

const DealCard = () => {
	return (
		<Box w="100%">
			<VStack>
				<HStack space={1}>
					<Box>
						<Center bg="custom.green" px="3" py="0.5" borderRadius={2}>
							<Text color="white" fontSize={5} bold>
								15%
							</Text>
							<Text color="white" fontSize={5} bold>
								OFF
							</Text>
						</Center>
					</Box>

					<Box w="20%">
						<Image
							source={require('../../assets/images/bottle.png')}
							alt="image"
						/>
					</Box>

					<Stack space={2} mr={4}>
						<Stack>
							<Heading fontSize={16} fontWeight={400} fontStyle="normal">
								Zacapa
							</Heading>
							<Text fontSize={9} fontWeight={500}>
								750 ml bottle
							</Text>
						</Stack>
						<HStack space={4}>
							<Text fontSize={10} fontWeight={500} strikeThrough>
								$48.00
							</Text>
							<Text fontSize={12} fontWeight={700}>
								$24.00
							</Text>
						</HStack>
					</Stack>

					<VStack space={4}>
						<Stack alignItems="flex-end">
							<Text fontSize={10} fontWeight={500}>
								Status
							</Text>
							<Text color="custom.orange" fontSize={10} fontWeight={500}>
								Active
							</Text>
						</Stack>

						<Stack space={1}>
							<Button size={'xs'} py={0.5} borderRadius={5}>
								{constants.BUTTON.EDIT_DEAL}
							</Button>
							<Button size={'xs'} py={0.5} borderRadius={5}>
								{constants.BUTTON.COPY_DEAL}
							</Button>
						</Stack>
					</VStack>
				</HStack>
			</VStack>
		</Box>
	);
};

export default DealCard;

import React, {useState} from 'react';
import {
	Box,
	Center,
	Divider,
	<PERSON>ing,
	ScrollView,
	Stack,
	VStack,
} from 'native-base';
import {Dimensions, Platform} from 'react-native';
import {LineChart} from 'react-native-chart-kit';

import DealCard from './DealCard';
import constants from '../../utils/constants';
import Tooltip from './Tooltip';

const {height: viewportHeight} = Dimensions.get('window');

const Report = () => {
	/**
	 * Start Initials
	 */
	let [tooltipPos, setTooltipPos] = useState({
		x: 0,
		y: 0,
		visible: false,
		value: 0,
	});

	const lineChartConfig = {
		propsForBackgroundLines: {
			strokeDasharray: '',
		},
		backgroundColor: constants.COLORS.WHITE,
		backgroundGradientFrom: constants.COLORS.WHITE,
		backgroundGradientTo: constants.COLORS.WHITE,
		decimalPlaces: 2, // optional, defaults to 2dp
		color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
		labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
		style: {
			borderRadius: 16,
		},
		propsForDots: {
			r: '4',
			strokeWidth: '1',
			stroke: constants.COLORS.ORANGE,
		},
	};

	const data = {
		labels: ['January', 'February', 'March', 'April', 'May', 'June'],
		datasets: [
			{
				color: (opacity = 1) => constants.COLORS.ORANGE,
				strokeWidth: 1,
				data: [
					Math.random() * 100,
					Math.random() * 100,
					Math.random() * 100,
					Math.random() * 100,
					Math.random() * 100,
					Math.random() * 100,
				],
			},
		],
	};
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const chartDotClick = data => {
		let isSamePoint = tooltipPos.x === data.x && tooltipPos.y === data.y;

		isSamePoint
			? setTooltipPos(previousState => {
					return {
						...previousState,
						value: data.value,
						visible: !previousState.visible,
					};
				})
			: setTooltipPos({x: data.x, value: data.value, y: data.y, visible: true});
	};
	/**
	 * End Methods
	 */

	return (
		<ScrollView>
			<Center w="100%" backgroundColor="white">
				<Box
					w="90%"
					h={viewportHeight - Platform.select({ios: 50, android: 0})}>
					<Stack space={3} mt={6}>
						<Heading my={2}>{constants.TITLE.IMPRESSION}</Heading>
						<Box justifyContent="center" mb={3}>
							<LineChart
								data={data}
								width={Dimensions.get('window').width - 40} // from react-native
								height={220}
								yAxisLabel="$"
								yAxisSuffix="k"
								yAxisInterval={1} // optional, defaults to 1
								withVerticalLines={false}
								getDotColor={() => {
									return constants.COLORS.ORANGE;
								}}
								chartConfig={lineChartConfig}
								bezier
								style={{
									marginVertical: 8,
									borderRadius: 16,
								}}
								decorator={() => (
									<Tooltip data={data} tooltipPos={tooltipPos} />
								)}
								onDataPointClick={chartDotClick}
							/>
						</Box>
						<VStack space={6}>
							<Divider />
							<DealCard />
						</VStack>
					</Stack>
				</Box>
			</Center>
		</ScrollView>
	);
};
export default Report;

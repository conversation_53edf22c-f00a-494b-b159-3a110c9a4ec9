import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Dimensions, StyleSheet} from 'react-native';
import {
	Box,
	Button,
	Center,
	HStack,
	Icon,
	Stack,
	Text,
	VStack,
} from 'native-base';
import MapView, {<PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch} from 'react-redux';

import constants from '../../utils/constants.js';
import {
	GooglePlaceCoordinates,
	GooglePlaceName,
} from '../../services/RNGoogleMaps';
import {setAddress, setCoordinates} from '../AddAddress/addAddressSlice';

const {width, height} = Dimensions.get('window');
const initialCoordinates = {
	latitude: 28.6129,
	longitude: 77.2295,
	latitudeDelta: 0,
	longitudeDelta: 0,
};

const ConfirmLocation = ({navigation, route}) => {
	/**
	 * Start Initials
	 */
	const [coordinate, setCoordinate] = useState(initialCoordinates);

	const [markerCoordinates, setMarkerCoordinates] = useState({
		latitude: initialCoordinates.latitude,
		longitude: initialCoordinates.longitude,
	});

	const [addressDesc, setAddressDesc] = useState('');

	const dispatch = useDispatch();

	const mapRef = useRef();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		(async () => {
			await getPlaceCoordinates(route.params.placeId);
		})();
	}, [route.params.placeId]);

	useEffect(() => {
		mapRef.current.animateToRegion(coordinate, 1000);
	}, [coordinate]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const getPlaceName = useCallback(async (lat, long, latDelta, longDelta) => {
		const data = await GooglePlaceName(lat, long);
		setMarkerCoordinates({
			latitude: lat,
			longitude: long,
		});

		if (data && data !== {}) {
			setCoordinate({
				latitude: data.coord.lat,
				longitude: data.coord.lng,
				longitudeDelta: longDelta,
				latitudeDelta: latDelta,
			});

			setAddressDesc(data.fullName);
		}
	}, []);

	const getPlaceCoordinates = async place_id => {
		const data = await GooglePlaceCoordinates(place_id);
		setAddressDesc(data.fullName);

		if (data && data !== {}) {
			setCoordinate({
				latitude: data.coord.lat,
				longitude: data.coord.lng,
				longitudeDelta: 0.01 * (width / height),
				latitudeDelta: 0.01,
			});
		}
	};

	const onPressConfirmLocation = useCallback(() => {
		dispatch(setCoordinates(coordinate));
		dispatch(setAddress(addressDesc));
	}, [coordinate, addressDesc, dispatch]);
	/**
	 * End Methods
	 */

	return (
		<Center w="100%">
			<Box w="100%" bg="white">
				<VStack space={2}>
					<MapView
						ref={mapRef}
						provider={PROVIDER_GOOGLE}
						style={styles.map}
						showsIndoors={false}
						showsBuildings={false}
						showsScale={true}
						showsUserLocation={true}
						showsMyLocationButton={true}
						initialRegion={{
							latitude: coordinate.latitude,
							longitude: coordinate.longitude,
							latitudeDelta: coordinate.latitudeDelta,
							longitudeDelta: coordinate.longitudeDelta,
						}}
						onRegionChangeComplete={region => {
							getPlaceName(
								region.latitude,
								region.longitude,
								region.latitudeDelta,
								region.longitudeDelta,
							);
						}}>
						{coordinate && (
							<Marker
								draggable
								coordinate={{
									latitude: markerCoordinates.latitude,
									longitude: markerCoordinates.longitude,
								}}
								onDragEnd={e => setMarkerCoordinates(e.nativeEvent.coordinate)}
								showsUserLocation={true}
								followsUserLocation={true}
							/>
						)}
					</MapView>

					<Stack w="95%" alignSelf="center" space={2}>
						<HStack>
							<Icon
								as={MaterialCommunityIcons}
								name="map-marker"
								size={7}
								ml={2}
								mr={2}
								color="custom.orange"
							/>
							<Text w="90%" pr={1} fontSize={14} bold>
								{addressDesc}
							</Text>
						</HStack>
						<Button mt="3" onPress={onPressConfirmLocation}>
							{constants.BUTTON.CONFIRM_LOCATION}
						</Button>
					</Stack>
				</VStack>
			</Box>
		</Center>
	);
};

const styles = StyleSheet.create({
	map: {
		width: width,
		height: height - height / 3,
	},
});

export default ConfirmLocation;

import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getFavoriteDeal = createAsyncThunk(
	'favoriteDeal/getFavoriteDeal',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url:
					constants.API_ROUTES.GET_FAVORITE +
					`?page=${data.page}&limit=${data.limit}`,
				method: 'GET',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const unFavoriteDeal = createAsyncThunk(
	'favoriteDeal/unFavoriteDeal',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.REMOVE_FAVORITE + `/${data}`,
				method: 'PATCH',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const markFavoriteDeal = createAsyncThunk(
	'favoriteDeal/markFavoriteDeal',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.ADD_FAVORITE,
				method: 'POST',
				data: {
					deal: data,
				},
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const favoriteDealSlice = createSlice({
	name: 'favoriteDeal',
	initialState: {
		favorites: [],
	},
	reducers: {
		setFavoriteDealIDs(state, action) {
			if (Array.isArray(action.payload)) {
				state.favorites = [...action.payload];
			}
		},
	},
	extraReducers: builder => {
		builder
			.addCase(unFavoriteDeal.fulfilled, (state, action) => {
				if (action.payload) {
					const newFavArr = state.favorites.filter(function (value) {
						return value._id !== action.payload._id;
					});
					state.favorites = [...newFavArr];
				}
			})
			.addCase(markFavoriteDeal.fulfilled, (state, action) => {
				if (action.payload) {
					state.favorites = [
						...state.favorites,
						...[{deal: action.payload.deal, _id: action.payload._id}],
					];
				}
			});
	},
});

export const {setFavoriteDealIDs} = favoriteDealSlice.actions;

export default favoriteDealSlice.reducer;

import React, {useCallback, useState} from 'react';

import {ActivityIndicator, Dimensions, FlatList} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {Box, Button, Center, Text} from 'native-base';
import {useDispatch, useSelector} from 'react-redux';

import DealCard from '../../components/DealCard';
import {getFavoriteDeal} from './favoriteDealSlice';
import constants from '../../utils/constants';

const {width} = Dimensions.get('window');
const DEFAULT_PAGE = 1,
	DEFAULT_LIMIT = 10;

const Favorite = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const [favorites, setFavorites] = useState([]);
	const [loading, setLoading] = useState(true);
	const [hasData, setHasData] = useState(true);
	const [totalPages, setTotalPages] = useState(1);
	const [page, setPage] = useState(DEFAULT_PAGE);
	const [favoriteMsg, setFavoriteMsg] = useState('');
	const [initialLoading, setInitialLoading] = useState(false);

	const dispatch = useDispatch();

	const user = useSelector(state => state.userSignIn.userData);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			setFavorites([]);
			setHasData(true);
			fetchFavoriteDeals();
		}, []),
	);
	/**
	 * Start Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchFavoriteDeals = () => {
		setInitialLoading(true);
		setPage(1);
		setHasData(true);
		dispatch(
			getFavoriteDeal({
				page: page,
				limit: DEFAULT_LIMIT,
			}),
		)
			.unwrap()
			.then(res => {
				setFavorites(res.data);
				setTotalPages(res.totalPages);
				if (res.totalPages == 0 || res.totalPages == 1) {
					setHasData(false);
					setFavoriteMsg(
						totalPages == 0
							? constants.LABEL.NO_FAVORITES_FOUND
							: constants.LABEL.NO_MORE_FAVORITES,
					);
				}
			})
			.finally(() => setInitialLoading(false));
	};

	const retrieveMore = () => {
		if (hasData) {
			setLoading(true);
			setPage(page + 1);
			dispatch(
				getFavoriteDeal({
					page: page,
					limit: DEFAULT_LIMIT,
				}),
			)
				.unwrap()
				.then(res => {
					setFavorites(prevFavorites => [...prevFavorites, ...res.data]);

					if (totalPages == page) {
						setHasData(false);
						setFavoriteMsg(constants.LABEL.NO_MORE_FAVORITES);
					}
				})
				.finally(() => setLoading(false));
		}
	};

	const removeItemFromList = favId => {
		setFavorites(favorites.filter(i => i._id !== favId));
	};

	const ItemView = ({item}) => {
		return (
			<Center w={width / 2.1} my={2}>
				<DealCard
					navigation={navigation}
					productID={item.deal.product?._id}
					productName={item.deal.product?.name}
					productPhoto={item.deal.product?.photo}
					productSize={item.deal.product?.size}
					productPrice={item.deal.productPrice}
					discountPrice={item.deal.discountPrice}
					discountType={item.deal.discountType}
					discountValue={item.deal.discountValue}
					dealId={item.deal._id}
					lowStock={item.deal.lowStock}
					user={user}
					removeItemFromList={removeItemFromList}
					quantity={item.deal.quantity}
				/>
			</Center>
		);
	};

	const renderFooter = () => {
		return (
			<Box w="100%" justifyContent="center" alignItems="center" py={2}>
				{hasData ? (
					<Button
						variant="ghost"
						isLoading={loading}
						isLoadingText="Loading"
						onPress={retrieveMore}>
						{constants.BUTTON.LOAD_MORE}
					</Button>
				) : (
					<Text>{favoriteMsg}</Text>
				)}
			</Box>
		);
	};
	/**
	 * Start Methods
	 */

	if (initialLoading) {
		return <ActivityIndicator style={{paddingTop: 15}} />;
	}

	return (
		<Center w="100%">
			<Box px={3} mt={3} flexDirection="row">
				<FlatList
					data={favorites}
					keyExtractor={(item, index) => index.toString()}
					enableEmptySections={true}
					renderItem={ItemView}
					numColumns={2}
					ListFooterComponent={renderFooter}
					onEndReached={retrieveMore}
					onRefresh={fetchFavoriteDeals}
					refreshing={!loading}
				/>
			</Box>
		</Center>
	);
};

export default Favorite;

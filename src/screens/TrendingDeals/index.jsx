import React, {useCallback, useState} from 'react';
import {useFocusEffect} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {Box, Center} from 'native-base';

import {getAllCategory} from '../Deals/dealsSlice';
import DealCategoryTab from '../../components/DealCategoryTabs';

const TrendingDeals = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const [categoryList, setCategoryList] = useState([]);

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			fetchCategoryList();
		}, []),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchCategoryList = () => {
		dispatch(getAllCategory())
			.unwrap()
			.then(res => {
				setCategoryList(res);
			});
	};
	/**
	 * End Methods
	 */

	return (
		<Center w="100%" bg="white">
			<Box>
				{categoryList.length > 0 && (
					<DealCategoryTab
						navigation={navigation}
						categoryList={categoryList}
						type="trending"
					/>
				)}
			</Box>
		</Center>
	);
};

export default TrendingDeals;

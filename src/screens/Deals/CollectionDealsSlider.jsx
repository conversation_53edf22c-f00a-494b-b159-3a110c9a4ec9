import React, {useEffect, useState} from 'react';
import {getTopTypeDeals} from './dealsSlice';
import {useDispatch} from 'react-redux';
import {Button, Heading, HStack, Icon, VStack} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useIsFocused} from '@react-navigation/native';

import constants from '../../utils/constants';
import DealSlider from '../../components/DealSlider';

const CollectionDealsSlider = props => {
	/**
	 * Start Initials
	 */
	const isFocused = useIsFocused();
	const {navigation, user} = props;

	const [collectionDeals, setCollectionDeals] = useState([]);

	const dispatch = useDispatch();
	/**
	 * Start Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		fetchCollectionDealsList();
	}, [props, isFocused]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchCollectionDealsList = () => {
		dispatch(getTopTypeDeals('collection'))
			.unwrap()
			.then(res => {
				setCollectionDeals(res);
			});
	};
	/**
	 * End Methods
	 */

	if (collectionDeals.length <= 0) {
		return null;
	}

	return (
		<VStack space={2} mt={4}>
			<HStack justifyContent="space-between" alignItems="center">
				<Heading fontSize={20} bold>
					{constants.TITLE.COLLECTION}
				</Heading>
				<Button
					pr={0}
					variant="ghost"
					_text={{fontSize: 14, color: 'custom.orange'}}
					endIcon={
						<Icon
							as={MaterialCommunityIcons}
							name="chevron-right"
							color="custom.orange"
							size="md"
							ml={-1}
							mt={0.5}
						/>
					}
					onPress={() =>
						navigation.navigate(constants.ROUTE.COLLECTIONS_DEALS)
					}>
					{constants.BUTTON.VIEW_MORE}
				</Button>
			</HStack>
			{collectionDeals.length > 0 && (
				<DealSlider
					navigation={navigation}
					deals={collectionDeals}
					user={user}
				/>
			)}
		</VStack>
	);
};

export default CollectionDealsSlider;

import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';

export const getAllCategory = createAsyncThunk(
	'deals/getAllCategory',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_CATEGORIES,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

export const getAllBanner = createAsyncThunk(
	'deals/getAllBanner',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_BANNERS,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

export const getTopTypeDeals = createAsyncThunk(
	'deals/getTopDeals',
	async (data, thunkAPI) => {
		const {userData, businessData} = thunkAPI.getState().userSignIn;
		const {coordinate} = thunkAPI.getState().addAddress;

		const {ROLE} = constants;
		let longitude, latitude;
		if (userData.role === ROLE.RETAILER) {
			if (businessData.location === undefined) {
				longitude = 0;
				latitude = 0;
			} else {
				[longitude, latitude] = businessData?.location?.coordinates;
			}
		} else {
			({longitude, latitude} = coordinate);
		}

		const url = `${constants.API_ROUTES.GET_TOP_DEAL_BY_TYPE}/${data}?lat=${latitude}&log=${longitude}`;

		return await apiCall({url, method: 'GET'}, false)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

const dealsSlice = createSlice({
	name: 'deals',
	initialState: {},
});

export default dealsSlice.reducer;

import * as React from 'react';
import Svg, { Path, Circle } from 'react-native-svg';

const LocationNotServiceableSvg = (props) => (
    <Svg
        width={200}
        height={200}
        viewBox="0 0 512 512"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}>
        <Circle cx={256} cy={256} r={256} fill="#FFF0E6" />
        <Path
            d="M256 120c-66.168 0-120 53.832-120 120 0 90 120 240 120 240s120-150 120-240c0-66.168-53.832-120-120-120zm0 190c-38.598 0-70-31.402-70-70s31.402-70 70-70 70 31.402 70 70-31.402 70-70 70z"
            fill="#FF8731"
        />
        <Path
            d="M296 240c0 22.091-17.909 40-40 40s-40-17.909-40-40 17.909-40 40-40 40 17.909 40 40z"
            fill="#243762"
        />
        <Path
            d="M376 240c0 66.168-53.832 120-120 120S136 306.168 136 240"
            stroke="#243762"
            strokeWidth={20}
            strokeLinecap="round"
        />
        <Path
            d="M376 240L136 240"
            stroke="#f05023"
            strokeWidth={20}
            strokeLinecap="round"
        />
    </Svg>
);

export default LocationNotServiceableSvg;
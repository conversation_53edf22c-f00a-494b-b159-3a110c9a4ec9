import React from 'react';
import { Box, Button, <PERSON>S<PERSON>ck, Icon, IconButton, Text } from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import DeviceInfo from 'react-native-device-info';

import constants, { APP_NAME } from '../../utils/constants';

const AddressBar = props => {
	/**
	 * Start Initials
	 */
	const { navigation, address, user, viewportWidth } = props;

	const insets = useSafeAreaInsets();
	/**
	 * Start Initials
	 */

	/**
	 * Start Methods
	 */
	const navigateToAdAddress = () => {
		navigation.navigate(constants.ROUTE.ADD_ADDRESS);
	};

	const navigateToNotifications = () => {
		navigation.navigate(constants.ROUTE.NOTIFICATIONS);
	};
	/**
	 * End Methods
	 */

	return (
		<Box
			pb={1}
			pt={insets.top + 2 - (DeviceInfo.hasNotch() ? 10 : 18)}
			style={{ backgroundColor: constants.COLORS.ORANGE }}>
			<HStack justifyContent="space-between" alignItems="center">
				{user.role === constants.ROLE.CUSTOMER ? (
					<Button
						variant="unstyled"
						leftIcon={
							<Icon
								as={MaterialCommunityIcons}
								name="map-marker-radius"
								color="white"
								size={5}
							/>
						}
						onPress={navigateToAdAddress}>
						<Text
							isTruncated
							w="100%"
							maxW={viewportWidth - 50}
							color="white"
							fontSize={13}
							bold>
							{address ? address : constants.LABEL.SELECT_LOCATION}
						</Text>
					</Button>
				) : (
					<Box
						w="100%"
						display="flex"
						flexDirection="row"
						justifyContent="space-between"
						alignItems="center">
						<Text color="white" fontSize={14} p={1} ml={1} bold>
							{APP_NAME}
						</Text>
						<IconButton
							variant="ghost"
							size="md"
							_icon={{
								as: MaterialCommunityIcons,
								name: 'bell',
								color: 'white',
							}}
							onPress={navigateToNotifications}
						/>
					</Box>
				)}
			</HStack>
		</Box>
	);
};

export default AddressBar;

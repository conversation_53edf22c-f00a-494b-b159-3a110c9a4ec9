import React, {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {Button, Heading, HStack, Icon, VStack} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useIsFocused} from '@react-navigation/native';

import {getTopTypeDeals} from './dealsSlice';
import constants from '../../utils/constants';
import DealSlider from '../../components/DealSlider';

const FlashDealsSlider = props => {
	/**
	 * Start Initials
	 */
	const {navigation, user} = props;

	const isFocused = useIsFocused();
	const dispatch = useDispatch();

	const [flashDeals, setFlashDeals] = useState([]);

	/**
	 * Start Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		fetchFlashDealsList();
	}, [props, isFocused]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchFlashDealsList = () => {
		dispatch(getTopTypeDeals('flash'))
			.unwrap()
			.then(res => {
				setFlashDeals(res);
			});
	};
	/**
	 * End Methods
	 */

	if (flashDeals.length <= 0) {
		return null;
	}

	return (
		<VStack space={2} mt={4}>
			<HStack justifyContent="space-between" alignItems="center">
				<Heading fontSize={20} bold>
					{constants.TITLE.FLASH_DEALS}
				</Heading>
				<Button
					pr={0}
					variant="ghost"
					_text={{fontSize: 14, color: 'custom.orange'}}
					endIcon={
						<Icon
							as={MaterialCommunityIcons}
							name="chevron-right"
							color="custom.orange"
							size="md"
							ml={-1}
							mt={0.5}
						/>
					}
					onPress={() => navigation.navigate(constants.ROUTE.FLASH_DEALS)}>
					{constants.BUTTON.VIEW_MORE}
				</Button>
			</HStack>
			<DealSlider navigation={navigation} deals={flashDeals} user={user} />
		</VStack>
	);
};

export default FlashDealsSlider;

import React, {useEffect, useState} from 'react';
import {getTopTypeDeals} from './dealsSlice';
import {useDispatch} from 'react-redux';
import {Button, Heading, HStack, Icon, VStack} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useIsFocused} from '@react-navigation/native';

import constants from '../../utils/constants';
import DealSlider from '../../components/DealSlider';

const NewDealsSlider = props => {
	/**
	 * Start Initials
	 */
	const isFocused = useIsFocused();
	const {navigation, user} = props;

	const [newDeals, setNewDeals] = useState([]);

	const dispatch = useDispatch();
	/**
	 * Start Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		fetchNewDealsList();
	}, [props, isFocused]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchNewDealsList = () => {
		dispatch(getTopTypeDeals('new'))
			.unwrap()
			.then(res => {
				setNewDeals(res);
			});
	};
	/**
	 * End Methods
	 */

	if (newDeals.length <= 0) {
		return null;
	}

	return (
		<VStack space={2} mt={4}>
			<HStack justifyContent="space-between" alignItems="center">
				<Heading fontSize={20} bold>
					{constants.TITLE.NEW}
				</Heading>
				<Button
					pr={0}
					variant="ghost"
					_text={{fontSize: 14, color: 'custom.orange'}}
					endIcon={
						<Icon
							as={MaterialCommunityIcons}
							name="chevron-right"
							color="custom.orange"
							size="md"
							ml={-1}
							mt={0.5}
						/>
					}
					onPress={() => navigation.navigate(constants.ROUTE.NEW_DEALS)}>
					{constants.BUTTON.VIEW_MORE}
				</Button>
			</HStack>
			<DealSlider navigation={navigation} deals={newDeals} user={user} />
		</VStack>
	);
};

export default NewDealsSlider;

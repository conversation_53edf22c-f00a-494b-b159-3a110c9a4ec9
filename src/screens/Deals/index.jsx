import React, { useEffect, useState } from 'react';
import { Dimensions } from 'react-native';
import { useSelector } from 'react-redux';
import { Box, Heading, ScrollView, VStack, Stack, Text, Center } from 'native-base';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import constants from '../../utils/constants';
import BannerSlider from '../../components/BannerSlider';
import CategorySlider from '../../components/CategorySlider';
import FlashDealsSlider from './FlashDealsSlider';
import TrendingDealsSlider from './TrendingDealsSlider';
import CollectionDealsSlider from './CollectionDealsSlider';
import NewDealsSlider from './NewDealsSlider';
import SearchBar from './SearchBar';
import AddressBar from './AddressBar';
import LocationNotServiceableSvg from './LocationNotServiceableSvg';

const { height: viewportHeight, width: viewportWidth } = Dimensions.get('window');

const Deals = ({ navigation }) => {
	/**
	 * Start Initials
	 */
	const insets = useSafeAreaInsets();
	const [isUSLocation, setIsUSLocation] = useState(true);

	const user = useSelector(state => state.userSignIn.userData);
	const storedAddress = useSelector(state => state.addAddress.address);

	useEffect(() => {
		checkLocationValidity();
	}, [storedAddress]);

	const checkLocationValidity = () => {
		if (!storedAddress) {
			setIsUSLocation(true);
			return;
		}
		const isUS = storedAddress.endsWith('USA') || storedAddress.endsWith('US');
		setIsUSLocation(isUS);
	};

	const renderContent = () => {
		if (!isUSLocation) {
			return (
				<Center flex={1} px={4}>
					<VStack space={4} alignItems="center">
						<LocationNotServiceableSvg />
						<Text fontSize="lg" textAlign="center" color="red.600">
							{constants.ERROR_MESSAGE.LOCATION_NOT_SERVICEABLE}
						</Text>
						<Text fontSize="md" textAlign="center" color="gray.600">
							{constants.ERROR_MESSAGE.SERVICE_IS_ONLY_IN_USA}
						</Text>
					</VStack>
				</Center>
			);
		}

		return (
			<ScrollView>
				<VStack mb={insets.bottom}>
					<Stack>
						<BannerSlider />
					</Stack>
					<VStack space={3}>
						<Heading color="custom.black" fontSize={20} bold ml={2}>
							{constants.TITLE.CATEGORIES}
						</Heading>
						<CategorySlider navigation={navigation} />
					</VStack>

					<VStack mx={2} space={2}>
						<FlashDealsSlider navigation={navigation} user={user} />
						<TrendingDealsSlider navigation={navigation} user={user} />
						<NewDealsSlider navigation={navigation} user={user} />
						<CollectionDealsSlider navigation={navigation} user={user} />
					</VStack>
				</VStack>
			</ScrollView>
		);
	};

	return (
		<Box h={viewportHeight - 50}>
			<Stack>
				<AddressBar
					navigation={navigation}
					address={storedAddress}
					user={user}
					viewportWidth={viewportWidth}
				/>
				<SearchBar navigation={navigation} />
			</Stack>
			{renderContent()}
		</Box>
	);
};

export default Deals;

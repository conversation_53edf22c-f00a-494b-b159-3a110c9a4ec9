import React, {useState} from 'react';
import {Box, Icon, Input} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import constants from '../../utils/constants';

const SearchBar = props => {
	/**
	 * Start Initials
	 */
	const {navigation} = props;

	const [searchValue, setSearchValue] = useState(null);
	/**
	 * Start Initials
	 */

	/**
	 * Start Methods
	 */
	const handleKeyDown = () => {
		if (searchValue) {
			navigation.navigate(constants.ROUTE.SEARCH_DEALS, {
				search: searchValue,
			});
			setSearchValue('');
		}
	};

	const onChangeSearch = value => {
		setSearchValue(value);
	};
	/**
	 * End Methods
	 */

	return (
		<Box mt={1}>
			<Input
				size="xl"
				_focus={{borderBottomColor: 'white'}}
				placeholder={constants.LABEL.WHAT_CAN_WE_HELP_YOU_FIND}
				py={3}
				fontSize={14}
				value={searchValue}
				onChangeText={onChangeSearch}
				onSubmitEditing={handleKeyDown}
				returnKeyType="search"
				returnKeyLabel="search"
				InputLeftElement={
					<Icon
						as={MaterialCommunityIcons}
						name="magnify"
						size={6}
						ml={2}
						mr={2}
					/>
				}
			/>
		</Box>
	);
};

export default SearchBar;

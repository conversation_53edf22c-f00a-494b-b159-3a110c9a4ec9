import React, {useEffect, useState} from 'react';
import {getTopTypeDeals} from './dealsSlice';
import {useDispatch} from 'react-redux';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Button, Heading, HStack, Icon, VStack} from 'native-base';
import {useIsFocused} from '@react-navigation/native';

import constants from '../../utils/constants';
import DealSlider from '../../components/DealSlider';

const TrendingDealsSlider = props => {
	/**
	 * Start Initials
	 */
	const isFocused = useIsFocused();
	const {navigation, user} = props;

	const [trendingDeals, setTrendingDeals] = useState([]);

	const dispatch = useDispatch();
	/**
	 * Start Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		fetchTrendingDealsList();
	}, [props, isFocused]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchTrendingDealsList = () => {
		dispatch(getTopTypeDeals('trending'))
			.unwrap()
			.then(res => {
				setTrendingDeals(res);
			});
	};
	/**
	 * End Methods
	 */

	if (trendingDeals.length <= 0) {
		return null;
	}

	return (
		<VStack space={2} mt={4}>
			<HStack justifyContent="space-between" alignItems="center">
				<Heading fontSize={20} bold>
					{constants.TITLE.TRENDING}
				</Heading>
				<Button
					pr={0}
					variant="ghost"
					_text={{fontSize: 14, color: 'custom.orange'}}
					endIcon={
						<Icon
							as={MaterialCommunityIcons}
							name="chevron-right"
							color="custom.orange"
							size="md"
							ml={-1}
							mt={0.5}
						/>
					}
					onPress={() => navigation.navigate(constants.ROUTE.TRENDING_DEALS)}>
					{constants.BUTTON.VIEW_MORE}
				</Button>
			</HStack>
			{trendingDeals.length > 0 && (
				<DealSlider navigation={navigation} deals={trendingDeals} user={user} />
			)}
		</VStack>
	);
};

export default TrendingDealsSlider;

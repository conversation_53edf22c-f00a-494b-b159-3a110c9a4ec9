import React, {useState, useRef} from 'react';
import {PermissionsAndroid, StyleSheet, Platform} from 'react-native';
import {
	Actionsheet,
	Avatar,
	Box,
	Button,
	Center,
	FormControl,
	Icon,
	Input,
	Pressable,
	Text,
	useDisclose,
	useToast,
	VStack,
} from 'native-base';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {Controller, useForm} from 'react-hook-form';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch, useSelector} from 'react-redux';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import dayjs from 'dayjs';
import _ from 'lodash';
import TextInputMask from 'react-native-text-input-mask';
import {useEffect} from 'react';

import SpinnerOverlay from '../../components/SpinnerOverlay';
import {capitalizeName, toInitials, toUri} from '../../utils/helpers';
import constants, {APP_AGE_LIMIT} from '../../utils/constants';
import {updateProfile, uploadProfilePhoto} from './editProfileSlice';
import ToastAlert from '../../components/ToastAlert';

const EditProfile = () => {
	/**
	 * Start Initials
	 */
	const userData = useSelector(state => state.userSignIn.userData);

	const [loading, setLoading] = useState(false);
	const [photo, setPhoto] = useState(userData.profilePhoto);
	const [phone, setPhone] = useState(
		userData.phone ? userData.phone.toString() : '',
	);
	const [errorField, setErrorField] = useState('');
	const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
	const zipLength = userData?.zipCode?.toString()?.length;
	const {
		control,
		handleSubmit,
		setValue,
		getValues,
		formState: {errors},
	} = useForm({
		defaultValues: {
			name: userData.name,
			streetAddress: userData.streetAddress,
			city: userData.city,
			state: userData.state,
			country: userData.country,
			zipCode:
				zipLength == 1
					? `0000${userData?.zipCode}`
					: zipLength == 2
						? `000${userData?.zipCode}`
						: zipLength == 3
							? `00${userData.zipCode}`
							: zipLength == 4
								? `0${userData.zipCode}`
								: userData.zipCode,
			dob: userData.dob,
		},
	});

	const firstErrorFieldRef = useRef();

	const {isOpen, onOpen, onClose} = useDisclose();

	const dispatch = useDispatch();

	const toast = useToast();
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */

	useEffect(() => {
		// Focus on the first error field when errors change
		if (Object.keys(errors).length > 0) {
			setErrorField(Object.keys(errors)[0]);
			firstErrorFieldRef.current?.focus();
		}
	}, [errors, errorField]);

	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const uploadPhoto = file => {
		setLoading(true);

		const data = new FormData();
		data.append('photo', {
			uri: file.uri,
			type: file.type,
			size: file.fileSize,
			name: file.fileName,
		});

		dispatch(uploadProfilePhoto(data))
			.unwrap()
			.then(res => {
				setPhoto(res.profilePhoto);
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.PROFILE_UPDATE}
							/>
						);
					},
				});
				setLoading(false);
			})
			.catch(err => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={err.message}
							/>
						);
					},
				});
				setLoading(false);
			});
	};

	const onSubmit = data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}
		setLoading(true);

		const toIntNumber = Number(phone);
		if (toIntNumber.toString().length !== 10) {
			if (Platform.OS === 'android') {
				const id = 'toastid';
				if (!toast.isActive(id)) {
					toast.show({
						id,
						avoidKeyboard: true,
						render: ({id}) => {
							return (
								<ToastAlert
									id={id}
									toast={toast}
									type="error"
									message={constants.ERROR_MESSAGE.INVALID_PHONE}
								/>
							);
						},
					});
				}
				setLoading(false);
				return;
			} else {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={constants.ERROR_MESSAGE.INVALID_PHONE}
							/>
						);
					},
				});
				setLoading(false);
				return;
			}
		}

		data.name = capitalizeName(data.name);

		if (!data.dob) {
			toast.show({
				avoidKeyboard: true,
				render: ({id}) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={constants.ERROR_MESSAGE.DOB_REQUIRED}
						/>
					);
				},
			});
			setLoading(false);
			return;
		}

		dispatch(updateProfile({phone, emailVerified: true, ...data}))
			.unwrap()
			.then(() => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.PROFILE_UPDATE}
							/>
						);
					},
				});
				setLoading(false);
			})
			.catch(err => {
				toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={err.message}
							/>
						);
					},
				});
				setLoading(false);
			});
	};

	const debouncedSubmit = _.debounce(onSubmit, 1000, {
		leading: true,
		trailing: false,
	});

	const pickPhoto = async () => {
		try {
			launchImageLibrary({}).then(r => {
				if (r?.didCancel) {
					return;
				}
				if (r.assets.length > 0) uploadPhoto(r.assets[0]);
				onClose();
			});
		} catch (error) {
			alert('Cancelled');
		}
	};

	const takePhoto = async () => {
		if (Platform.OS === 'android') {
			try {
				const granted = await PermissionsAndroid.request(
					PermissionsAndroid.PERMISSIONS.CAMERA,
					{
						title: constants.VALIDATION_MESSAGES.CAMERA_PERMISSION,
						message: constants.VALIDATION_MESSAGES.CAMERA_PERMISSION_MESSAGE,
						buttonNeutral: constants.VALIDATION_MESSAGES.ASK_ME_LATER,
						buttonNegative: constants.VALIDATION_MESSAGES.CANCEL,
						buttonPositive: constants.VALIDATION_MESSAGES.OK,
					},
				);
				if (granted === PermissionsAndroid.RESULTS.GRANTED) {
					launchCamera({}).then(r => {
						if (r?.didCancel) {
							return;
						}
						if (!r?.errorCode) {
							uploadPhoto(r.assets[0]);
						} else {
							if (r.errorCode === 'camera_unavailable') {
								toast.show({
									avoidKeyboard: true,
									render: ({id}) => {
										return (
											<ToastAlert
												id={id}
												toast={toast}
												type="error"
												message={constants.ERROR_MESSAGE.CAMERA_NOT_AVAILABLE}
											/>
										);
									},
								});
							} else {
								toast.show({
									avoidKeyboard: true,
									render: ({id}) => {
										return (
											<ToastAlert
												id={id}
												toast={toast}
												type="error"
												message={
													r?.errorMessage
														? r.errorMessage
														: constants.ERROR_MESSAGE.SOMETHING_WRONG
												}
											/>
										);
									},
								});
							}
							setLoading(false);
						}
						onClose();
					});
				} else {
					alert('You have to enable camera permission');
				}
			} catch (err) {
				return toast.show({
					avoidKeyboard: true,
					render: ({id}) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={
									err?.errorMessage
										? err.errorMessage
										: constants.ERROR_MESSAGE.SOMETHING_WRONG
								}
							/>
						);
					},
				});
			}
		} else {
			launchCamera({}).then(r => {
				if (!r?.errorCode) {
					uploadPhoto(r.assets[0]);
				} else {
					if (r.errorCode === 'camera_unavailable') {
						toast.show({
							avoidKeyboard: true,
							render: ({id}) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={constants.ERROR_MESSAGE.CAMERA_NOT_AVAILABLE}
									/>
								);
							},
						});
					} else {
						toast.show({
							avoidKeyboard: true,
							render: ({id}) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={
											r?.errorMessage
												? r.errorMessage
												: constants.ERROR_MESSAGE.SOMETHING_WRONG
										}
									/>
								);
							},
						});
					}
					setLoading(false);
				}
				onClose();
			});
		}
	};

	const toggleDatePicker = () => {
		setDatePickerVisibility(prevState => !prevState);
	};

	const handleDateConfirm = date => {
		setValue('dob', dayjs(date).format('MM-DD-YYYY'));
		toggleDatePicker();
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Center w="100%">
					<Box w="90%" my={5}>
						<VStack>
							<Center>
								<Pressable onPress={onOpen}>
									<Avatar
										size="xl"
										bg="custom.lightOrange"
										borderWidth={2}
										borderColor="custom.grey1"
										_text={{
											color: 'custom.orange',
											fontSize: 20,
											fontWeight: Platform.select({ios: '700', android: 700}),
										}}
										// Conditionally set the source or fallback to icon if photo is unavailable
										source={photo ? {uri: toUri(photo)} : null}>
										{userData.name
											? toInitials(userData.name) // Show initials if name exists
											: !photo && ( // Show icon if no photo and no name
													<Icon
														as={MaterialCommunityIcons}
														name="account" // Default user icon
														size={8}
														color="custom.orange"
													/>
												)}

										{/* Avatar Badge for editing */}
										<Avatar.Badge
											bg="custom.green"
											borderColor="custom.green"
											size={5}>
											<Icon
												as={MaterialCommunityIcons}
												name="lead-pencil"
												size={4}
												color="white"
											/>
										</Avatar.Badge>
									</Avatar>
								</Pressable>
							</Center>

							<VStack space={4} mt={4}>
								<FormControl>
									<FormControl.Label>
										{constants.LABEL.NAME}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Controller
										control={control}
										name="name"
										rules={{
											required: {
												value: true,
												message: constants.VALIDATION_MESSAGES.NAME_REQUIRED,
											},
											pattern: {
												value: /^[A-Za-z0-9 _]*[A-Za-z0-9][A-Za-z0-9 _]*$/,
												message: 'Please enter valid name',
											},
											minLength: {
												value: 2,
												message: constants.VALIDATION_MESSAGES.NAME_MIN_LENGTH,
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												ref={errorField === 'name' ? firstErrorFieldRef : null}
												size="xl"
												autoCapitalize="none"
												autoCorrect={false}
												onBlur={onBlur}
												onChangeText={onChange}
												value={value}
											/>
										)}
									/>
									{errors.name && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.name.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.YOUR_EMAIL_ID}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Input size="xl" value={userData.email} isDisabled />
								</FormControl>

								<FormControl>
									<FormControl.Label _android={{mb: -1.5}}>
										{constants.LABEL.PHONE}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<TextInputMask
										keyboardType={
											Platform.OS === 'ios' ? 'number-pad' : 'numeric'
										}
										onChangeText={(formatted, extracted) => setPhone(extracted)}
										mask={'([000]) [000]-[0000]'}
										defaultValue={phone}
										style={styles.input}
									/>
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.DOB}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Input
										onFocus={toggleDatePicker}
										showSoftInputOnFocus={false}
										size="xl"
										value={getValues('dob')}
										InputRightElement={
											<Icon
												as={MaterialCommunityIcons}
												size={6}
												name="calendar"
												onPress={toggleDatePicker}
											/>
										}
									/>
									<DateTimePickerModal
										isVisible={isDatePickerVisible}
										mode="date"
										onConfirm={handleDateConfirm}
										onCancel={toggleDatePicker}
										maximumDate={
											new Date(dayjs().subtract(APP_AGE_LIMIT, 'year'))
										}
									/>
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.STREET_ADDRESS}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Controller
										control={control}
										name="streetAddress"
										rules={{
											required: {
												value: true,
												message:
													constants.VALIDATION_MESSAGES.STREET_ADDRESS_REQUIRED,
											},
											pattern: {
												value: constants.REGEX.STREET_ADDRESS,
												message:
													constants.VALIDATION_MESSAGES.VALID_STREET_ADDRESS,
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												autoCapitalize="none"
												autoCorrect={false}
												onBlur={onBlur}
												ref={
													errorField === 'streetAddress'
														? firstErrorFieldRef
														: null
												}
												onChangeText={onChange}
												value={value}
											/>
										)}
									/>
									{errors.streetAddress && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.streetAddress.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.CITY}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Controller
										control={control}
										name="city"
										rules={{
											required: {
												value: true,
												message: constants.VALIDATION_MESSAGES.CITY_REQUIRED,
											},
											pattern: {
												value: /^[a-zA-Z\s]*$/,
												message: 'Enter Valid city',
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												ref={errorField === 'city' ? firstErrorFieldRef : null}
												autoCapitalize="none"
												autoCorrect={false}
												focusable={errors.city}
												onBlur={onBlur}
												onChangeText={onChange}
												value={value}
											/>
										)}
									/>
									{errors.city && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.city.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.STATE}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Controller
										control={control}
										name="state"
										rules={{
											required: {
												value: true,
												message: constants.VALIDATION_MESSAGES.STATE_REQUIRED,
											},
											pattern: {
												value: /^[a-zA-Z\s]*$/,
												message: 'Enter Valid state',
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												autoCapitalize="none"
												ref={errorField === 'state' ? firstErrorFieldRef : null}
												autoCorrect={false}
												onBlur={onBlur}
												onChangeText={onChange}
												value={value}
											/>
										)}
									/>
									{errors.state && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.state.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.ZIP_CODE}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Controller
										control={control}
										name="zipCode"
										rules={{
											required: {
												value: true,
												message:
													constants.VALIDATION_MESSAGES.ZIP_CODE_REQUIRED,
											},
											maxLength: {
												value: 6,
												message:
													constants.VALIDATION_MESSAGES.ZIP_CODE_MAX_LENGTH,
											},
											minLength: {
												value: 5,
												message:
													constants.VALIDATION_MESSAGES.ZIP_CODE_MINLENGTH,
											},
											pattern: {
												value: constants.REGEX.PIN_CODE,
												message: constants.ERROR_MESSAGE.ZIP_CODE_VALIDATION,
											},
										}}
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												ref={
													errorField === 'zipCode' ? firstErrorFieldRef : null
												}
												autoCapitalize="none"
												keyboardType="number-pad"
												returnKeyType="done"
												returnKeyLabel="done"
												autoCorrect={false}
												onBlur={onBlur}
												onChangeText={onChange}
												value={value && value.toString()}
											/>
										)}
									/>
									{errors.zipCode && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.zipCode.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<FormControl>
									<FormControl.Label>
										{constants.LABEL.COUNTRY}
										<Text color="custom.red">*</Text>
									</FormControl.Label>
									<Controller
										rules={{
											required: {
												value: true,
												message: constants.VALIDATION_MESSAGES.NAME_REQUIRED,
											},
											pattern: {
												value: /^[A-Za-z0-9 _]*[A-Za-z0-9][A-Za-z0-9 _]*$/,
												message: 'Please enter valid name',
											},
										}}
										control={control}
										name="country"
										render={({field: {onChange, onBlur, value}}) => (
											<Input
												size="xl"
												autoCapitalize="none"
												ref={
													errorField === 'country' ? firstErrorFieldRef : null
												}
												autoCorrect={false}
												onBlur={onBlur}
												onChangeText={onChange}
												value={value}
											/>
										)}
									/>
									{errors.country && (
										<FormControl.HelperText _text={{color: 'custom.red'}}>
											{errors.country.message}
										</FormControl.HelperText>
									)}
								</FormControl>

								<Button
									mt={4}
									mb={4}
									onPress={handleSubmit(debouncedSubmit)}
									isDisabled={loading}>
									{constants.BUTTON.SAVE}
								</Button>
							</VStack>
						</VStack>
					</Box>
				</Center>
			</KeyboardAwareScrollView>
			<Actionsheet isOpen={isOpen} onClose={onClose} size="full">
				<Actionsheet.Content>
					<Actionsheet.Item
						startIcon={
							<Icon as={MaterialCommunityIcons} name="image" size="6" />
						}
						onPress={pickPhoto}>
						{constants.LABEL.SELECT_PHOTO}
					</Actionsheet.Item>
					<Actionsheet.Item
						startIcon={
							<Icon as={MaterialCommunityIcons} size="6" name="camera" />
						}
						onPress={takePhoto}>
						{constants.LABEL.TAKE_PHOTO}
					</Actionsheet.Item>
				</Actionsheet.Content>
			</Actionsheet>
			<SpinnerOverlay loading={loading} />
		</>
	);
};

export default EditProfile;

const styles = StyleSheet.create({
	input: {
		overflow: 'hidden',
		borderBottomWidth: 1,
		borderColor: constants.COLORS.GREY3,
		variant: 'underlined',
		color: constants.COLORS.DARK1,
		placeholderTextColor: 'grey',
		fontSize: 17,
		paddingBottom: 0,
	},
});

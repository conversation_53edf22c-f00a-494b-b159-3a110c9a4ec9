import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import constants from '../../utils/constants';
import apiCall from '../../services/apiClient';
import {setUserData} from '../SignIn/userSignInSlice';

export const updateProfile = createAsyncThunk(
	'editProfile/updateProfile',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.UPDATE_USER,
				method: 'PUT',
				data: data,
			},
			true,
		)
			.then(r => {
				thunkAPI.dispatch(setUserData(r.data.responseData));
				return r.data.responseData;
			})
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const uploadProfilePhoto = createAsyncThunk(
	'editProfile/uploadProfilePhoto',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.UPLOAD_PROFILE,
				method: 'POST',
				data: data,
			},
			true,
			true,
		)
			.then(r => {
				thunkAPI.dispatch(setUserData(r.data.responseData));
				return r.data.responseData;
			})
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const editProfileSlice = createSlice({
	name: 'editProfile',
	initialState: {},
});

export default editProfileSlice.reducer;

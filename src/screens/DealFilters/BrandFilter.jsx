import React, {useCallback, useState} from 'react';
import {Dimensions, Platform, TouchableOpacity} from 'react-native';

import {
	Center,
	FormControl,
	VStack,
	Input,
	Box,
	Icon,
	Text,
	FlatList,
	Spinner,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch, useSelector} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';

import {getBrands, applyBrand, fetchSelectedBrands} from './dealFilterSlice';
import DeviceInfo from 'react-native-device-info';

const {height: viewportHeight} = Dimensions.get('window');

const BrandFilter = props => {
	/**
	 * Start Initials
	 */
	const {isFilterClear, keyboardHeight, setIsFilterClear} = props;

	const [selectedBrands, setSelectedBrands] = useState([]);
	const [brands, setBrands] = useState([]);
	const [filteredBrands, setFilteredBrands] = useState([]);
	const [loading, setLoading] = useState(false);
	const [searchValue, setSearchValue] = useState(null);

	const dispatch = useDispatch();

	const getSelectedBrands = useSelector(fetchSelectedBrands);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			setSelectedBrands(getSelectedBrands);
			fetchBrands();
		}, []),
	);

	useFocusEffect(
		useCallback(() => {
			if (isFilterClear) setSelectedBrands(getSelectedBrands);
			setIsFilterClear(false);
		}, [isFilterClear]),
	);

	useFocusEffect(
		useCallback(() => {
			dispatch(applyBrand(selectedBrands));
		}, [selectedBrands]),
	);

	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchBrands = () => {
		setLoading(true);
		dispatch(getBrands())
			.unwrap()
			.then(res => {
				setBrands(res);
				setFilteredBrands(res);
				setLoading(false);
			})
			.catch(() => setLoading(false));
	};

	const onPressAction = item => {
		let selectedIds = [...selectedBrands];

		if (selectedIds.includes(item._id))
			selectedIds = selectedIds.filter(_id => _id !== item._id);
		else selectedIds.push(item._id);

		setSelectedBrands(selectedIds);
	};

	const renderRow = item => {
		return (
			<TouchableOpacity onPress={() => onPressAction(item)}>
				<Box px={1} py={2}>
					<Text
						color={
							selectedBrands.includes(item._id)
								? 'custom.orange'
								: 'custom.black'
						}
						fontSize={12}
						alignSelf="flex-start">
						{item.name}
					</Text>
				</Box>
			</TouchableOpacity>
		);
	};

	const handleSearch = value => {
		setSearchValue(value);
		const newData = brands.filter(
			item => item.name.toUpperCase().indexOf(value.toUpperCase()) > -1,
		);
		setFilteredBrands(newData);
	};
	/**
	 * End Methods
	 */

	return (
		<Center w="100%" justifyContent="flex-start">
			<Box
				w="90%"
				h={
					viewportHeight -
					(viewportHeight *
						Platform.select({
							ios: DeviceInfo.hasNotch() ? 39 : 40,
							android: 32.5,
						})) /
						100 -
					keyboardHeight
				}>
				<VStack>
					<FormControl>
						<Input
							size="xl"
							placeholder="Search"
							px={2}
							py={3}
							InputLeftElement={
								<Icon
									as={MaterialCommunityIcons}
									name="magnify"
									size={5}
									ml="2"
									color="custom.grey2"
								/>
							}
							returnKeyType="search"
							returnKeyLabel="search"
							value={searchValue}
							onChangeText={handleSearch}
						/>
					</FormControl>

					{loading ? (
						<Spinner color="custom.orange" />
					) : (
						<FlatList
							my={2}
							data={filteredBrands}
							renderItem={({item}) => renderRow(item)}
							keyExtractor={item => item.name}
						/>
					)}
				</VStack>
			</Box>
		</Center>
	);
};

export default BrandFilter;

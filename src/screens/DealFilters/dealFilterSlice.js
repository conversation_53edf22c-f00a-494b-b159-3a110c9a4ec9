import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import constants from '../../utils/constants';
import apiCall from '../../services/apiClient';

export const getBrands = createAsyncThunk(
	'dealFilter/getBrands',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_BRANDS,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

export const getSizes = createAsyncThunk(
	'dealFilter/getSizes',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_PRODUCT_SIZES,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

export const getOffers = createAsyncThunk(
	'dealFilter/getOffers',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_DEAL_OFFERS,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

export const getDiscounts = createAsyncThunk(
	'dealFilter/getDiscounts',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_DISCOUNTS,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

export const getPriceRange = createAsyncThunk(
	'dealFilter/getPriceRange',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_PRICE_RANGE,
				method: 'GET',
			},
			false,
		)
			.then(r => r.data.responseData)
			.catch(error => {
				return thunkAPI.rejectWithValue(error);
			});
	},
);

const dealFilterSlice = createSlice({
	name: 'dealFilter',
	initialState: {
		activeTab: 'Brand',
		filters: {
			brand: [],
			size: [],
			offers: [],
			discount: [],
			priceRange: [],
		},
		activeFilters: {
			brand: [],
			size: [],
			offers: [],
			discount: [],
			priceRange: [],
		},
	},
	reducers: {
		applyBrand(state, action) {
			state.filters.brand = [...action.payload];
		},
		applySize(state, action) {
			state.filters.size = [...action.payload];
		},
		applyOffers(state, action) {
			state.filters.offers = [...action.payload];
		},
		applyPriceRange(state, action) {
			state.filters.priceRange = [...action.payload];
		},
		applyDiscount(state, action) {
			state.filters.discount = [...action.payload];
		},
		setActiveTab(state, action) {
			state.activeTab = action.payload;
		},
		clearAllFilter(state) {
			state.filters = {
				brand: [],
				size: [],
				offers: [],
				discount: [],
				priceRange: [],
			};
			state.activeFilters = {
				brand: [],
				size: [],
				offers: [],
				discount: [],
				priceRange: [],
			};
		},
		applyFilters(state) {
			state.activeFilters = {...state.filters};
		},
		applyInitialFilters(state) {
			state.filters = {...state.activeFilters};
		},
	},
});

export const {
	applyBrand,
	applySize,
	applyOffers,
	applyDiscount,
	applyPriceRange,
	setActiveTab,
	clearAllFilter,
	applyFilters,
	applyInitialFilters,
} = dealFilterSlice.actions;

export const fetchSelectedBrands = state =>
	state.dealFilter.filters.brand ?? [];
export const fetchSelectedOffers = state =>
	state.dealFilter.filters.offers ?? [];
export const fetchSelectedDiscount = state =>
	state.dealFilter.filters.discount ?? [];
export const fetchSelectedPriceRange = state =>
	state.dealFilter.filters.priceRange ?? [];

export default dealFilterSlice.reducer;

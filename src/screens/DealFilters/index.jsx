import React, {useCallback, useEffect, useState} from 'react';
import {Box, Button, Center, HStack, IconButton, VStack} from 'native-base';
import {useDispatch, useSelector} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';
import {Keyboard} from 'react-native';

import constants from '../../utils/constants';
import VerticalTabs from './VerticalTabs';
import BrandFilter from './BrandFilter';
import SizeFilter from './SizeFilter';
import {
	applyFilters,
	applyInitialFilters,
	clearAllFilter,
	setActiveTab,
} from './dealFilterSlice';
import OffersFilter from './OffersFilter';
import PriceRangeFilter from './PriceRangeFilter';
import DiscountFilter from './DiscountFilter';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const DealFilters = ({navigation}) => {
	/**
	 * Start Initials
	 */
	const [isFilterClear, setIsFilterClear] = useState(false);
	const [keyboardHeight, setKeyboardHeight] = useState(0);

	const dispatch = useDispatch();

	const activeTab = useSelector(state => state.dealFilter.activeTab);
	/**
	 * End Initials
	 */

	useEffect(() => {
		const showSubscription = Keyboard.addListener('keyboardDidShow', e =>
			setKeyboardHeight(
				e.endCoordinates.height ? e.endCoordinates.height - 50 : 0,
			),
		);
		const hideSubscription = Keyboard.addListener('keyboardDidHide', e =>
			setKeyboardHeight(
				e.endCoordinates.height ? e.endCoordinates.height - 50 : 0,
			),
		);

		return () => {
			showSubscription.remove();
			hideSubscription.remove();
		};
	}, []);

	/**
	 * Start Methods
	 */
	const clearFilters = () => {
		dispatch(clearAllFilter());
		setIsFilterClear(true);
	};

	const onPressApplyFilters = () => {
		dispatch(applyFilters());
		navigation.goBack();
	};

	const clearMe = () => {
		dispatch(applyInitialFilters());
		dispatch(setActiveTab('Brand'));
		navigation.goBack();
	};
	/**
	 * End Methods
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			navigation.setOptions({
				headerRight: () => (
					<Button _text={{color: 'white', fontSize: 12}} onPress={clearFilters}>
						Clear All
					</Button>
				),
				headerLeft: () => (
					<IconButton
						variant="ghost"
						size="lg"
						onPress={clearMe}
						_icon={{
							as: MaterialCommunityIcons,
							name: 'chevron-left',
							color: 'white',
						}}
					/>
				),
			});
		}, [navigation]),
	);
	/**
	 * Start Lifecycle Methods
	 */

	return (
		<Center w="100%">
			<Box w="100%" mt={1}>
				<VStack>
					<HStack borderBottomWidth={1} borderColor="custom.grey2">
						<VerticalTabs
							activeTab={activeTab}
							keyboardHeight={keyboardHeight}
						/>
						<Box w="70%">
							{
								{
									Brand: (
										<BrandFilter
											isFilterClear={isFilterClear}
											setIsFilterClear={setIsFilterClear}
											keyboardHeight={keyboardHeight}
										/>
									),
									Size: (
										<SizeFilter
											isFilterClear={isFilterClear}
											setIsFilterClear={setIsFilterClear}
										/>
									),
									'Price Range': (
										<PriceRangeFilter
											isFilterClear={isFilterClear}
											setIsFilterClear={setIsFilterClear}
										/>
									),
									Offers: (
										<OffersFilter
											isFilterClear={isFilterClear}
											setIsFilterClear={setIsFilterClear}
										/>
									),
									Discounts: (
										<DiscountFilter
											isFilterClear={isFilterClear}
											setIsFilterClear={setIsFilterClear}
										/>
									),
								}[activeTab]
							}
						</Box>
					</HStack>

					<Box px={5} py={1} my={2}>
						<Button onPress={onPressApplyFilters}>
							{constants.BUTTON.APPLY_FILTERS}
						</Button>
					</Box>
				</VStack>
			</Box>
		</Center>
	);
};

export default DealFilters;

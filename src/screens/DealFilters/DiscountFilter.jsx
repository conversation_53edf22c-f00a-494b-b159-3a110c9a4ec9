import React, {useCallback, useState} from 'react';
import {Dimensions, TouchableOpacity} from 'react-native';

import {Center, VStack, Box, Text, FlatList, Spinner} from 'native-base';
import {useDispatch, useSelector} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';

import {
	getDiscounts,
	applyDiscount,
	fetchSelectedDiscount,
} from './dealFilterSlice';

const {height: viewportHeight} = Dimensions.get('window');

const DiscountFilter = props => {
	/**
	 * Start Initials
	 */
	const {isFilterClear, setIsFilterClear} = props;

	const [selectedDiscounts, setSelectedDiscounts] = useState([]);
	const [brands, setBrands] = useState([]);
	const [loading, setLoading] = useState(false);

	const dispatch = useDispatch();

	const getSelectedDiscount = useSelector(fetchSelectedDiscount);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			setSelectedDiscounts(getSelectedDiscount);
			fetchBrands();
		}, []),
	);

	useFocusEffect(
		useCallback(() => {
			if (isFilterClear) setSelectedDiscounts(getSelectedDiscount);
			setIsFilterClear(false);
		}, [isFilterClear]),
	);

	useFocusEffect(
		useCallback(() => {
			dispatch(applyDiscount(selectedDiscounts));
		}, [selectedDiscounts]),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchBrands = () => {
		setLoading(true);
		dispatch(getDiscounts())
			.unwrap()
			.then(res => {
				setBrands(res);
				setLoading(false);
			})
			.catch(() => setLoading(false));
	};

	const onPressAction = item => {
		let selectedIds = [...selectedDiscounts];

		if (selectedIds.includes(item.discount))
			selectedIds = selectedIds.filter(_id => _id !== item.discount);
		else selectedIds.push(item.discount);

		setSelectedDiscounts(selectedIds);
	};

	const renderRow = item => {
		return (
			<TouchableOpacity onPress={() => onPressAction(item)}>
				<Box px={1} py={2}>
					<Text
						color={
							selectedDiscounts.includes(item.discount)
								? 'custom.orange'
								: 'custom.black'
						}
						fontSize={12}
						alignSelf="flex-start">
						{item.discount}% OFF
					</Text>
				</Box>
			</TouchableOpacity>
		);
	};
	/**
	 * End Methods
	 */

	return (
		<Center w="100%" justifyContent="flex-start">
			<Box w="90%" h={viewportHeight - (viewportHeight * 35) / 100}>
				<VStack>
					{loading ? (
						<Spinner color="custom.orange" />
					) : (
						<FlatList
							my={2}
							data={brands}
							renderItem={({item}) => renderRow(item)}
							keyExtractor={item => item.discount}
						/>
					)}
				</VStack>
			</Box>
		</Center>
	);
};

export default DiscountFilter;

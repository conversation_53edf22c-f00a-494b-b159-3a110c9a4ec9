import React, {useEffect, useState} from 'react';
import {Dimensions} from 'react-native';

import {Center, VStack, Box, Text, Icon, Spinner} from 'native-base';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useDispatch, useSelector} from 'react-redux';

import constants from '../../utils/constants';
import {
	applyPriceRange,
	fetchSelectedPriceRange,
	getPriceRange,
} from './dealFilterSlice';

const {width: viewportWidth, height: viewportHeight} = Dimensions.get('window');

const PriceRangeFilter = props => {
	/**
	 * Start Initials
	 */
	const {isFilterClear, setIsFilterClear} = props;

	const [min, setMin] = React.useState(null);
	const [max, setMax] = React.useState(null);
	const [multiSliderValue, setMultiSliderValue] = React.useState([]);
	const [loading, setLoading] = useState(false);

	const dispatch = useDispatch();

	const getSelectedPriceRange = useSelector(fetchSelectedPriceRange);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		fetchPriceRange();
	}, []);

	useEffect(() => {
		if (isFilterClear)
			setMultiSliderValue(
				getSelectedPriceRange.length > 0 ? getSelectedPriceRange : [min, max],
			);
		setIsFilterClear(false);
	}, [isFilterClear]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const multiSliderValuesChange = values => {
		setMultiSliderValue(values);
		dispatch(applyPriceRange(values));
	};

	const fetchPriceRange = () => {
		setLoading(true);
		dispatch(getPriceRange())
			.unwrap()
			.then(res => {
				if (res) {
					setMin(res?.minimumPrice ?? 0);
					setMax(res?.maximumPrice ?? 0);
					setMultiSliderValue([res?.minimumPrice ?? 0, res?.maximumPrice ?? 0]);
					if (getSelectedPriceRange.length > 0) {
						setMultiSliderValue(getSelectedPriceRange);
					}
				}
				setLoading(false);
			})
			.catch(() => {
				setLoading(false);
			});
	};

	const customMarker = () => {
		return (
			<Icon
				as={MaterialCommunityIcons}
				name="circle-outline"
				backgroundColor="white"
				color="custom.orange"
				size="lg"
			/>
		);
	};
	/**
	 * End Methods
	 */

	return (
		<Center w="100%" justifyContent="flex-start">
			<Box
				my={2}
				w="90%"
				ml={2}
				h={viewportHeight - (viewportHeight * 36) / 100}>
				<VStack>
					<Text color="custom.grey2" fontSize={12} mb={2}>
						{constants.LABEL.SELECT_PRICE_RANGE}
					</Text>

					{loading ? (
						<Spinner color="custom.orange" />
					) : (
						min && (
							<>
								<Text color="custom.black" fontSize={12}>
									${multiSliderValue[0]} - ${multiSliderValue[1]}
								</Text>

								<MultiSlider
									selectedStyle={{
										backgroundColor: constants.COLORS.ORANGE,
									}}
									unselectedStyle={{
										backgroundColor: constants.COLORS.GREY2,
									}}
									values={[multiSliderValue[0], multiSliderValue[1]]}
									sliderLength={viewportWidth - 160}
									onValuesChangeFinish={multiSliderValuesChange}
									customMarker={customMarker}
									min={min}
									max={max}
									step={1}
									allowOverlap
									snapped
								/>
							</>
						)
					)}
				</VStack>
			</Box>
		</Center>
	);
};

export default PriceRangeFilter;

import React from 'react';
import {Box, Center, Text, Divider} from 'native-base';
import {Dimensions, TouchableOpacity, FlatList, Platform} from 'react-native';
import {useDispatch} from 'react-redux';
import {setActiveTab} from './dealFilterSlice';

const {width, height} = Dimensions.get('window');

const data = ['Brand', 'Size', 'Price Range', 'Offers', 'Discounts'];

const VerticalTabs = props => {
	/**
	 * Start Initials
	 */
	const {activeTab, keyboardHeight} = props;

	const dispatch = useDispatch();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const onPress = tab => {
		dispatch(setActiveTab(tab));
	};
	/**
	 * End Methods
	 */

	return (
		<Center
			h={
				height -
				(height * Platform.select({ios: 34, android: 26})) / 100 -
				keyboardHeight
			}
			w={width - width / 1.4}
			borderRightWidth={1}
			borderColor="custom.grey2">
			<FlatList
				data={data}
				renderItem={({item}) => (
					<Box w="100%">
						<TouchableOpacity
							style={{width: width - width / 1.4}}
							onPress={() => onPress(item)}>
							<Box p={2} w="100%" alignItems="center">
								<Text
									fontSize={14}
									color={activeTab === item ? 'custom.black' : 'custom.grey2'}>
									{item}
								</Text>
							</Box>
						</TouchableOpacity>
						<Divider />
					</Box>
				)}
				snapToAlignment="start"
				decelerationRate={'fast'}
				snapToInterval={Dimensions.get('window').height}
			/>
		</Center>
	);
};
export default VerticalTabs;

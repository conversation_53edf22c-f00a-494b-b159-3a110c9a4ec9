import React, {useCallback, useState} from 'react';
import {Dimensions, TouchableOpacity} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';
import {Center, VStack, Box, Text, FlatList, Spinner} from 'native-base';

import {applyOffers, fetchSelectedOffers, getOffers} from './dealFilterSlice';

const {height: viewportHeight} = Dimensions.get('window');

const OffersFilter = props => {
	/**
	 * Start Initials
	 */
	const {isFilterClear, setIsFilterClear} = props;

	const [selectedOffers, setSelectedOffers] = useState([]);
	const [offers, setOffers] = useState([]);
	const [loading, setLoading] = useState(false);

	const dispatch = useDispatch();

	const getSelectedOffers = useSelector(fetchSelectedOffers);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			setSelectedOffers(getSelectedOffers);
			fetchOffers();
		}, []),
	);

	useFocusEffect(
		useCallback(() => {
			if (isFilterClear) setSelectedOffers(getSelectedOffers);
			setIsFilterClear(false);
		}, [isFilterClear]),
	);

	useFocusEffect(
		useCallback(() => {
			dispatch(applyOffers(selectedOffers));
		}, [selectedOffers]),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchOffers = () => {
		setLoading(true);
		dispatch(getOffers())
			.unwrap()
			.then(res => {
				setOffers(res);
				setLoading(false);
			})
			.catch(() => {
				setLoading(false);
			});
	};

	const onPressAction = item => {
		let selectedIds = [...selectedOffers];

		if (selectedIds.includes(item.name))
			selectedIds = selectedIds.filter(_id => _id !== item.name);
		else selectedIds.push(item.name);

		setSelectedOffers(selectedIds);
	};

	const renderRow = item => {
		return (
			<TouchableOpacity onPress={() => onPressAction(item)}>
				<Box px={1} py={2}>
					<Text
						color={
							selectedOffers.includes(item.name)
								? 'custom.orange'
								: 'custom.black'
						}
						fontSize={12}
						alignSelf="flex-start">
						{item.name}
					</Text>
				</Box>
			</TouchableOpacity>
		);
	};
	/**
	 * End Methods
	 */

	return (
		<Center w="100%" justifyContent="flex-start">
			<Box w="90%" h={viewportHeight - (viewportHeight * 35) / 100}>
				<VStack>
					{loading ? (
						<Spinner color="custom.orange" />
					) : (
						<FlatList
							my={2}
							data={offers}
							renderItem={({item}) => renderRow(item)}
							keyExtractor={item => item.name}
						/>
					)}
				</VStack>
			</Box>
		</Center>
	);
};

export default OffersFilter;

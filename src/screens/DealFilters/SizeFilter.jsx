import React, {useCallback, useState} from 'react';
import {Center, VStack, Box, Text, FlatList, Spinner} from 'native-base';
import {Dimensions, TouchableOpacity} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';

import {applySize, getSizes} from './dealFilterSlice';
import {useFocusEffect} from '@react-navigation/native';

const {height: viewportHeight} = Dimensions.get('window');

const SizeFilter = props => {
	/**
	 * Start Initials
	 */
	const {isFilterClear, setIsFilterClear} = props;

	const [selectedSize, setSelectedSize] = useState([]);
	const [sizes, setSizes] = useState([]);
	const [loading, setLoading] = useState(false);

	const dispatch = useDispatch();

	const getSelectedSize = useSelector(state => state.dealFilter.filters.size);
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useFocusEffect(
		useCallback(() => {
			setSelectedSize(getSelectedSize);
			fetchSizes();
		}, []),
	);

	useFocusEffect(
		useCallback(() => {
			if (isFilterClear) setSelectedSize(getSelectedSize);
			setIsFilterClear(false);
		}, [isFilterClear]),
	);

	useFocusEffect(
		useCallback(() => {
			dispatch(applySize(selectedSize));
		}, [selectedSize]),
	);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start Methods
	 */
	const fetchSizes = () => {
		setLoading(true);
		dispatch(getSizes())
			.unwrap()
			.then(res => {
				setSizes(res);
				setLoading(false);
			})
			.catch(() => setLoading(false));
	};

	const onPressAction = item => {
		let selectedIds = [...selectedSize]; // clone state

		if (selectedIds.includes(item.size))
			selectedIds = selectedIds.filter(_id => _id !== item.size);
		else selectedIds.push(item.size);

		setSelectedSize(selectedIds);
	};

	const renderRow = item => {
		return (
			<TouchableOpacity onPress={() => onPressAction(item)}>
				<Box px={1} py={2}>
					<Text
						color={
							selectedSize.includes(item.size)
								? 'custom.orange'
								: 'custom.black'
						}
						fontSize={12}
						alignSelf="flex-start">
						{item.size} ml
					</Text>
				</Box>
			</TouchableOpacity>
		);
	};
	/**
	 * End Methods
	 */

	return (
		<Center w="100%" justifyContent="flex-start">
			<Box w="90%" h={viewportHeight - (viewportHeight * 35) / 100}>
				<VStack>
					{loading ? (
						<Spinner color="custom.orange" />
					) : (
						<FlatList
							my={2}
							data={sizes}
							renderItem={({item}) => renderRow(item)}
							keyExtractor={item => item.size}
						/>
					)}
				</VStack>
			</Box>
		</Center>
	);
};

export default SizeFilter;

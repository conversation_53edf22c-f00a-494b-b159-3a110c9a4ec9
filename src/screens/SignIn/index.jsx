import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import {
	Box,
	Text,
	Heading,
	VStack,
	FormControl,
	Input,
	Link,
	Button,
	HStack,
	Center,
	Pressable,
	Icon,
	useToast,
	Image,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useDispatch } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import DeviceInfo from 'react-native-device-info';

import constants from '../../utils/constants';
import { setIsGuestUser, signIn } from './userSignInSlice';
import ToastAlert from '../../components/ToastAlert';
import SpinnerOverlay from '../../components/SpinnerOverlay';
import SocialLoginButton from '../../components/SocialLoginButton';

const SignIn = ({ navigation }) => {
	/**
	 * Start Initials
	 */
	const {
		control,
		handleSubmit,
		formState: { errors },
	} = useForm({
		defaultValues: {
			email: '',
			password: '',
		},
	});
	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);

	const dispatch = useDispatch();

	const toast = useToast();
	/**
	 * End Initials
	 */

	/**
	 * Start Methods
	 */
	const onSubmit = data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}
		setLoading(true);

		dispatch(signIn(data))
			.unwrap()
			.then()
			.catch(err =>
				toast.show({
					avoidKeyboard: true,
					render: ({ id }) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={err?.message || err}
							/>
						);
					},
				}),
			)
			.finally(() => setLoading(false));
	};

	const signInAsGuest = () => {
		dispatch(setIsGuestUser(true));
	};
	/**
	 * End Methods
	 */

	return (
		<>
			<KeyboardAwareScrollView>
				<Box bg="custom.orange" style={styles.mainBox}>
					<Box style={styles.logoContainer}>
						<Image
							source={require('../../assets/images/new_logo.png')}
							style={{ width: 90, height: 90 }}
							alt="image"
						/>
					</Box>
					<Box style={styles.signInContainer}>
						<Center w="100%">
							<Box w="90%" mt={5}>
								<VStack justifyContent="space-between">
									<VStack>
										<Center>
											<Heading>{constants.TITLE.SIGN_IN}</Heading>
										</Center>
										<VStack space={7} mt="5">
											<FormControl>
												<FormControl.Label>
													{constants.LABEL.YOUR_EMAIL_ID}
												</FormControl.Label>
												<Controller
													control={control}
													name="email"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES.EMAIL_REQUIRED,
														},
														pattern: {
															value: constants.REGEX.EMAIL,
															message:
																constants.VALIDATION_MESSAGES.VALID_EMAIL,
														},
													}}
													render={({ field: { onChange, onBlur, value } }) => (
														<Input
															size="xl"
															autoCapitalize="none"
															keyboardType="email-address"
															autoCorrect={false}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
														/>
													)}
												/>
												{errors.email && (
													<FormControl.HelperText _text={{ color: 'custom.red' }}>
														{errors.email.message}
													</FormControl.HelperText>
												)}
											</FormControl>

											<FormControl>
												<FormControl.Label>
													{constants.LABEL.PASSWORD}
												</FormControl.Label>
												<Controller
													control={control}
													name="password"
													rules={{
														required: {
															value: true,
															message:
																constants.VALIDATION_MESSAGES.PASSWORD_REQUIRED,
														},
														minLength: {
															value: 6,
															message:
																constants.VALIDATION_MESSAGES.PASSWORD_LENGTH,
														},
													}}
													render={({ field: { onChange, onBlur, value } }) => (
														<Input
															size="xl"
															secureTextEntry={!showPassword}
															InputRightElement={
																<Pressable
																	onPress={() =>
																		setShowPassword(!showPassword)
																	}>
																	<Icon
																		as={MaterialCommunityIcons}
																		name={
																			!showPassword
																				? 'eye-outline'
																				: 'eye-off-outline'
																		}
																		size="lg"
																	/>
																</Pressable>
															}
															onBlur={onBlur}
															onChangeText={onChange}
															value={value}
														/>
													)}
												/>
												{errors.password && (
													<FormControl.HelperText _text={{ color: 'custom.red' }}>
														{errors.password.message}
													</FormControl.HelperText>
												)}
												<Link
													onPress={() =>
														navigation.navigate(constants.ROUTE.FORGOT_PASSWORD)
													}
													_text={{ color: 'custom.orange' }}
													alignSelf="flex-end"
													mt="2">
													{constants.LINK.FORGOT_PASSWORD}
												</Link>
											</FormControl>
											<Button mt="3" onPress={handleSubmit(onSubmit)}>
												{constants.BUTTON.SIGN_IN}
											</Button>
										</VStack>

										<VStack
											space={2}
											mt="5"
											justifyContent="center"
											alignItems="center">
											<Text>{constants.TEXT.OR + ' '}</Text>
											<Text>
												<Link onPress={signInAsGuest}>
													{constants.LINK.SIGN_IN_AS_GUEST}
												</Link>
											</Text>
										</VStack>

										<VStack space={5} mt="5" alignItems="center">
											<Text mb="1">{constants.TEXT.OR_SIGN_IN_WITH}</Text>
											<SocialLoginButton setLoading={setLoading} />
										</VStack>
									</VStack>

									<HStack my={6} justifyContent="center" alignItems="flex-end">
										<Text>{constants.TEXT.NOT_HAVE_AN_ACCOUNT}</Text>
										<Link
											onPress={() =>
												navigation.navigate(constants.ROUTE.SIGN_UP)
											}>
											{constants.LINK.CREATE_AN_ACCOUNT}
										</Link>
									</HStack>
								</VStack>
							</Box>
						</Center>
					</Box>
				</Box>
			</KeyboardAwareScrollView>
			<SpinnerOverlay loading={loading} />
		</>
	);
};

const styles = StyleSheet.create({
	mainBox: {
		flex: 1,
	},
	logoContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		marginTop: DeviceInfo.hasNotch() ? '15%' : '10%',
	},
	signInContainer: {
		flex: 2,
		borderTopLeftRadius: 20,
		borderTopRightRadius: 20,
		backgroundColor: 'white',
		marginTop: '5%',
	},
});

export default SignIn;

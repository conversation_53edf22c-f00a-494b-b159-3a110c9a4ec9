import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import DeviceInfo from 'react-native-device-info';
import {Platform} from 'react-native';
import {Toast} from 'native-base';
import {firebase} from '@react-native-firebase/auth';

import {
	firebaseAppleSignIn,
	firebaseFacebookSignIn,
	firebaseGoogleSignIn,
	firebaseSignIn,
	firebaseSignOut,
	signInWithFacebookCredentials,
	signInWithGoogleCredentials,
} from '../../services/firebaseAuth';
import apiCall from '../../services/apiClient';
import constants from '../../utils/constants';
import {setFavoriteDealIDs} from '../Favorite/favoriteDealSlice';
import UserModel from '../../models/userModel';
import BusinessModel from '../../models/businessModel';

export const signIn = createAsyncThunk(
	'user/signIn',
	async (data, thunkAPI) => {
		return await firebaseSignIn(data)
			.then(async res => {
				const user = await thunkAPI
					.dispatch(getUserData())
					.unwrap()
					.then(r => r);

				if (user) {
					if (!user.emailVerified) {
						thunkAPI.dispatch(verifyEmail());
					}

					thunkAPI.dispatch(addUpdateDevice());
					thunkAPI.dispatch(getFavoriteDealIDs());
				} else {
					Toast.show({
						avoidKeyboard: true,
						title: constants.ERROR_MESSAGE.ACCOUNT_NOT_FIND,
					});
				}

				return res;
			})
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const signOut = createAsyncThunk(
	'user/signOut',
	async (data, thunkAPI) => {
		return await firebaseSignOut(data)
			.then(res => res)
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const googleSignIn = createAsyncThunk(
	'user/googleSignIn',
	async (data, thunkAPI) => {
		return await firebaseGoogleSignIn()
			.then(async res => {
				const user = await thunkAPI
					.dispatch(checkUser(res.user.email))
					.unwrap()
					.then(r => r);
				if (user) {
					const userData = await signInWithGoogleCredentials(res.idToken);

					thunkAPI.dispatch(addUpdateDevice());
					thunkAPI.dispatch(getFavoriteDealIDs());

					return userData;
				} else {
					Toast.show({
						avoidKeyboard: true,
						title: constants.ERROR_MESSAGE.ACCOUNT_NOT_FIND,
					});
					return user;
				}
			})
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const addUpdateDevice = createAsyncThunk(
	'user/addUpdateDevice',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.ADD_UPDATE_DEVICE,
				method: 'POST',
				data: {
					deviceId: await DeviceInfo.getUniqueId(),
					deviceToken: await DeviceInfo.getDeviceToken(),
					deviceType: Platform.OS,
					osVersion: Platform.Version,
					deviceModel: DeviceInfo.getDeviceId(),
					deviceManufacturer: await DeviceInfo.getManufacturer(),
					deviceIP: await DeviceInfo.getIpAddress(),
				},
			},
			true,
		)
			.then(r => r)
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const verifyEmail = createAsyncThunk(
	'user/verifyEmail',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.VERIFY_EMAIL,
				method: 'PATCH',
			},
			true,
		)
			.then(r => r.data.responseData)
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const getFavoriteDealIDs = createAsyncThunk(
	'user/getFavoriteDealIDs',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_FAVORITE_DEAL_IDS,
				method: 'GET',
			},
			true,
		)
			.then(r => {
				thunkAPI.dispatch(setFavoriteDealIDs(r.data.responseData));

				return r.data.responseData;
			})
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const getUserData = createAsyncThunk(
	'user/getUserData',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_USER,
				method: 'GET',
			},
			true,
		)
			.then(async r => {
				const {user, business} = r.data.responseData;
				thunkAPI.dispatch(setUserData(user));
				if (business) {
					thunkAPI.dispatch(setBusinessData(business));
				}

				return user;
			})
			.catch(error => thunkAPI.rejectWithValue(error.data));
	},
);

export const checkUser = createAsyncThunk(
	'user/checkUser',
	async (data, thunkAPI) => {
		return await apiCall({
			url: constants.API_ROUTES.CHECK_USER + `/${data}`,
			method: 'GET',
		})
			.then(async r => {
				const {user, business} = r.data.responseData;
				thunkAPI.dispatch(setUserData(user));
				if (business) {
					thunkAPI.dispatch(setBusinessData(business));
				}

				return user;
			})
			.catch(error => thunkAPI.rejectWithValue(error.data));
	},
);

export const facebookSignIn = createAsyncThunk(
	'user/facebookSignIn',
	async (data, thunkAPI) => {
		return await firebaseFacebookSignIn()
			.then(async res => {
				const user = await thunkAPI
					.dispatch(checkUser(res.user.email))
					.unwrap()
					.then(r => r);
				if (user) {
					const userData = await signInWithFacebookCredentials(res.idToken);

					thunkAPI.dispatch(addUpdateDevice());
					thunkAPI.dispatch(getFavoriteDealIDs());

					return userData;
				} else {
					Toast.show({
						avoidKeyboard: true,
						title: constants.ERROR_MESSAGE.ACCOUNT_NOT_FIND,
					});
					return user;
				}
			})
			.catch(error =>
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

// Sign in with apple account check the user in database and login the user if exists.
export const appleSignIn = createAsyncThunk(
	'user/appleSignIn', // Action type for appleSignIn
	async (data, thunkAPI) => {
		// Start the async function for Apple sign-in
		return await firebaseAppleSignIn() // Call the firebaseAppleSignIn function
			.then(async res => {
				// Once the sign-in is successful, execute the following
				const user = await thunkAPI
					.dispatch(checkUser(res.user.email)) // Dispatch checkUser action with the user's email
					.unwrap() // Unwrap the result of the promise returned by checkUser
					.then(r => r); // Store the result in the user variable

				if (user) {
					// If the user exists, proceed with the following actions
					thunkAPI.dispatch(addUpdateDevice()); // Dispatch addUpdateDevice action to update or register the user's device
					thunkAPI.dispatch(getFavoriteDealIDs()); // Dispatch getFavoriteDealIDs to fetch the user's favorite deal IDs
					return user; // Return the user data
				} else {
					// If the user is not found, show an error toast message
					Toast.show({
						avoidKeyboard: true, // Avoid showing the toast over the keyboard
						title: constants.ERROR_MESSAGE.ACCOUNT_NOT_FIND, // Display the account not found error message
					});
					return user; // Return null or undefined user
				}
			})
			.catch(error =>
				// If there's an error during the sign-in process, reject with the error
				thunkAPI.rejectWithValue(error?.data ? error.data : error),
			);
	},
);

export const deleteAccount = createAsyncThunk(
	'user/deleteAccount',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.USER_DELETE,
				method: 'PATCH',
			},
			true,
		)
			.then(async () => thunkAPI.dispatch(signOut()))
			.catch(error => thunkAPI.rejectWithValue(error.data));
	},
);

const userSignInSlice = createSlice({
	name: 'userSignIn',
	initialState: {
		user: firebase.auth().currentUser,
		userData: UserModel,
		businessData: BusinessModel,
		isGuestUser: false,
	},
	reducers: {
		setUserData(state, action) {
			state.userData = {...action.payload};
		},
		setBusinessData(state, action) {
			state.businessData = {...action.payload};
		},
		setIsGuestUser(state, action) {
			state.isGuestUser = action.payload;
			state.userData = action.payload
				? {role: 'Customer', name: 'Guest User'}
				: UserModel;
		},
	},
});

export const {setUserData, setBusinessData, setIsGuestUser} =
	userSignInSlice.actions;

export default userSignInSlice.reducer;

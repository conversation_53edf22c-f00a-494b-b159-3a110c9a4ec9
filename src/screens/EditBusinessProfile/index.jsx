import React, { useState, useRef, useEffect } from 'react';
import { PermissionsAndroid, StyleSheet, Platform, Switch } from 'react-native';
import {
	Actionsheet,
	AspectRatio,
	Box,
	Button,
	Center,
	HStack,
	FormControl,
	Icon,
	Image,
	Input,
	Pressable,
	useDisclose,
	useToast,
	VStack,
	Text
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useDispatch, useSelector } from 'react-redux';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import { Controller, useForm } from 'react-hook-form';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import _ from 'lodash';
import TextInputMask from 'react-native-text-input-mask';
import { useCallback } from 'react';
import RNPickerSelect from 'react-native-picker-select';
import CheckBox from '@react-native-community/checkbox';

import constants, { dayNames, timeRange } from '../../utils/constants';
import { updateBusinessProfile } from './editBusinessProfileSlice';
import ToastAlert from '../../components/ToastAlert';
import { toUri } from '../../utils/helpers';
import SpinnerOverlay from '../../components/SpinnerOverlay';

const EditBusinessProfile = () => {
	/**
	 * Start Initials
	 */
	const businessData = useSelector(state => state.userSignIn.businessData);
	const zipLength = businessData?.zipCode?.toString()?.length;

	const [businessHourError, setBusinessHourError] = useState(false);
	const [loading, setLoading] = useState(false);
	const [photoFile, setPhotoFile] = useState(null);
	const [phone, setPhone] = useState(
		businessData.phone ? businessData.phone.toString() : '',
	);
	const [errorField, setErrorField] = useState('');
	const [businessHours, setBusinessHours] = useState({});
	const [photo, setPhoto] = useState(null);
	const [selectedServiceOptions, setSelectedServiceOptions] = useState([]);

	const firstErrorFieldRef = useRef();
	const dispatch = useDispatch();
	const toast = useToast();
	const { isOpen, onOpen, onClose } = useDisclose();

	const {
		control,
		handleSubmit,
		formState: { errors },
	} = useForm({
		defaultValues: {
			name: businessData.name,
			email: businessData.email,
			city: businessData.city,
			websiteUrl: businessData.websiteUrl || '',
			streetAddress: businessData.streetAddress,
			state: businessData.state,
			zipCode:
				zipLength == 1
					? `0000${businessData.zipCode}`
					: zipLength == 2
						? `000${businessData.zipCode}`
						: zipLength == 3
							? `00${businessData.zipCode}`
							: zipLength == 4
								? `0${businessData.zipCode}`
								: businessData.zipCode,
			country: 'USA',
			location: businessData.location,
			timing: '',
			businessLicense: businessData.businessLicense || '',
			photos: businessData.photos,
			description: businessData.description,
			serviceOptions: businessData.serviceOptions,
		},
	});
	/**
	 * End Initials
	 */

	/**
	 * Start Lifecycle Methods
	 */
	useEffect(() => {
		// Focus on the first error field when error change
		if (Object.keys(errors).length > 0) {
			setErrorField(Object.keys(errors)[0]);
			firstErrorFieldRef.current?.focus();
		}
	}, [errors, errorField]);

	useEffect(() => {
		if (businessData && businessData.timing) {
			setBusinessHours(parseBusinessHours(businessData.timing));
		} else {
			// Set default timing for Sunday
			setBusinessHours(prevHours => ({
				...prevHours,
				Sun: {
					opensAt: '11:00 AM',
					closesAt: '10:00 PM',
					closed: false
				}
			}));
		}

		if (businessData && businessData.serviceOptions)
			setSelectedServiceOptions(businessData.serviceOptions);

		if (businessData?.photos?.[0])
			setPhoto(businessData.photos[0] ? toUri(businessData.photos[0]) : null);
	}, [businessData]);
	/**
	 * End Lifecycle Methods
	 */

	/**
	 * Start testing timing logic
	 */
	const parseBusinessHours = timingString => {
		try {
			const parsedTiming = JSON.parse(timingString);

			if (typeof parsedTiming === 'object' && parsedTiming !== null) {
				return parsedTiming;
			} else {
				return {};
			}
		} catch (error) {
			return {};
		}
	};

	const handleCheckboxChange = useCallback(
		(day, checked) => {
			setBusinessHourError(false);
			setBusinessHours(prevHours => {
				if (!checked) {
					return {
						...prevHours,
						[day]: {
							...{},
							closed: true,
						},
					};
				} else {
					return {
						...prevHours,
						[day]: {
							closed: false
						},
					};
				}
			});
		},
		[businessHours],
	);

	const handleTimeChange = useCallback(
		(day, type, time) => {
			setBusinessHourError(false);
			setBusinessHours(prevHours => ({
				...prevHours,
				[day]: {
					...prevHours[day],
					[type]: time,
				},
			}));
		},
		[businessHours],
	);

	const handleCopyPreviousDay = (currentDay) => {
		const dayIndex = dayNames.indexOf(currentDay);
		if (dayIndex > 0) { // Skip Sunday (first day)
			const previousDay = dayNames[dayIndex - 1];
			if (businessHours[previousDay]) {
				setBusinessHours(prevHours => ({
					...prevHours,
					[currentDay]: {
						opensAt: businessHours[previousDay].opensAt,
						closesAt: businessHours[previousDay].closesAt,
						closed: businessHours[previousDay].closed
					}
				}));
			}
		}
	};
	/**
	 * End testing timing logic
	 */

	/**
	 * Start Methods
	 */
	const pickPhoto = () => {
		onClose();
		setLoading(true);
		launchImageLibrary({ noData: true, quality: 0.5 }).then(r => {
			if (r.assets?.length > 0) {
				setPhoto(r.assets[0]?.uri);
				setPhotoFile({
					uri: r.assets[0]?.uri,
					type: r.assets[0]?.type,
					size: r.assets[0]?.fileSize,
					name: r.assets[0]?.fileName,
				});
			} else {
				setLoading(false);
				return;
			}
			setLoading(false);
		});
	};

	const takePhoto = async () => {
		if (Platform.OS === 'android') {
			try {
				const granted = await PermissionsAndroid.request(
					PermissionsAndroid.PERMISSIONS.CAMERA,
					{
						title: 'App Camera Permission',
						message: 'App needs access to your camera ',
						buttonNeutral: 'Ask Me Later',
						buttonNegative: 'Cancel',
						buttonPositive: 'OK',
					},
				);
				if (granted === PermissionsAndroid.RESULTS.GRANTED) {
					onClose();
					setLoading(true);
					launchCamera({ noData: true, quality: 0.5 }).then(r => {
						if (!r?.errorCode) {
							if (r?.didCancel) {
								setLoading(false);
								return;
							}
							setPhoto(r?.assets[0]?.uri);
							setPhotoFile({
								uri: r.assets[0].uri,
								type: r.assets[0].type,
								size: r.assets[0].fileSize,
								name: r.assets[0].fileName,
							});
						} else {
							if (r.errorCode === 'camera_unavailable') {
								toast.show({
									avoidKeyboard: true,
									render: ({ id }) => {
										return (
											<ToastAlert
												id={id}
												toast={toast}
												type="error"
												message={constants.ERROR_MESSAGE.CAMERA_NOT_AVAILABLE}
											/>
										);
									},
								});
							} else {
								toast.show({
									avoidKeyboard: true,
									render: ({ id }) => {
										return (
											<ToastAlert
												id={id}
												toast={toast}
												type="error"
												message={
													r?.errorMessage
														? r.errorMessage
														: constants.ERROR_MESSAGE.SOMETHING_WRONG
												}
											/>
										);
									},
								});
							}
						}
						setLoading(false);
					});
				} else {
					alert('Camera permission denied');
				}
			} catch (err) {
				return err;
			}
		} else {
			onClose();
			setLoading(true);
			launchCamera({ noData: true, quality: 0.5 }).then(r => {
				if (!r?.errorCode) {
					setPhoto(r.assets[0].uri);
					setPhotoFile({
						uri: r.assets[0].uri,
						type: r.assets[0].type,
						size: r.assets[0].fileSize,
						name: r.assets[0].fileName,
					});
				} else {
					if (r.errorCode === 'camera_unavailable') {
						toast.show({
							avoidKeyboard: true,
							render: ({ id }) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={constants.ERROR_MESSAGE.CAMERA_NOT_AVAILABLE}
									/>
								);
							},
						});
					} else {
						toast.show({
							avoidKeyboard: true,
							render: ({ id }) => {
								return (
									<ToastAlert
										id={id}
										toast={toast}
										type="error"
										message={
											r?.errorMessage
												? r.errorMessage
												: constants.ERROR_MESSAGE.SOMETHING_WRONG
										}
									/>
								);
							},
						});
					}
				}
				setLoading(false);
			});
		}
	};

	const onSubmit = data => {
		if (loading) {
			setLoading(false);
			return; // If a submission is already in progress, don't submit again
		}
		setLoading(true);

		const toIntNumber = Number(phone);
		const businessDaysHours = Object.keys(businessHours).length;

		if (businessDaysHours !== 7) {
			setBusinessHourError(true);
			setLoading(false);
			return;
		}

		if (toIntNumber.toString().length !== 10) {
			toast.show({
				avoidKeyboard: true,
				render: ({ id }) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={constants.ERROR_MESSAGE.INVALID_PHONE}
						/>
					);
				},
			});
			setLoading(false);
			return;
		}

		if (!photo) {
			toast.show({
				avoidKeyboard: true,
				render: ({ id }) => {
					return (
						<ToastAlert
							id={id}
							toast={toast}
							type="error"
							message={constants.VALIDATION_MESSAGES.PHOTO_REQUIRED}
						/>
					);
				},
			});
			setLoading(false);
			return;
		}

		const formData = new FormData();

		delete data.location;
		delete data.user;
		delete data._id;
		delete data.status;
		delete data.verified;
		delete data.createdAt;
		delete data.updatedAt;
		delete data.timing;

		if (photoFile) formData.append('photo', photoFile);

		data.serviceOptions = selectedServiceOptions.join(',');

		for (const key in data) {
			if (key !== 'phone') {
				formData.append(key, data[key]);
			}
		}
		const businessTimings = JSON.stringify(businessHours);
		formData.append('phone', phone);
		formData.append('timing', businessTimings);

		dispatch(
			updateBusinessProfile({
				businessId: businessData._id,
				data: formData,
			}),
		)
			.unwrap()
			.then(() => {
				toast.show({
					avoidKeyboard: true,
					render: ({ id }) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="success"
								message={constants.SUCCESS_MESSAGE.BUSINESS_PROFILE_UPDATE}
							/>
						);
					},
				});
				setLoading(false);
			})
			.catch(err => {
				toast.show({
					avoidKeyboard: true,
					render: ({ id }) => {
						return (
							<ToastAlert
								id={id}
								toast={toast}
								type="error"
								message={err.message}
							/>
						);
					},
				});
				setLoading(false);
			});
	};

	const debouncedSubmit = _.debounce(onSubmit, 500, {
		leading: true,
		trailing: false,
	});

	const handleServiceOptionChange = option => {
		const index = selectedServiceOptions.indexOf(option);

		if (index === -1) {
			// If the option is not in the array, add it
			setSelectedServiceOptions([...selectedServiceOptions, option]);
		} else {
			// If the option is already in the array, remove it
			const newOptions = [...selectedServiceOptions];
			newOptions.splice(index, 1);
			setSelectedServiceOptions(newOptions);
		}
	};
	/**
	 * End Methods
	 */

	const renderSelect = (day, type) => {
		const timeItems = timeRange.map(item => ({ label: item, value: item }));
		return (
			<RNPickerSelect
				onValueChange={itemValue => handleTimeChange(day, type, itemValue)}
				value={businessHours?.[day]?.[type] || ''} // Use `value` for selected item
				placeholder={{
					label: type === 'opensAt' ? 'Opens at' : 'Closes at',
					value: null,
				}}
				useNativeAndroidPickerStyle={false} // Use custom styling for Android picker
				items={timeItems} // Ensure timeItems is an array of objects with label/value keys
				style={{
					inputIOS: {
						borderBottomColor: constants.COLORS.GREY4,
						borderBottomWidth: 1,
						padding: 1,
						marginTop: 5,
						width: 80,
						textAlign: 'center',
						color: !businessHours[day]?.closed
							? constants.COLORS.BLACK
							: constants.COLORS.GREY3,
					},
					inputAndroid: {
						borderBottomColor: constants.COLORS.GREY4,
						borderBottomWidth: 1,
						padding: 1,
						width: 80,
						textAlign: 'center',
						color: !businessHours[day]?.closed
							? constants.COLORS.BLACK
							: constants.COLORS.GREY3,
					},
					placeholder: {
						color: !businessHours[day]?.closed
							? constants.COLORS.BLACK
							: constants.COLORS.GREY3,
					},
				}}
				disabled={businessHours?.[day]?.closed} // Disabling when closed
				pickerProps={{
					accessibilityLabel: type === 'opensAt' ? 'Opens at' : 'Closes at',
				}}
			/>
		);
	};

	return (
		<>
			<KeyboardAwareScrollView>
				<Center w="100%">
					<Box w="90%" px={1}>
						<VStack space={3} my={5}>
							<FormControl>
								<FormControl.Label>
									{constants.LABEL.BUSINESS_NAME}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<Controller
									control={control}
									name="name"
									rules={{
										required: {
											value: true,
											message:
												constants.VALIDATION_MESSAGES.BUSINESS_NAME_REQUIRED,
										},
										pattern: {
											value: constants.REGEX.BUSINESS_NAME,
											message:
												constants.VALIDATION_MESSAGES.VALID_BUSINESS_NAME,
										},
									}}
									render={({ field: { onChange, onBlur, value } }) => (
										<Input
											size="xl"
											ref={errorField === 'name' ? firstErrorFieldRef : null}
											autoCapitalize="none"
											autoCorrect={false}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.name && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.name.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.LICENSE_NUMBER}
								</FormControl.Label>
								<Controller
									control={control}
									name="businessLicense"
									rules={{
										pattern: {
											value: /^[A-Za-z0-9 _]*[A-Za-z0-9][A-Za-z0-9 _]*$/,
											message: 'Please enter valid License Number',
										},
										minLength: {
											value: 5,
											message: 'License number must be at least 5 characters',
										},
									}}
									render={({ field: { onChange, onBlur, value } }) => (
										<Input
											size="xl"
											ref={
												errorField === 'businessLicense'
													? firstErrorFieldRef
													: null
											}
											autoCapitalize="none"
											autoCorrect={false}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.businessLicense && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.businessLicense.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl isDisabled>
								<FormControl.Label>
									{constants.LABEL.BUSINESS_EMAIL_ID}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<Controller
									control={control}
									name="email"
									rules={{
										required: {
											value: true,
											message:
												constants.VALIDATION_MESSAGES.BUSINESS_EMAIL_REQUIRED,
										},
										pattern: {
											value: constants.REGEX.EMAIL,
											message: constants.VALIDATION_MESSAGES.VALID_EMAIL,
										},
									}}
									render={({ field: { onChange, onBlur, value } }) => (
										<Input
											size="xl"
											keyboardType="email-address"
											autoCapitalize="none"
											ref={errorField === 'email' ? firstErrorFieldRef : null}
											autoCorrect={false}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.email && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.email.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label mb={-1}>
									{constants.LABEL.BUSINESS_PHONE_NUMBER}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<TextInputMask
									keyboardType={
										Platform.OS === 'ios' ? 'number-pad' : 'numeric'
									}
									onChangeText={(formatted, extracted) => setPhone(extracted)}
									mask={'([000]) [000]-[0000]'}
									defaultValue={phone}
									style={styles.input}
								/>
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.BUSINESS_PORTAL_URL}
								</FormControl.Label>
								<Controller
									control={control}
									name="websiteUrl"
									rules={{
										pattern: {
											// Simple regex: anything + dot + minimum 2 chars
											value: /^.+\.[a-z]{2,}$/i,
											message: constants.VALIDATION_MESSAGES.VALID_BUSINESS_PORTAL_URL,
										},
									}}
									render={({ field: { onChange, onBlur, value } }) => (
										<Input
											size="xl"
											keyboardType="url"
											ref={
												errorField === 'websiteUrl' ? firstErrorFieldRef : null
											}
											autoCapitalize="none"
											autoCorrect={false}
											onBlur={(e) => {
												// Format URL before saving
												if (value) {
													const formattedUrl = value.trim().toLowerCase();
													if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
														onChange(`https://${formattedUrl}`);
													}
												}
												onBlur(e);
											}}
											onChangeText={(text) => {
												// Remove http:// or https:// while typing and convert to lowercase
												const cleanUrl = text.replace(/^(https?:\/\/)/, '').toLowerCase();
												onChange(cleanUrl);
											}}
											value={value?.replace(/^(https?:\/\/)/, '')}
											placeholder="example.com"
										/>
									)}
								/>
								{errors.websiteUrl && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.websiteUrl.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.STREET_ADDRESS}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<Controller
									control={control}
									name="streetAddress"
									rules={{
										required: {
											value: true,
											message:
												constants.VALIDATION_MESSAGES.STREET_ADDRESS_REQUIRED,
										},
										pattern: {
											value: constants.REGEX.STREET_ADDRESS,
											message:
												constants.VALIDATION_MESSAGES.VALID_STREET_ADDRESS,
										},
									}}
									render={({ field: { onChange, onBlur, value } }) => (
										<Input
											size="xl"
											autoCapitalize="none"
											autoCorrect={false}
											ref={
												errorField === 'streetAddress'
													? firstErrorFieldRef
													: null
											}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.streetAddress && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.streetAddress.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.CITY}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<Controller
									control={control}
									name="city"
									rules={{
										required: {
											value: true,
											message: constants.VALIDATION_MESSAGES.CITY_REQUIRED,
										},
										pattern: {
											value: /^[a-zA-Z\s]*$/,
											message: 'Enter Valid city',
										},
									}}
									render={({ field: { onChange, onBlur, value } }) => (
										<Input
											size="xl"
											autoCapitalize="none"
											ref={errorField === 'city' ? firstErrorFieldRef : null}
											autoCorrect={false}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.city && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.city.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.STATE}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<Controller
									control={control}
									name="state"
									rules={{
										required: {
											value: true,
											message: constants.VALIDATION_MESSAGES.STATE_REQUIRED,
										},
										pattern: {
											value: /^[a-zA-Z\s]*$/,
											message: 'Enter Valid state',
										},
									}}
									render={({ field: { onChange, onBlur, value } }) => (
										<Input
											size="xl"
											autoCapitalize="none"
											ref={errorField === 'state' ? firstErrorFieldRef : null}
											autoCorrect={false}
											onBlur={onBlur}
											onChangeText={onChange}
											value={value}
										/>
									)}
								/>
								{errors.state && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.state.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.ZIP_CODE}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<Controller
									control={control}
									name="zipCode"
									rules={{
										required: {
											value: true,
											message: constants.VALIDATION_MESSAGES.ZIP_CODE_REQUIRED,
										},
										maxLength: {
											value: 6,
											message:
												constants.VALIDATION_MESSAGES.ZIP_CODE_MAX_LENGTH,
										},
										minLength: {
											value: 5,
											message: constants.VALIDATION_MESSAGES.ZIP_CODE_MINLENGTH,
										},
										pattern: {
											value: constants.REGEX.PIN_CODE,
											message: constants.ERROR_MESSAGE.ZIP_CODE_VALIDATION,
										},
									}}
									render={({ field: { onChange, onBlur, value } }) => (
										<Input
											size="xl"
											autoCapitalize="none"
											keyboardType="number-pad"
											autoCorrect={false}
											onBlur={onBlur}
											ref={errorField === 'zipCode' ? firstErrorFieldRef : null}
											onChangeText={onChange}
											value={value && value.toString()}
											returnKeyType="done"
											returnKeyLabel="done"
										/>
									)}
								/>
								{errors.zipCode && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.zipCode.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.COUNTRY}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<Controller
									control={control}
									name="country"
									ref={errorField === 'country' ? firstErrorFieldRef : null}
									render={({ field: { onChange, onBlur } }) => (
										<Input
											size="xl"
											autoCapitalize="none"
											isDisabled
											autoCorrect={false}
											onBlur={onBlur}
											onChangeText={onChange}
											value={'USA'}
										/>
									)}
								/>
								{errors.country && (
									<FormControl.HelperText _text={{ color: 'custom.red' }}>
										{errors.country.message}
									</FormControl.HelperText>
								)}
							</FormControl>

							<Box>
								<FormControl.Label>
									{constants.LABEL.STORE_TIMING}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								{dayNames.map((day, key) => (
									<HStack
										key={key}
										mt={3}
										justifyContent="space-between"
										alignItems='center'
									>
										<Switch
											mt={-1}
											trackColor={{ false: constants.COLORS.GREY2, true: constants.COLORS.ORANGE }}
											size="sm"
											value={!businessHours[day]?.closed}
											onValueChange={value => handleCheckboxChange(day, value)}
											thumbColor={!businessHours[day]?.closed ? constants.COLORS.LIGHT_ORANGE : 'white'}
										/>
										<Text w="40px" fontWeight='600'>
											{day}
										</Text>
										{renderSelect(day, 'opensAt')}
										{renderSelect(day, 'closesAt')}
										<VStack>
											<Pressable
												mt={1}
												display='flex'
												alignItems='center'
												onPress={() => handleCopyPreviousDay(day)}
												_pressed={{
													opacity: 0.6
												}}
											>
												<Icon
													as={MaterialCommunityIcons}
													name="content-duplicate"
													size={5}
													color={constants.COLORS.ORANGE}
												/>
											</Pressable>

										</VStack>

									</HStack>
								))}
								{businessHourError && (
									<Text fontSize={12} color="custom.red">
										{
											constants.VALIDATION_MESSAGES
												.ALL_BUSINESS_DAYS_TIME_REQUIRED
										}
									</Text>
								)}
							</Box>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.SERVICE_OPTIONS}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<VStack justifyContent="center">
									<HStack alignItems="center">
										<CheckBox
											boxType="square"
											animationDuration={0}
											onAnimationType="flat"
											onFillColor={constants.COLORS.ORANGE}
											onTintColor={constants.COLORS.ORANGE}
											tintColors={{
												true: constants.COLORS.ORANGE,
												false: 'gray',
											}}
											onCheckColor="white"
											style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
											value={selectedServiceOptions.includes(
												'In-store Shopping',
											)}
											onValueChange={() =>
												handleServiceOptionChange('In-store Shopping')
											}
										/>
										<Text>In-store Shopping</Text>
									</HStack>
									<HStack alignItems="center">
										<CheckBox
											boxType="square"
											animationDuration={0}
											onAnimationType="flat"
											onFillColor={constants.COLORS.ORANGE}
											onTintColor={constants.COLORS.ORANGE}
											tintColors={{
												true: constants.COLORS.ORANGE,
												false: 'gray',
											}}
											onCheckColor="white"
											style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
											value={selectedServiceOptions.includes(
												'In-store Pick-up',
											)}
											onValueChange={() =>
												handleServiceOptionChange('In-store Pick-up')
											}
										/>
										<Text>In-store Pick-up</Text>
									</HStack>
									<HStack alignItems="center">
										<CheckBox
											boxType="square"
											animationDuration={0}
											onAnimationType="flat"
											tintColors={{
												true: constants.COLORS.ORANGE,
												false: 'gray',
											}}
											onFillColor={constants.COLORS.ORANGE}
											onTintColor={constants.COLORS.ORANGE}
											onCheckColor="white"
											style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
											value={selectedServiceOptions.includes('Delivery')}
											onValueChange={() =>
												handleServiceOptionChange('Delivery')
											}
										/>
										<Text>Delivery</Text>
									</HStack>
								</VStack>
							</FormControl>

							<FormControl>
								<FormControl.Label>
									{constants.LABEL.UPLOAD_BUSINESS_PICTURE}
									<Text color="custom.red">*</Text>
								</FormControl.Label>
								<Pressable onPress={onOpen}>
									<Box borderColor="custom.grey2" borderWidth="1">
										<AspectRatio w="100%" ratio={16 / 9}>
											<Image source={{ uri: photo }} alt="image" />
										</AspectRatio>
										{photo ? (
											<Center
												bg="custom.orange"
												position="absolute"
												top={0}
												right={0}
												px={3}
												py={1.5}>
												<Icon
													as={MaterialCommunityIcons}
													name="lead-pencil"
													size={4}
													color="white"
												/>
											</Center>
										) : (
											<Center position="absolute" top="35%" left="42%">
												<Icon
													as={MaterialCommunityIcons}
													name="plus"
													size={12}
													color="custom.grey1"
												/>
											</Center>
										)}
									</Box>
								</Pressable>
							</FormControl>

							<Button
								mt="3"
								onPress={handleSubmit(debouncedSubmit)}
								isDisabled={loading}>
								{constants.BUTTON.SAVE}
							</Button>
						</VStack>
					</Box>
				</Center>
			</KeyboardAwareScrollView>
			<Actionsheet isOpen={isOpen} onClose={onClose} size="full">
				<Actionsheet.Content>
					<Actionsheet.Item
						startIcon={
							<Icon as={MaterialCommunityIcons} name="image" size="6" />
						}
						onPress={pickPhoto}>
						{constants.LABEL.SELECT_PHOTO}
					</Actionsheet.Item>
					<Actionsheet.Item
						startIcon={
							<Icon as={MaterialCommunityIcons} size="6" name="camera" />
						}
						onPress={takePhoto}>
						{constants.LABEL.TAKE_PHOTO}
					</Actionsheet.Item>
				</Actionsheet.Content>
			</Actionsheet>

			<SpinnerOverlay loading={loading} />
		</>
	);
};

export default EditBusinessProfile;

const styles = StyleSheet.create({
	input: {
		px: 3,
		overflow: 'hidden',
		borderBottomWidth: 1,
		borderColor: constants.COLORS.GREY3,
		variant: 'underlined',
		color: constants.COLORS.DARK1,
		placeholderTextColor: 'grey',
		fontSize: 17,
		paddingBottom: 0,
		paddingTop: 10,
	},
	checkbox: {
		width: 14,
		height: 14,
		fontSize: 12,
	},
});

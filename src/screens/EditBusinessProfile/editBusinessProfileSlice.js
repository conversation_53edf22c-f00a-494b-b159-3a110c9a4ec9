import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import constants from '../../utils/constants';
import apiCall from '../../services/apiClient';
import {setBusinessData} from '../SignIn/userSignInSlice';

export const getBusiness = createAsyncThunk(
	'editBusinessProfile/getBusiness',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.GET_BUSINESS,
				method: 'GET',
			},
			true,
		)
			.then(r => {
				r.data.responseData;
			})
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

export const updateBusinessProfile = createAsyncThunk(
	'editBusinessProfile/updateBusinessProfile',
	async (data, thunkAPI) => {
		return await apiCall(
			{
				url: constants.API_ROUTES.UPDATE_BUSINESS + `/${data.businessId}`,
				method: 'PUT',
				data: data.data,
			},
			true,
			true,
		)
			.then(async r => {
				await thunkAPI.dispatch(setBusinessData(r.data.responseData));
				return r.data.responseData;
			})
			.catch(error => {
				return thunkAPI.rejectWithValue(error.data);
			});
	},
);

const editBusinessProfileSlice = createSlice({
	name: 'editBusinessProfile',
	initialState: {},
});

export default editBusinessProfileSlice.reducer;

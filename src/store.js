import {combineReducers, configureStore} from '@reduxjs/toolkit';
import {persistReducer, persistStore} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

import userSignInSlice from './screens/SignIn/userSignInSlice';
import userSignUpSlice from './screens/SignUp/userSignUpSlice';
import forgotPasswordSlice from './screens/ForgotPassword/forgotPasswordSlice';
import dealsSlice from './screens/Deals/dealsSlice';
import editProfileSlice from './screens/EditProfile/editProfileSlice';
import categoryDealSlice from './screens/CategoryDeal/categoryDealSlice';
import favoriteDealSlice from './screens/Favorite/favoriteDealSlice';
import shopDetailSlice from './screens/ShopDetail/shopDetailSlice';
import dealFilterSlice from './screens/DealFilters/dealFilterSlice';
import notificationSlice from './screens/Notifications/notificationSlice';
import searchDealsSlice from './screens/SearchDeal/searchDealsSlice';
import userProfileSlice from './screens/Profile/userProfileSlice';
import dealDetailSlice from './screens/DealDetail/dealDetailSlice';
import editBusinessProfileSlice from './screens/EditBusinessProfile/editBusinessProfileSlice';
import requestProductSlice from './screens/RequestAddProduct/requestAddProductSlice';
import myDealsSlice from './screens/MyDeals/myDealsSlice';
import myTemplatesSlice from './screens/MyTemplates/myTemplatesSlice';
import createDealSlice from './screens/CreateDeal/createDealSlice';
import myDealDetailSlice from './screens/MyDealDetail/myDealDetailSlice';
import editDealSlice from './screens/EditDeal/editDealSlice';
import editTemplateSlice from './screens/EditTemplate/editTemplateSlice';
import addAddressSlice from './screens/AddAddress/addAddressSlice';
import ageVerificationSlice from './screens/AgeVerification/ageVerificationSlice';

const persistConfig = {
	key: 'root',
	whitelist: [
		'userSignIn',
		'userSignUp',
		'favoriteDeal',
		'ageVerification',
		'addAddress',
	],
	storage: AsyncStorage,
};

const rootReducer = combineReducers({
	userSignIn: userSignInSlice,
	userSignUp: userSignUpSlice,
	forgotPassword: forgotPasswordSlice,
	deals: dealsSlice,
	editProfile: editProfileSlice,
	dealDetail: dealDetailSlice,
	categoryDeal: categoryDealSlice,
	favoriteDeal: favoriteDealSlice,
	shopDetail: shopDetailSlice,
	dealFilter: dealFilterSlice,
	notification: notificationSlice,
	searchDeals: searchDealsSlice,
	userProfile: userProfileSlice,
	editBusinessProfile: editBusinessProfileSlice,
	requestAddProduct: requestProductSlice,
	myDeals: myDealsSlice,
	myTemplates: myTemplatesSlice,
	createDeal: createDealSlice,
	myDealDetail: myDealDetailSlice,
	editDeal: editDealSlice,
	editTemplate: editTemplateSlice,
	addAddress: addAddressSlice,
	ageVerification: ageVerificationSlice,
});

const appReducer = (state, action) => {
	if (action.type === 'user/signOut/fulfilled') {
		return rootReducer(
			state?.ageVerification
				? {ageVerification: state.ageVerification}
				: undefined,
			action,
		);
	}
	return rootReducer(state, action);
};

const persistedReducer = persistReducer(persistConfig, appReducer);

export const store = configureStore({
	reducer: persistedReducer,
	middleware: getDefaultMiddleware =>
		getDefaultMiddleware({
			serializableCheck: false,
		}),
});

export const persistedStore = persistStore(store);

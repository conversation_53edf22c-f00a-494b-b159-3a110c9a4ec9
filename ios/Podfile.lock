PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - BEMCheckBox (1.4.1)
  - boost (1.76.0)
  - DoubleConversion (1.1.6)
  - FBAEMKit (15.0.0):
    - FBSDKCoreKit_Basics (= 15.0.0)
  - FBLazyVector (0.70.5)
  - FBReactNativeSpec (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.5)
    - RCTTypeSafety (= 0.70.5)
    - React-Core (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - FBSDKCoreKit (15.0.0):
    - FBAEMKit (= 15.0.0)
    - FBSDKCoreKit_Basics (= 15.0.0)
  - FBSDKCoreKit_Basics (15.0.0)
  - FBSDKGamingServicesKit (15.0.0):
    - FBSDKCoreKit (= 15.0.0)
    - FBSDKCoreKit_Basics (= 15.0.0)
    - FBSDKShareKit (= 15.0.0)
  - FBSDKLoginKit (15.0.0):
    - FBSDKCoreKit (= 15.0.0)
  - FBSDKShareKit (15.0.0):
    - FBSDKCoreKit (= 15.0.0)
  - Firebase/Auth (10.7.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.7.0)
  - Firebase/CoreOnly (10.7.0):
    - FirebaseCore (= 10.7.0)
  - FirebaseAuth (10.7.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseCore (10.7.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.2.2):
    - Google-Maps-iOS-Utils/Clustering (= 4.2.2)
    - Google-Maps-iOS-Utils/Geometry (= 4.2.2)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.2.2)
    - Google-Maps-iOS-Utils/Heatmap (= 4.2.2)
    - Google-Maps-iOS-Utils/QuadTree (= 4.2.2)
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Clustering (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Geometry (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/GeometryUtils (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Heatmap (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/QuadTree (4.2.2):
    - GoogleMaps (~> 7.3)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleSignIn (6.2.4):
    - AppAuth (~> 1.5)
    - GTMAppAuth (~> 1.3)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (1.3.1):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 3.0, >= 1.5)
  - GTMSessionFetcher/Core (2.3.0)
  - hermes-engine (0.70.5)
  - InputMask (6.1.0)
  - libevent (2.1.12)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.70.5)
  - RCTTypeSafety (0.70.5):
    - FBLazyVector (= 0.70.5)
    - RCTRequired (= 0.70.5)
    - React-Core (= 0.70.5)
  - React (0.70.5):
    - React-Core (= 0.70.5)
    - React-Core/DevSupport (= 0.70.5)
    - React-Core/RCTWebSocket (= 0.70.5)
    - React-RCTActionSheet (= 0.70.5)
    - React-RCTAnimation (= 0.70.5)
    - React-RCTBlob (= 0.70.5)
    - React-RCTImage (= 0.70.5)
    - React-RCTLinking (= 0.70.5)
    - React-RCTNetwork (= 0.70.5)
    - React-RCTSettings (= 0.70.5)
    - React-RCTText (= 0.70.5)
    - React-RCTVibration (= 0.70.5)
  - React-bridging (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi (= 0.70.5)
  - React-callinvoker (0.70.5)
  - React-Codegen (0.70.5):
    - FBReactNativeSpec (= 0.70.5)
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.5)
    - RCTTypeSafety (= 0.70.5)
    - React-Core (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-Core (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.5)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/CoreModulesHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/Default (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/DevSupport (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.5)
    - React-Core/RCTWebSocket (= 0.70.5)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-jsinspector (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTBlobHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTImageHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTTextHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-Core/RCTWebSocket (0.70.5):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.5)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - Yoga
  - React-CoreModules (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/CoreModulesHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-RCTImage (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-cxxreact (0.70.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsinspector (= 0.70.5)
    - React-logger (= 0.70.5)
    - React-perflogger (= 0.70.5)
    - React-runtimeexecutor (= 0.70.5)
  - React-hermes (0.70.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-jsiexecutor (= 0.70.5)
    - React-jsinspector (= 0.70.5)
    - React-perflogger (= 0.70.5)
  - React-jsi (0.70.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi/Default (= 0.70.5)
  - React-jsi/Default (0.70.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.70.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-perflogger (= 0.70.5)
  - React-jsinspector (0.70.5)
  - React-logger (0.70.5):
    - glog
  - react-native-fbsdk-next (11.3.0):
    - React-Core
    - react-native-fbsdk-next/Core (= 11.3.0)
    - react-native-fbsdk-next/Login (= 11.3.0)
    - react-native-fbsdk-next/Share (= 11.3.0)
  - react-native-fbsdk-next/Core (11.3.0):
    - FBSDKCoreKit (~> 15.0.0)
    - React-Core
  - react-native-fbsdk-next/Login (11.3.0):
    - FBSDKLoginKit (~> 15.0.0)
    - React-Core
  - react-native-fbsdk-next/Share (11.3.0):
    - FBSDKGamingServicesKit (~> 15.0.0)
    - FBSDKShareKit (~> 15.0.0)
    - React-Core
  - react-native-geolocation (3.1.0):
    - React-Core
  - react-native-get-random-values (1.10.0):
    - React-Core
  - react-native-google-maps (1.8.3):
    - Google-Maps-iOS-Utils (= 4.2.2)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-image-picker (4.10.3):
    - React-Core
  - react-native-maps (1.8.3):
    - React-Core
  - react-native-pager-view (6.2.3):
    - React-Core
  - react-native-safe-area-context (4.8.0):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-text-input-mask (3.2.0):
    - InputMask (~> 6.1.0)
    - React-Core
    - React-RCTText
  - React-perflogger (0.70.5)
  - React-RCTActionSheet (0.70.5):
    - React-Core/RCTActionSheetHeaders (= 0.70.5)
  - React-RCTAnimation (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTAnimationHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTBlob (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTBlobHeaders (= 0.70.5)
    - React-Core/RCTWebSocket (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-RCTNetwork (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTImage (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTImageHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-RCTNetwork (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTLinking (0.70.5):
    - React-Codegen (= 0.70.5)
    - React-Core/RCTLinkingHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTNetwork (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTNetworkHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTSettings (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.5)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTSettingsHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-RCTText (0.70.5):
    - React-Core/RCTTextHeaders (= 0.70.5)
  - React-RCTVibration (0.70.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.5)
    - React-Core/RCTVibrationHeaders (= 0.70.5)
    - React-jsi (= 0.70.5)
    - ReactCommon/turbomodule/core (= 0.70.5)
  - React-runtimeexecutor (0.70.5):
    - React-jsi (= 0.70.5)
  - ReactCommon/turbomodule/core (0.70.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-bridging (= 0.70.5)
    - React-callinvoker (= 0.70.5)
    - React-Core (= 0.70.5)
    - React-cxxreact (= 0.70.5)
    - React-jsi (= 0.70.5)
    - React-logger (= 0.70.5)
    - React-perflogger (= 0.70.5)
  - RNAppleAuthentication (2.4.0):
    - React-Core
  - RNCAsyncStorage (1.21.0):
    - React-Core
  - RNCCheckbox (0.5.17):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNCPicker (2.6.1):
    - React-Core
  - RNDateTimePicker (7.6.2):
    - React-Core
  - RNDeviceInfo (10.12.0):
    - React-Core
  - RNFBApp (16.7.0):
    - Firebase/CoreOnly (= 10.7.0)
    - React-Core
  - RNFBAuth (16.7.0):
    - Firebase/Auth (= 10.7.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.14.0):
    - React-Core
  - RNGoogleSignin (8.2.2):
    - GoogleSignIn (~> 6.2)
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.29.0):
    - React-Core
    - React-RCTImage
  - RNSVG (13.14.0):
    - React-Core
  - RNVectorIcons (9.2.0):
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Maps-iOS-Utils (~> 4.1)
  - GoogleMaps (~> 7.0)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-bridging (from `../node_modules/react-native/ReactCommon`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-fbsdk-next (from `../node_modules/react-native-fbsdk-next`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - react-native-google-maps (from `../node_modules/react-native-maps`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-text-input-mask (from `../node_modules/react-native-text-input-mask`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppAuth
    - BEMCheckBox
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKGamingServicesKit
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - InputMask
    - libevent
    - PromisesObjC

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-bridging:
    :path: "../node_modules/react-native/ReactCommon"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-fbsdk-next:
    :path: "../node_modules/react-native-fbsdk-next"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-google-maps:
    :path: "../node_modules/react-native-maps"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-text-input-mask:
    :path: "../node_modules/react-native-text-input-mask"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: e371a6a63a61dc3c8f0f481ba0b8a22a8e93640c
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBAEMKit: d8312d8451ead46282adc7f3565ffc4965e3a4a7
  FBLazyVector: affa4ba1bfdaac110a789192f4d452b053a86624
  FBReactNativeSpec: fe8b5f1429cfe83a8d72dc8ed61dc7704cac8745
  FBSDKCoreKit: 81879058dd06208c0820fc713d054ca54903e8ba
  FBSDKCoreKit_Basics: eebc9bb69a0e5d133b92dca53a20716227faa454
  FBSDKGamingServicesKit: e0766514d64d26a9f846e793c0a44bc4a236723b
  FBSDKLoginKit: 11995469cd7da16fd4d5245e8d2ec61060479270
  FBSDKShareKit: 9501792c3024580809f811a8a549868699d9bd32
  Firebase: 0219acf760880eeec8ce479895bd7767466d9f81
  FirebaseAuth: dd64c01631df724b09f33e584625775c52f7d71f
  FirebaseCore: e317665b9d744727a97e623edbbed009320afdd7
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: f77eab4c4326d7e6a277f8e23a0232402731913a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleSignIn: 5651ce3a61e56ca864160e79b484cd9ed3f49b7a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: 0ff230db599948a9ad7470ca667337803b3fc4dd
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  hermes-engine: 7fe5fc6ef707b7fdcb161b63898ec500e285653d
  InputMask: 71d291dc54d2deaeac6512afb6ec2304228c0bb7
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 0080d0a6ebf2577475bda044aa59e2ca1f909cda
  RCTRequired: 21229f84411088e5d8538f21212de49e46cc83e2
  RCTTypeSafety: 62eed57a32924b09edaaf170a548d1fc96223086
  React: f0254ccddeeef1defe66c6b1bb9133a4f040792b
  React-bridging: e46911666b7ec19538a620a221d6396cd293d687
  React-callinvoker: 66b62e2c34546546b2f21ab0b7670346410a2b53
  React-Codegen: b6999435966df3bdf82afa3f319ba0d6f9a8532a
  React-Core: dabbc9d1fe0a11d884e6ee1599789cf8eb1058a5
  React-CoreModules: 5b6b7668f156f73a56420df9ec68ca2ec8f2e818
  React-cxxreact: c7ca2baee46db22a30fce9e639277add3c3f6ad1
  React-hermes: c93e1d759ad5560dfea54d233013d7d2c725c286
  React-jsi: a565dcb49130ed20877a9bb1105ffeecbb93d02d
  React-jsiexecutor: 31564fa6912459921568e8b0e49024285a4d584b
  React-jsinspector: badd81696361249893a80477983e697aab3c1a34
  React-logger: fdda34dd285bdb0232e059b19d9606fa0ec3bb9c
  react-native-fbsdk-next: 16e652b5f1236492631185c58ec2ae15b029119d
  react-native-geolocation: ef66fb798d96284c6043f0b16c15d9d1d4955db4
  react-native-get-random-values: 384787fd76976f5aec9465aff6fa9e9129af1e74
  react-native-google-maps: e63e064f77df4af32655d05fbcd2f1ad1078db4a
  react-native-image-picker: 60f4246eb5bb7187fc15638a8c1f13abd3820695
  react-native-maps: a5f431a3eaaef69f2e6f8d39901fb3f2e9de931e
  react-native-pager-view: c29d484f19c49ff19525a94105e4ab2c4d4ae273
  react-native-safe-area-context: d1c8161a1e9560f7066e8926a7d825eb57c5dab5
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  react-native-text-input-mask: a273f7823ac4cb7189ea96873fd7b4a48b8aaaaa
  React-perflogger: e68d3795cf5d247a0379735cbac7309adf2fb931
  React-RCTActionSheet: 05452c3b281edb27850253db13ecd4c5a65bc247
  React-RCTAnimation: 578eebac706428e68466118e84aeacf3a282b4da
  React-RCTBlob: f47a0aa61e7d1fb1a0e13da832b0da934939d71a
  React-RCTImage: 60f54b66eed65d86b6dffaf4733d09161d44929d
  React-RCTLinking: 91073205aeec4b29450ca79b709277319368ac9e
  React-RCTNetwork: ca91f2c9465a7e335c8a5fae731fd7f10572213b
  React-RCTSettings: 1a9a5d01337d55c18168c1abe0f4a589167d134a
  React-RCTText: c591e8bd9347a294d8416357ca12d779afec01d5
  React-RCTVibration: 8e5c8c5d17af641f306d7380d8d0fe9b3c142c48
  React-runtimeexecutor: 7401c4a40f8728fd89df4a56104541b760876117
  ReactCommon: c9246996e73bf75a2c6c3ff15f1e16707cdc2da9
  RNAppleAuthentication: e00c76acb03351f5544373c78fa7f359bef6d5d3
  RNCAsyncStorage: 618d03a5f52fbccb3d7010076bc54712844c18ef
  RNCCheckbox: a3ca9978cb0846b981d28da4e9914bd437403d77
  RNCPicker: b18aaf30df596e9b1738e7c1f9ee55402a229dca
  RNDateTimePicker: fc2e4f2795877d45e84d85659bebe627eba5c931
  RNDeviceInfo: db5c64a060e66e5db3102d041ebe3ef307a85120
  RNFBApp: 30fc7cf195759d82d0ae77f7d586694cd97ba218
  RNFBAuth: 12af0b8e33f1adbd736e9137b5f911c21ef9734b
  RNGestureHandler: 4279931048471f17faf46d912b2e96e10f1da99b
  RNGoogleSignin: 81521697b2c8f97f9a586ac7257b1a1d9b51b115
  RNPermissions: 128c79617d4c98e2272cf44be8e68a445f1c5e37
  RNReanimated: bec7736122a268883bdede07f1bf9cf4b40158db
  RNScreens: fa9b582d85ae5d62c91c66003b5278458fed7aaa
  RNSVG: d00c8f91c3cbf6d476451313a18f04d220d4f396
  RNVectorIcons: fcc2f6cb32f5735b586e66d14103a74ce6ad61f8
  Yoga: eca980a5771bf114c41a754098cd85e6e0d90ed7

PODFILE CHECKSUM: fb5473385756ad4d467af482d508d449731e9821

COCOAPODS: 1.12.1

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIAppFonts</key>
	<array>
		<string>Quicksand-VariableFont_wght.ttf</string>
		<string>Quicksand-Bold.ttf</string>
		<string>Quicksand-Light.ttf</string>
		<string>Quicksand-Medium.ttf</string>
		<string>Quicksand-Regular.ttf</string>
		<string>Quicksand-SemiBold.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
	</array>
	<key>NSPhotoLibraryUsageDescription</key>
    <string>$(PRODUCT_NAME) would like to access your photo library to upload photos and videos.</string>
    <key>NSCameraUsageDescription</key>
    <string>$(PRODUCT_NAME) request access to your device's camera to enable you to upload profile photos and business photos in app. This feature allows you to personalize your profile and showcase your business in a visually appealing manner.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>$(PRODUCT_NAME) needs access to your Microphone to record video with audio.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>$(PRODUCT_NAME) would like to access your location to display exclusive deals near you.</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>$(PRODUCT_NAME) would like to access your location to display exclusive deals near you.</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>$(PRODUCT_NAME) would like to access your location to display exclusive deals near you.</string>
    <key>NSAppleMusicUsageDescription</key>
    <string>$(PRODUCT_NAME) needs access to your Apple Music.</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>FacebookDisplayName</key>
	<string>SYPnSAVE</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookAppID</key>
	<string>754509512299378</string>
	<key>CFBundleDisplayName</key>
	<string>SYPnSAVE</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.740702161397-hnhlreer7o5ohroitoaih66rn777vfr6</string>
				<string>fb754509512299378</string>
			</array>
		</dict>
	</array>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
</dict>
</plist>

# Override Firebase SDK Version
$FirebaseSDKVersion = '10.7.0'
$RNFirebaseAsStaticFramework = true

require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

# Set deployment target for the project
platform :ios, '14.0'
install! 'cocoapods', 
  :deterministic_uuids => false

# Define static frameworks
$static_framework = [
  'react-native-maps',
  'react-native-google-maps',
  'Google-Maps-iOS-Utils',
  'GoogleMaps',
  'Firebase'
]

target 'SYPnSAVE' do
  use_frameworks! :linkage => :static

  # Maps dependencies with version constraints
  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-google-maps', :path => rn_maps_path
  pod 'GoogleMaps', '~> 7.0'
  pod 'Google-Maps-iOS-Utils', '~> 4.1'

  config = use_native_modules!
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => true,
    :fabric_enabled => flags[:fabric_enabled],
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'SYPnSAVETests' do
    inherit! :complete
  end

  pre_install do |installer|
    Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}
    installer.pod_targets.each do |pod|
      if $static_framework.include?(pod.name)
        def pod.build_type
          Pod::BuildType.static_library
        end
      end
    end
  end

  post_install do |installer|
    react_native_post_install(
      installer,
      :mac_catalyst_enabled => false
    )

    # Optimize bitcode stripping
    bitcode_strip_path = `xcrun --find bitcode_strip`.strip
    framework_paths = [
      "Pods/LogRocket/LogRocket.xcframework/ios-arm64/LogRocket.framework/LogRocket",
      "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64/hermes.framework/hermes",
      "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64_x86_64-maccatalyst/hermes.framework/hermes"
    ]

    framework_paths.each do |path|
      if File.exist?(File.join(Dir.pwd, path))
        system("#{bitcode_strip_path} #{File.join(Dir.pwd, path)} -r -o #{File.join(Dir.pwd, path)}")
      end
    end

    # Set deployment target for all pods
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'
      end
    end
    
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end

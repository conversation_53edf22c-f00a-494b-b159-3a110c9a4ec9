module.exports = {
	root: true,
	plugins: ['react', 'react-native', 'prettier'],
	extends: ['@react-native/eslint-config', 'plugin:prettier/recommended'],
	rules: {
		'react-hooks/exhaustive-deps': 'off',
		'react-native/no-inline-styles': 'off',
		'react/no-unstable-nested-components': 'off',
		'no-unused-vars': [
			'error',
			{
				argsIgnorePattern: '^_',
				varsIgnorePattern: '^_', // Ignore variables starting with "_"
			},
		],
	},
};

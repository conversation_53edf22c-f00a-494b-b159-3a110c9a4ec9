{"name": "sypnsave", "version": "0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios --simulator \"iPhone 16 Pro\"", "start": "react-native start", "test": "jest", "lint": "eslint . --ext js,tsx --report-unused-disable-directives --max-warnings 0", "postinstall": "react-native setup-ios-permissions && pod-install && patch-package && husky install", "prepare": "husky install", "format": "npx prettier --write './**/*.js' './**/*.jsx' './**/*.css' './**/*.json'"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.4.0", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "^1.17.11", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/datetimepicker": "^7.6.1", "@react-native-community/geolocation": "^3.0.4", "@react-native-firebase/app": "^16.5.0", "@react-native-firebase/auth": "^16.5.0", "@react-native-google-signin/google-signin": "^8.2.1", "@react-native-picker/picker": "^2.6.1", "@react-navigation/bottom-tabs": "^6.4.0", "@react-navigation/native": "^6.0.13", "@react-navigation/stack": "^6.3.4", "@reduxjs/toolkit": "^1.9.0", "axios": "^1.2.1", "constants": "^0.0.2", "dayjs": "^1.11.7", "hermes-engine": "^0.11.0", "lodash": "^4.17.21", "native-base": "^3.4.25", "react": "18.1.0", "react-dom": "^18.2.0", "react-hook-form": "^7.39.5", "react-native": "0.70.5", "react-native-chart-kit": "^6.12.0", "react-native-confirmation-code-field": "^7.3.1", "react-native-device-info": "^10.3.0", "react-native-fbsdk-next": "^11.1.0", "react-native-gesture-handler": "^2.8.0", "react-native-get-random-values": "^1.8.0", "react-native-image-picker": "^4.10.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-maps": "^1.3.2", "react-native-modal-datetime-picker": "^17.1.0", "react-native-pager-view": "^6.1.2", "react-native-permissions": "^3.8.0", "react-native-picker-select": "^9.0.0", "react-native-reanimated": "^2.13.0", "react-native-safe-area-context": "^4.4.1", "react-native-screens": "^3.18.2", "react-native-snap-carousel": "4.0.0-beta.6", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^13.6.0", "react-native-swipe-list-view": "^3.2.9", "react-native-tab-view": "^3.3.0", "react-native-text-input-mask": "^3.2.0", "react-native-vector-icons": "^9.2.0", "react-redux": "^8.0.5", "redux-persist": "^6.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@react-native/eslint-config": "^0.74.0", "babel-jest": "^26.6.3", "eslint": "^8.55.0", "eslint-plugin-ft-flow": "^3.0.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-native": "^4.1.0", "husky": "^8.0.0", "jest": "^26.6.3", "metro-react-native-babel-preset": "0.72.3", "patch-package": "^7.0.2", "pod-install": "^0.2.2", "prettier": "^3.1.1", "react-native-asset": "^2.0.1", "react-test-renderer": "18.1.0"}, "reactNativePermissionsIOS": ["AppTrackingTransparency", "Camera", "LocationAccuracy", "LocationAlways", "LocationWhenInUse", "MediaLibrary", "Microphone", "Notifications", "PhotoLibrary", "PhotoLibraryAddOnly", "StoreKit"], "jest": {"preset": "react-native"}, "lint-staged": {"src/**/*.{js,jsx}": "eslint --cache --fix", "src/**/*.{js,jsx,css,md}": "prettier --write --ignore-unknown"}}